stages:
  - build_staging
  - deploy_staging

variables:
  APP_DEPLOY_PATH: "/home/<USER>/htdocs/lds.admin.staging.legalyard.com"
  PACKAGE_JSON_FILE: "package.json"

before_script:
  - export CI=false
  - echo $SSH_USER_SRV487149 "$SSH_USER_SRV487149"
  - which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )
  - eval $(ssh-agent -s)
  - echo -n "$SSH_PRIVATE_KEY_SRV487149" | tr -d '\r' | ssh-add -
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - ssh-keyscan $SSH_HOST_SRV487149 >> ~/.ssh/known_hosts

build_staging:
  stage: build_staging
  image: node:20.11.1
  script:
    - npm install
    - npm run build:staging
  artifacts:
    paths:
      - build
    expire_in: "2 days"
  only:
    - release/staging

deploy_staging:
  stage: deploy_staging
  script:
    - scp -r build/* $SSH_USER_SRV487149@$SSH_HOST_SRV487149:$APP_DEPLOY_PATH
  only:
    - release/staging
