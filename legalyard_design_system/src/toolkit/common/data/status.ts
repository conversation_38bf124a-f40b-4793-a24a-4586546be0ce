export const statusMessageOld = {
  requested:
    "The user has submitted a service request, and it is awaiting assignment to an partner.",
  assigned:
    "The service request has been assigned to an partner who will be responsible for working on it.",
  inProgress:
    "The partner has started working on the service request and is actively engaged in providing the requested services.",
  pendingReview:
    "The partner has completed their work, and the service request is pending review by the user or an internal reviewer.",
  completed:
    "The service request has been reviewed and considered complete. All necessary tasks related to the request have been fulfilled.",
  rejected:
    "The service request has been rejected, typically due to an inability to provide the requested service or other specific reasons.",
  onHold:
    "The service request is temporarily put on hold, usually due to a need for additional information, clarification, or external dependencies.",
  cancelled:
    "The service request has been cancelled either by the user or by the system/administrator.",
};

export const statusMessageNew = {
  requested: "You requested, & awaiting expert.",
  assigned:
    "The service request has been assigned to an partner who will be responsible for working on it.",
  inProgress:
    "The partner has started working on the service request and is actively engaged in providing the requested services.",
  pendingReview:
    "The partner has completed their work, and the service request is pending review by the user or an internal reviewer.",
  completed:
    "The service request has been reviewed and considered complete. All necessary tasks related to the request have been fulfilled.",
  rejected:
    "The service request has been rejected, typically due to an inability to provide the requested service or other specific reasons.",
  onHold:
    "The service request is temporarily put on hold, usually due to a need for additional information, clarification, or external dependencies.",
  cancelled:
    "The service request has been cancelled either by the user or by the system/administrator.",
};

export const statusMessage = statusMessageNew;
