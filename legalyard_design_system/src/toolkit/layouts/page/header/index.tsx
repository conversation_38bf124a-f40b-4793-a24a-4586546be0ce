import useScrollDirection from "../../../utils/hooks/useScrollDirection";
import classNames from "classnames";
import { FC, ReactNode, RefObject, useRef } from "react";

interface propsType {
  className?: string;
  hideEffect?: boolean;
  parentRef?: RefObject<HTMLElement>;
  Breadcrumb?: ReactNode;
  MainAction?: ReactNode;
  SubActions?: ReactNode;
  SubHeader?: ReactNode;
}

const PageHeader: FC<propsType> = ({
  className,
  hideEffect,
  parentRef,
  Breadcrumb,
  MainAction,
  SubActions,
  SubHeader,
}) => {
  const boxRef = useRef<HTMLDivElement>(null);
  const scrollDirection = useScrollDirection({ ref: parentRef });

  return (
    <div
      ref={boxRef}
      aria-label="page header"
      className={classNames(
        "sticky shadow z-30 transition-all duration-500",
        hideEffect && scrollDirection === "down" ? "-top-24" : "top-0",
        className
      )}
    >
      <div className="bg-page flex items-center justify-between gap-2 border-b min-h-[39px] border-slate-200 px-3 py-1">
        {Breadcrumb}
        <div className="flex items-center justify-between gap-x-1">
          {MainAction}
          {SubActions}
        </div>
      </div>
      {SubHeader && (
        <div className="relative bg-white px-3 py-1">{SubHeader}</div>
      )}
    </div>
  );
};

export default PageHeader;
