import classNames from "classnames";
import { FC, InputHTMLAttributes, ReactNode } from "react";

interface propsType {
  children?: ReactNode;
  fluid?: boolean;
}

const AppContainer: FC<propsType & InputHTMLAttributes<HTMLDivElement>> = ({
  children,
  fluid,
  ...rest
}) => {
  return (
    <div
      aria-label="App Container"
      className={classNames(
        "w-full px-4 mx-auto",
        !fluid &&
          "sm:max-w-screen-sm md:max-w-screen-md lg:max-w-screen-lg xl:max-w-screen-xl 2xl:max-w-screen-2xl"
      )}
      {...rest}
    >
      {children}
    </div>
  );
};

export default AppContainer;
