import React from "react";
import Dropdown from "../../../../../../ui/dropdown";
import { Settings } from "react-feather";
import Badge from "../badge";

import * as Styles from "./styles";

const Setting = () => {
  return (
    <div className="relative">
      <Dropdown
        inlineAlign="left"
        content={
          <div className="p-5 w-72 max-w-72 max-h-80 overflow-auto">
            Settings...
          </div>
        }
      >
        <div className="relative">
          <Styles.IconWrapper>
            <Badge />
            <Settings />
          </Styles.IconWrapper>
        </div>
      </Dropdown>
    </div>
  );
};

export default Setting;
