import classNames from "classnames";
import {
  ChangeEvent,
  Dispatch,
  KeyboardEvent,
  ReactNode,
  SetStateAction,
  useEffect,
  useRef,
} from "react";
import { ChevronUp, Command, Search } from "react-feather";
import InputField from "../../../../ui/input/field";

const SearchInput = ({
  searchValue,
  setSearchValue,
  setFocused,
}: {
  searchValue: string;
  setSearchValue: Dispatch<SetStateAction<string>>;
  setFocused: Dispatch<SetStateAction<boolean>>;
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleFocused = () => {
    try {
      setFocused((prev) => !prev);
      setTimeout(() => {
        if (inputRef.current === document.activeElement) {
          inputRef?.current?.blur();
        } else {
          inputRef?.current?.focus();
        }
      }, 1);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const handleKeyDown: EventListener = (event) => {
      try {
        const e = event as unknown as KeyboardEvent;
        if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
          e.preventDefault();
          handleFocused();
        }
      } catch (error) {
        console.error(error);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  const shortcutIcon = (showNames?: boolean): ReactNode => {
    try {
      const platform = navigator.platform.toLowerCase();
      const isMac = platform.includes("mac");
      const isWin = platform.includes("win");
      const isOthers = /linux|android/.test(platform);

      const mainKey = (
        <span className="flex items-center">
          {isMac ? (
            <>
              <Command />
              {showNames && "Cmd + "}
            </>
          ) : isWin ? (
            <>
              <ChevronUp />
              {showNames && "Ctrl + "}
            </>
          ) : isOthers ? (
            <>
              <ChevronUp />
              {showNames && "Ctrl + "}
            </>
          ) : (
            ""
          )}
        </span>
      );

      return (
        <span
          className={classNames(
            "flex items-center gap-0.5  [&_svg]:w-3.5 [&_svg]:h-3.5 whitespace-nowrap",
            showNames ? "text-white" : "text-slate-400",
            showNames ? "text-sm font-bold" : "text-sb"
          )}
        >
          {mainKey} K
        </span>
      );
    } catch {
      return "";
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      setSearchValue(e?.target?.value ?? "");
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <InputField
      ref={inputRef}
      type="text"
      name="search"
      placeholder="Search"
      onChange={(e) => handleChange(e)}
      value={searchValue || ""}
      onFocus={setFocused}
      autoComplete="off"
      attributes={{
        prefix: {
          icon: {
            src: <Search />,
          },
        },
        suffix: {
          icon: {
            src: shortcutIcon(),
            tooltip: {
              show: true,
              name: shortcutIcon(true),
              direction: "bottom",
            },
          },
        },
      }}
    />
  );
};

export default SearchInput;
