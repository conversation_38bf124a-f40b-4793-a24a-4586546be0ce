import styled from "styled-components";

export const Container = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  padding: 20px 0;
  gap: 20px;
  width: 100%;
  border-bottom: 1px solid ${({ theme: { color } }) => color.main};
`;

export const ProfileContainer = styled.div`
  position: relative;
  cursor: pointer;
`;

export const ImageWrapper = styled.div`
  border-radius: 100px;
  overflow: hidden;
  border: 2px solid
    ${({ $active, theme: { color } }) => ($active ? color.main : color.grey)};
  width: 60px;
  height: 60px;
  cursor: pointer;
`;

export const ImageHolder = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

export const TextWrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  flex: 1;

  svg {
    width: 22px;
    height: 22px;
  }
`;

export const NameHolder = styled.h3`
  font-weight: 600;
  font-size: ${({ theme: { font } }) => font?.title};
`;
