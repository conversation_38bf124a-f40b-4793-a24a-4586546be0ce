import { User } from "react-feather";
import PageDimensions from "../../../../styles/usePageDimensions";
import * as Styles from "./styles";

const MobileGuestMenu = () => {
  const isPartner = true;
  const { pageWidth } = PageDimensions();

  return (
    <Styles.Container>
      {pageWidth?.sm_mobile && (
        <Styles.ItemHolder>
          <User />
          <Styles.TextHolder>
            <a href="/auth">Log in</a>
          </Styles.TextHolder>
        </Styles.ItemHolder>
      )}
      <>
        {isPartner ? (
          <Styles.Button onClick={() => {}}>Sign Up Now</Styles.Button>
        ) : (
          <Styles.Button onClick={() => {}}>Partner with us</Styles.Button>
        )}
      </>
    </Styles.Container>
  );
};

export default MobileGuestMenu;
