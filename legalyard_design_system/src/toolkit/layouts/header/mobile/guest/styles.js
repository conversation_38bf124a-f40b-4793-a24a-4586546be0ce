import styled from "styled-components";

export const Container = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flex};
  align-items: flex-start;
  flex-direction: column;
  gap: 30px;
`;

export const ItemHolder = styled.div`
  cursor: pointer;
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  gap: 5px;

  a {
    padding: 10px 0;
  }
  color: ${({ theme: { color } }) => color.grey};
  font-weight: 600;
  text-transform: capitalize;

  ${({ theme: { placeholders } }) => placeholders.bottomBorder};

  svg {
    width: 18px;
    height: 18px;
  }
`;

export const TextHolder = styled.span`
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
  white-space: nowrap;
`;

export const Button = styled.button`
  ${({ theme: { placeholders } }) => placeholders.button};

  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
  white-space: nowrap;
`;
