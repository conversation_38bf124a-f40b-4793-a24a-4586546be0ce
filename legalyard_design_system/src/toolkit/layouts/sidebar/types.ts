import { Dispatch, ReactNode, SetStateAction } from "react";
import { Icon } from "react-feather";

export interface MenuItemType {
  id?: string;
  name: string;
  path?: string;
  icon?: string | ReactNode | Icon;
  externalUrl?: boolean;
  openInNewTab?: boolean;
  menu?: MenuItemType[];
}

export interface MenuDataEntryType {
  title?: string;
  menu?: MenuItemType[];
}

export interface MenuDataType {
  allow?: string[];
  data?: MenuDataEntryType[];
}

export interface DashboardSidebarType {
  data: MenuDataType[];
  pathname?: string;
  onNavigate?: (path?: string, options?: any) => void;
  setSidebarOpen: Dispatch<SetStateAction<boolean>>;
  sidebarOpen: boolean;
}
