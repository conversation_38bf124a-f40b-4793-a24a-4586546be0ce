import styled from "styled-components";
import { StyledType } from "./types";

export const ItemWrapper = styled.div<StyledType>`
  display: flex;
  align-items: center;
  gap: 0.3rem;

  &:not(:last-of-type) {
    cursor: ${({ $nav }: any) => ($nav ? "pointer" : "auto")};

    &:hover {
      color: ${({ $nav, theme: { color } }: any) =>
        $nav ? color?.main : "inherit"};
    }
  }

  svg {
    width: 0.95rem;
    height: 0.95rem;
  }

  &:last-of-type {
    span {
      font-weight: 600;
    }
  }
`;

export const TextHolder = styled.span`
  font-weight: 400;
`;
