import { color } from "../../../../../../styles/variables";
import Image from "../../../../../image";
import { getRandomColor } from "../../../../../../utils/helpers/data/color";
import classNames from "classnames";
import Placeholder from "../../../../../..//assets/placeholder/1.png";

const FeatureCard = () => {
  const localBg = getRandomColor();

  const colorKey = Object.keys(color).find(
    (key: string) => color[key] === localBg
  );

  return (
    <div
      aria-label="feature-card"
      className="bg-white rounded-base text-center overflow-hidden shadow-lg last-of-type:justify-self-stretch"
    >
      <style
        dangerouslySetInnerHTML={{
          __html: `
            #intro-feature-card-fold-${colorKey}::before {
              background: ${color["gray"]};
              border-color: ${color[colorKey ?? "#ffffff"]}99;
            }
          `,
        }}
      />
      <div
        id={`intro-feature-card-fold-${colorKey}`}
        className={classNames(
          "w-full h-full backdrop-blur-sm rounded-sm px-5 py-7 fold hover:fold-none"
        )}
        style={{ background: localBg }}
      >
        <Image
          src={Placeholder}
          className="mb-5 w-full h-auto max-h-32 object-contain mx-auto text-white"
        />
        <h5 className={classNames("font-bold text-xl mb-3 text-white")}>
          Project Management
        </h5>
        <p className="text-sb leading-5 text-white">
          With lots of unique blocks, you can easily build a page without
          coding, build your next landing page
        </p>
      </div>
    </div>
  );
};

export default FeatureCard;
