import { FC, lazy } from "react";
import { propsType } from "./types";

const IntroTitle = lazy(() => import("./components/title"));
const IntroVideo = lazy(() => import("./components/video"));
const IntroFeatures = lazy(() => import("./components/features"));
const IntroPricing = lazy(() => import("./components/pricing"));
const IntroBoard = lazy(() => import("./components/board"));
const IntroOtherApps = lazy(() => import("./components/others"));

const IntroductionLanding: FC<propsType> = (props) => {
  return (
    <div aria-label="Introduction">
      <IntroTitle {...props} />
      <div className="max-w-full md:max-w-[90%] lg:max-w-[75%] mx-auto">
        <IntroVideo {...props} />
        <IntroFeatures />
        <IntroPricing />
        <IntroBoard />
        <IntroOtherApps />
      </div>
    </div>
  );
};

export default IntroductionLanding;
