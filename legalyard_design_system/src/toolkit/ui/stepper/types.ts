import { ComponentType, LazyExoticComponent, ReactNode } from "react";

export interface dataType {
  title?: string;
  sub?: string;
  component?: ReactNode | LazyExoticComponent<ComponentType<any>>;
}

export interface propsType {
  data?: dataType[];
  currentActive: number;
  lastCompleted?: string;
  justify?: boolean;
  vertical?: boolean;
  className?: string;
  wrapperClass?: string;
  hideCheck?: boolean;
}

export interface styles {
  $justify?: boolean;
  $sub?: boolean;
  $complete?: boolean;
  $active?: boolean;
  $vertical?: boolean;
}

export type StyledType = styles;
