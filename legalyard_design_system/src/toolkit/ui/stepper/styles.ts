import styled, { css } from "styled-components";
import { StyledType } from "./types";

export const Container = styled.div`
  background-color: transparent;
  margin-inline: auto;
  height: 100%;
`;

const verticalWrapper = css`
  flex-direction: column;
`;

export const StepsWrapper = styled.div<StyledType>`
  height: 100%;
  white-space: nowrap;

  ${({ theme: { placeholders } }) => placeholders.flex};
  align-items: ${({ $vertical }) => ($vertical ? "flex-start" : "center")};
  ${({ $vertical }) => $vertical && verticalWrapper};

  justify-content: ${({ $justify }) =>
    $justify ? "space-between" : "flex-start"};
  gap: 10px;
  overflow-x: auto;
  padding-bottom: 0.25rem;
  margin-bottom: 0.24rem;
`;

export const StepHolder = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};

  ${({ theme: { media } }) => `
    ${media.mobile}{
     flex-direction: column;
    }
  `}

  gap: 0.5rem;
  max-width: fit-content;
  max-height: fit-content;
`;

export const NumberWrapper = styled.div<StyledType>`
  background-color: ${({ $active, $complete, theme: { color } }) =>
    $complete ? color.green : $active ? color.main : `${color.grey}30`};
  color: ${({ $active, theme: { color } }) =>
    $active ? color.white : color.font};
  border-radius: 100px;
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  font-size: ${({ theme: { font } }) => font.sub};
  justify-content: center;
  font-weight: 600;
  width: 1.5rem;
  height: 1.5rem;
  min-width: 1.5rem;
  min-height: 1.5rem;
  max-width: 1.5rem;
  max-height: 1.5rem;

  svg {
    width: 1rem;
    height: 1rem;
    color: ${({ theme: { color } }) => color.white};
    stroke-width: 3px;
  }
`;

export const TextWrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flex};
  flex-direction: column;
  gap: 5px;
`;

export const TextHolder = styled.p<StyledType>`
  font-size: ${({ $sub, theme: { font } }) =>
    $sub ? font.extraSmall : font.sub};
  font-weight: ${({ $sub }) => ($sub ? 400 : 600)};
  text-transform: ${({ $sub }) => ($sub ? "none" : "capitalize")};

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const verticalDivider = css`
  border-left-style: solid;
  border-left-width: 1px;
  margin-left: 0.75rem;
  min-height: 1rem;
`;

const horizontalDivider = css`
  border-top-style: solid;
  border-top-width: 1px;
  min-width: 1rem;
`;

export const StepDivider = styled.span<StyledType>`
  flex: 1;
  display: block;
  border-color: ${({ theme: { color } }) => color.grey};

  ${({ $vertical }) => ($vertical ? verticalDivider : horizontalDivider)};
`;
