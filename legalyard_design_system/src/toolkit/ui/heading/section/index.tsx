import { FC } from "react";
import { propsType } from "../types";
import classNames from "classnames";
import Button from "../../button";

const SectionHeading: FC<propsType> = ({
  className,
  title,
  sub,
  button,
  handleButton,
}) => {
  const buttonCLick = () => {
    if (button && handleButton) {
      handleButton();
    }
  };
  return (
    <div
      className={classNames(
        "section_heading flex items-start justify-between gap-4 mb-1",
        className
      )}
    >
      <div className="flex items-center flex-col item-start gap-2">
        <span className="text-base text-font font-bold capitalize flex-1">
          {title}
        </span>
        {sub && (
          <span className="text-xs text-gray font-normal flex-1">{sub}</span>
        )}
      </div>
      {button && <Button onClick={() => buttonCLick()}>{button}</Button>}
    </div>
  );
};

export default SectionHeading;
