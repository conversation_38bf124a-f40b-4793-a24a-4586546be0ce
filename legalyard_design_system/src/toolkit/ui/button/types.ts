import { ButtonHTMLAttributes, ReactNode } from "react";

export type AllowedVariantsType =
  | "success"
  | "error"
  | "warning"
  | "ghost"
  | "action"
  | "outline"
  | "dark";

export type AllowedSize = "full" | "half";

export interface ButtonType extends ButtonHTMLAttributes<HTMLButtonElement> {
  children?: ReactNode;
  className?: string;
  loading?: boolean;
  effect?: boolean;
  variant?: AllowedVariantsType;
  size?: AllowedSize;
  rightArrow?: boolean;
}
