import useOutsideAlert from "../../utils/hooks/useOutsideAlert";
import classNames from "classnames";
import { FC, MouseEvent, useEffect, useRef, useState } from "react";
import { X } from "react-feather";
import { propsType } from "./types";

const Modal: FC<propsType> = ({
  id,
  children,
  open,
  close,
  onClose,
  outsideClose,
  className,
  wrapperClassName,
  contentClassName,
}) => {
  const modalRef = useRef(null);
  const contentRef = useRef(null);

  const [display, setDisplay] = useState(false);

  const handleClose = (e: MouseEvent<HTMLButtonElement | HTMLDivElement>) => {
    try {
      e?.stopPropagation();
      e?.preventDefault();

      if (onClose) {
        onClose(false);
      }

      return false;
    } catch (error) {
      console.error(error);
    }
  };

  const handleOutsideClose = (e: MouseEvent<HTMLDivElement>) => {
    try {
      e?.stopPropagation();
      e?.preventDefault();

      if (outsideClose) {
        handleClose(e);
        if (onClose) {
          onClose(false);
        }
      }

      return false;
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (open) {
      setDisplay(true);
    } else {
      setTimeout(() => {
        setDisplay(false);
      }, 100);
    }
  }, [open]);

  useOutsideAlert(contentRef, handleOutsideClose);

  return (
    <>
      {display && (
        <div
          ref={modalRef}
          id={id ?? "modal"}
          role="button"
          aria-label="Modal"
          onClick={(e) => e?.stopPropagation()}
          className={classNames(
            "modal fixed top-0 right-0 bottom-0 left-0 z-40 overflow-auto cursor-auto w-screen h-screen",
            "before:bg-grey before:bg-opacity-70 before:backdrop-blur-sm before:w-screen before:h-screen before:fixed before:top-0 before:right-0 before:bottom-0 before:left-0 before:outline-0 before:opacity-0 before:transition-[opacity]",
            open && "before:!opacity-100",
            className
          )}
        >
          <div
            ref={contentRef}
            className={classNames(
              "bg-white absolute top-0 right-0 bottom-0 left-0 rounded-base w-fit h-fit m-auto shadow-lg transition-[transform] -translate-y-[10%]",
              open && "!translate-y-[0%]",
              wrapperClassName
            )}
          >
            {close && (
              <button
                className="close_icon z-10 absolute top-2.5 right-2.5 cursor-pointer border-0 rounded-base hover:bg-red hover:bg-opacity-50 flex items-center justify-center"
                onClick={(e) => handleClose(e)}
              >
                <X />
              </button>
            )}

            <div
              className={classNames("relative h-full w-full", contentClassName)}
            >
              {children}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Modal;
