import { useEffect } from "react";
import { Swiper, SwiperSlide, useSwiper } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

import * as Styles from "./styles";

const SwiperCarousel = () => {
  const swiper = useSwiper();
  // useEffect(() => {
  //   const swiper = new Swiper(".swiper-container", {
  //     modules: [Navigation, Pagination],
  //     direction: "horizontal",
  //     loop: true,
  //     autoplay: {
  //       delay: 3000,
  //     },
  //     pagination: {
  //       el: ".swiper-pagination",
  //       clickable: true,
  //     },
  //   });

  //   return () => {
  //     swiper.destroy();
  //   };
  // }, []);

  return (
    <>
      <Swiper
        spaceBetween={50}
        slidesPerView={3}
        // onSlideChange={() => {}}
        // onSwiper={(swiper) => {}}
      >
        <SwiperSlide>Slide 1</SwiperSlide>
        <SwiperSlide>Slide 2</SwiperSlide>
        <SwiperSlide>Slide 3</SwiperSlide>
        <SwiperSlide>Slide 4</SwiperSlide>
        ...
      </Swiper>
      <button onClick={() => swiper.slideNext()}>
        Slide to the next slide
      </button>
    </>
  );
};

export default SwiperCarousel;
