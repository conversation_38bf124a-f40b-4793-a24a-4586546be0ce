import classNames from "classnames";
import { cloneElement, FC, Fragment, isValidElement, Suspense } from "react";
import { isLazyExoticComponent } from "../../../../../utils";
import Loader from "../../../../loader";
import { TabsType } from "../../types";

interface PropsType extends TabsType {
  activeTabIndex: number | null;
}

const TabContent: FC<PropsType> = ({
  data,
  activeTabIndex,
  contentClassName,
  noContent,
  contentLoader,
}) => {
  if (noContent || !(Array.isArray(data) && data?.length > 0)) {
    return null;
  }

  return (
    <div
      className={classNames(
        "tabs-content relative bg-transparent",
        contentClassName
      )}
    >
      {data?.map((tab, index) => {
        const RenderComponent = tab?.content;
        if (activeTabIndex !== index) {
          return null;
        } else if (
          RenderComponent &&
          typeof RenderComponent !== "string" &&
          isValidElement(RenderComponent)
        ) {
          return (
            <Fragment key={index}>
              <Suspense fallback={contentLoader ?? <Loader center box big />}>
                {cloneElement(RenderComponent, tab?.props)}
              </Suspense>
            </Fragment>
          );
        } else if (RenderComponent && isLazyExoticComponent(RenderComponent)) {
          const LazyComponent = RenderComponent;
          return (
            <Fragment key={index}>
              <Suspense fallback={contentLoader ?? <Loader center box big />}>
                <LazyComponent {...tab?.props} />
              </Suspense>
            </Fragment>
          );
        } else if (RenderComponent && typeof RenderComponent === "string") {
          return (
            <Fragment key={index}>
              <Suspense fallback={contentLoader ?? <Loader center box big />}>
                {RenderComponent}
              </Suspense>
            </Fragment>
          );
        } else {
          return null;
        }
      })}
    </div>
  );
};

export default TabContent;
