import {
  ClassAttributes,
  ComponentType,
  FC,
  JSXElementConstructor,
  LazyExoticComponent,
  ReactElement,
  ReactNode,
} from "react";

export interface TabsDataType {
  name: string;
  content?:
    | string
    | ReactElement<any, string | JSXElementConstructor<any>>
    | LazyExoticComponent<ComponentType | FC>;
  props?: any;
}

export interface TabsType {
  defaultActive?: number;
  data: TabsDataType[];
  justify?: boolean;
  noContent?: boolean;
  tabClassName?: string | ClassAttributes<string>;
  variant?: "normal" | "underline";
  ghostTheme?: boolean;
  className?: string;
  contentClassName?: string;
  contentLoader?: ReactNode;
  onChange?: (active: number) => void;
}
