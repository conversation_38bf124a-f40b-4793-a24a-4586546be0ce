import { FC, useState } from "react";
import TabContent from "./components/content";
import TabsMenu from "./components/menu";
import { TabsType } from "./types";
import classNames from "classnames";

const Tabs: FC<TabsType> = ({ className, defaultActive, ...rest }) => {
  const [activeTabIndex, setActiveTabIndex] = useState<number | null>(
    defaultActive ?? null
  );

  return (
    <div className={classNames("tabs", className)}>
      <TabsMenu
        activeTabIndex={activeTabIndex}
        setActiveTabIndex={setActiveTabIndex}
        {...rest}
      />
      <TabContent activeTabIndex={activeTabIndex} {...rest} />
    </div>
  );
};

export default Tabs;
