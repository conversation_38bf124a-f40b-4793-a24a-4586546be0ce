import { FC } from "react";
import classNames from "classnames";
import { validArray } from "../../../utils/helpers/data/array";
import { propsType } from "../types";
import { v4 as uuid } from "uuid";
import Tag from "..";

const TagsList: FC<propsType> = ({ className, tagClass, data, ...rest }) => {
  return (
    <div
      className={classNames("flex items-center flex-wrap gap-x-0.5", className)}
    >
      {validArray(data) &&
        data?.map((tag: any, index: number) => {
          const unique_id = uuid();
          return (
            <Tag
              key={unique_id ?? tag?._id ?? index}
              {...rest}
              title={tag?.name ?? tag?.title}
              icon={tag?.icon}
              className={tagClass}
            />
          );
        })}
    </div>
  );
};

export default TagsList;
