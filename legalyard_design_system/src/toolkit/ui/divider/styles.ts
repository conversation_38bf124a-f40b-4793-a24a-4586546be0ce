import styled from "styled-components";

export const Container = styled.div`
  color: ${({ theme: { color } }) => color.gray};
  font-size: ${({ theme: { font } }) => font.sub};
  border-radius: ${({ theme: { element } }) => element.radius};
  width: 100%;

  font-weight: 600;
  text-align: center;
  margin: 1.5rem 0;
  padding: 4px 5px;
  position: relative;
  ${({ theme: { placeholders } }) => placeholders.flexCenter};

  &::before {
    content: "";
    flex: 1 1 10%;
    border-bottom: 1px solid rgb(193, 199, 198);
    margin: 0px 10px;
    margin-left: 0px;
    transform: translateY(-50%);
  }

  &::after {
    content: "";
    flex: 1 1 10%;
    border-bottom: 1px solid rgb(193, 199, 198);
    margin: 0px 10px;
    margin-right: 0px;
    transform: translateY(-50%);
  }
`;
