import TableLoader from "../../../../../../../loader/skeleton/table";
import { validArray } from "../../../../../../../../utils/helpers/data/array";
import Resizable from "../../../../../../../../utils/hooks/resizable";
import classNames from "classnames";
import { ArrowDown, ArrowUp, Lock } from "react-feather";
import useTable from "../../../@context";
import Styles from "../../../styles.module.css";
import RenderActions from "../common/action";
import RenderCheckbox from "../common/checkbox";
import SortMenu from "./sort-menu";

const TableColumns = () => {
  const {
    variables,
    helper: {
      handleColumnSort,
      handlePin,
      getActionPinnedWidth,
      getColumnCount,
      handleSelectAll,
    },
    state: { selectAll, tableScrollLeft, localLoading },
    columns: { data: columnData, loading: colLoading = false } = {},
    action,
    checkbox,
    loading,
  } = useTable();

  if (loading || localLoading || colLoading) {
    return (
      <tr>
        <th colSpan={getColumnCount()}>
          <TableLoader column={4} row={1} />
        </th>
      </tr>
    );
  }

  return (
    <tr className="group">
      {action && <RenderActions elementType="th" header />}
      {checkbox && (
        <RenderCheckbox
          elementType="th"
          header
          onChange={(e) => handleSelectAll(e)}
          isChecked={selectAll}
        />
      )}
      {validArray(columnData) &&
        columnData?.map((col, index) => {
          return (
            <Resizable
              key={index + col?.key}
              minWidth={col?.minWidth}
              onWidthChange={(width) => (col.width = width)}
            >
              {({ ref, width: slideWidth }) => {
                const initialWidth =
                  col?.width ?? col?.minWidth ?? variables.column.width;
                const finalWidth = !isNaN(parseInt(String(slideWidth)))
                  ? parseInt(String(slideWidth))
                  : initialWidth;

                return (
                  <th
                    {...(col?.thProps ?? {})}
                    id={col?.key}
                    className={classNames(
                      "table-data-column relative uppercase text-start py-2 px-4 bg-slate-100 hover:bg-slate-200",
                      col?.pin && "sticky z-[7]",
                      col?.thProps?.className
                    )}
                    style={{
                      width: finalWidth,
                      ...(col?.pin && { left: `${getActionPinnedWidth()}px` }),
                      ...(col?.thProps?.style ?? {}),
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <h3
                        title={col?.title}
                        className="flex-1 font-semibold text-ellipsis overflow-hidden whitespace-nowrap"
                      >
                        {col?.title}
                      </h3>

                      {col?.sortable && (
                        <div className="flex items-center">
                          <div className="flex items-center">
                            {col?.sortType && (
                              <span title={col?.sortType}>
                                {col?.sortType === "asc" ? (
                                  <ArrowUp className="w-3 h-3" />
                                ) : (
                                  <ArrowDown className="w-3 h-3" />
                                )}
                              </span>
                            )}
                            {col?.pin && (
                              <span title="pinned">
                                <Lock className="w-3 h-3 mr-1" />
                              </span>
                            )}
                          </div>

                          <SortMenu
                            active={col?.sortType || ""}
                            handleActive={(sortType: string) =>
                              handleColumnSort(
                                sortType,
                                col?.key,
                                validArray(col?.renderField)
                                  ? col?.renderField
                                  : col?.field
                              )
                            }
                            pinned={col?.pin ?? false}
                            handlePin={() =>
                              handlePin(col?.pin ? "unpin" : "pin", col?.key)
                            }
                          />
                        </div>
                      )}
                    </div>

                    {col?.resize ? (
                      <div
                        className={classNames(Styles.table_data_column_resizer)}
                        ref={ref}
                      />
                    ) : (
                      <div
                        className={classNames(
                          Styles.table_data_column_side_border
                        )}
                      />
                    )}
                    {col?.pin && (
                      <div
                        className={classNames(
                          Styles.table_data_column_side_show,
                          tableScrollLeft > 0 && "!visible"
                        )}
                      />
                    )}
                  </th>
                );
              }}
            </Resizable>
          );
        })}
    </tr>
  );
};

export default TableColumns;
