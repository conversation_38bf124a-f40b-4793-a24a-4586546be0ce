import classNames from "classnames";
import { MouseEvent } from "react";
import { validArray } from "../../../../../../../../utils/helpers/data/array";
import TableLoader from "../../../../../../../loader";
import useTable from "../../../@context";
import Styles from "../../../styles.module.css";
import { NavigationType } from "../../../types";
import RenderActions from "../common/action";
import RenderCheckbox from "../common/checkbox";
import DataIcon from "./icon";

const TableRows = ({ rows }: { rows: any[] }) => {
  const {
    helper: {
      getActionPinnedWidth,
      getColumnCount,
      handleSelect,
      handleNavigation,
      getFieldValue,
    },
    state: { searchValue, selected, tableScrollLeft, localLoading },
    columns,
    action,
    checkbox,
    rows: { loading: RowLoading = false } = {},
    navigation,
    loading,
  } = useTable();

  const textMatched = (text: string | number) => {
    try {
      return (
        searchValue &&
        text
          ?.toString()
          ?.toLowerCase()
          ?.includes(searchValue?.toString()?.toLowerCase())
      );
    } catch (error) {
      console.error(error);
      return false;
    }
  };

  const handleRowClick = (
    e: MouseEvent<HTMLDivElement>,
    row: any,
    nav?: NavigationType
  ) => {
    try {
      e?.stopPropagation();
      handleNavigation(row, nav);
    } catch (error) {
      console.error(error);
    }
  };

  const handleDataClick = (
    e: MouseEvent<HTMLDivElement>,
    row: any,
    nav?: NavigationType
  ) => {
    try {
      e?.stopPropagation();
      handleNavigation(row, nav);
    } catch (error) {
      console.error(error);
    }
  };

  if (loading || localLoading || RowLoading) {
    return (
      <tr>
        <td colSpan={getColumnCount()}>
          <TableLoader variant="table" column={4} row={3} />
        </td>
      </tr>
    );
  }

  return (
    <>
      {validArray(rows) &&
        rows?.map((row, rowIndex) => {
          const randomId = Math.random();
          return (
            <tr
              key={rowIndex * randomId}
              className={classNames(
                "group leading-normal border-b border-r last:border-0 border-solid border-slate-100 hover:bg-slate-100 cursor-pointer"
              )}
            >
              {action && <RenderActions row={row} />}
              {checkbox && (
                <RenderCheckbox
                  row={row}
                  onChange={() => handleSelect(row)}
                  isChecked={selected?.includes(row?.rowId)}
                />
              )}
              {validArray(columns?.data) &&
                columns?.data?.map((col, index) => {
                  const renderValue = getFieldValue(
                    row,
                    validArray(col?.renderField) ? col?.renderField : col?.field
                  );

                  if (col?.component) {
                    const customComponent = col?.component({
                      row,
                      column: col,
                      rowIndex,
                    });

                    return <>{customComponent}</>;
                  }

                  return (
                    <td
                      {...(col?.tdProps ?? {})}
                      key={index * randomId}
                      title={String(renderValue)}
                      className={classNames(
                        "relative border-r last:border-0 border-solid group-hover:bg-slate-100 border-slate-100 py-2 px-4",
                        col?.pin && "sticky z-[7] bg-white",
                        col?.tdProps?.className
                      )}
                      style={{
                        ...(col?.pin && {
                          left: `${getActionPinnedWidth()}px`,
                        }),
                        ...(col?.tdProps?.style ?? {}),
                      }}
                      {...(Boolean(navigation)
                        ? {
                            onClick: (e) => handleRowClick(e, row),
                          }
                        : {})}
                    >
                      <div
                        className={classNames(
                          "flex items-center justify-start gap-2",
                          col?.navigation && "hover:text-blue"
                        )}
                        {...(col?.navigation
                          ? {
                              onClick: (e) =>
                                handleDataClick(e, row, col?.navigation),
                            }
                          : {})}
                      >
                        {col?.icon ? (
                          <>
                            {typeof col?.icon === "string" &&
                            Boolean(getFieldValue(row, col?.icon)) ? (
                              <DataIcon
                                title={String(renderValue)}
                                icon={getFieldValue(row, col?.icon)}
                                className="!max-w-7"
                              />
                            ) : (
                              <DataIcon
                                title={String(renderValue)}
                                icon={col?.icon}
                                className="!max-w-7"
                              />
                            )}
                          </>
                        ) : (
                          col?.iconPlaceholder && (
                            <DataIcon
                              title={String(renderValue)}
                              icon={col?.iconPlaceholder}
                              className="!max-w-7"
                            />
                          )
                        )}

                        <h5
                          title={String(renderValue)}
                          className={classNames(
                            "flex-1 font-normal group-hover:font-[500] text-ellipsis overflow-hidden whitespace-nowrap"
                          )}
                        >
                          {Boolean(renderValue) ? renderValue : "—"}

                          {searchValue && textMatched(renderValue) && (
                            <span className="absolute left-0 top-0 bg-skyBlue bg-opacity-15 h-full w-full" />
                          )}
                        </h5>
                      </div>

                      {col?.pin && (
                        <div
                          className={classNames(
                            Styles.table_data_column_side_show,
                            tableScrollLeft > 0 && "!visible"
                          )}
                        />
                      )}
                    </td>
                  );
                })}
            </tr>
          );
        })}
    </>
  );
};

export default TableRows;
