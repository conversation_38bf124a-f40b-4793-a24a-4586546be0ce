import { createContext, useContext } from "react";
import { TableType } from "../types";
import { HelperExportType, VariablesType } from "./helpers";
import { StateType } from "./provider";

export interface ContextValueType extends TableType {
  variables: VariablesType;
  helper: HelperExportType;
  state: StateType;
}

export const Context = createContext<ContextValueType | undefined>(undefined);

const useTable = (): ContextValueType => {
  const context = useContext(Context);

  if (!context) {
    throw new Error("useTable must be used within a TableProvider");
  }

  return context;
};

export default useTable;
