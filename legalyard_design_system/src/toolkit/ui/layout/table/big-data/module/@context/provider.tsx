import {
  Dispatch,
  FC,
  ReactNode,
  SetStateAction,
  useEffect,
  useMemo,
  useState,
} from "react";
import { v4 } from "uuid";
import { Context } from ".";
import { validArray } from "../../../../../../utils/helpers/data/array";
import { ColumnsType, SelectedTableRowType, TableType } from "../types";
import { useTableHelper, variables } from "./helpers";

interface ContextTypes extends TableType {
  children?: ReactNode;
}

export interface StateType {
  searchValue: string;
  setSearchValue: Dispatch<SetStateAction<string>>;
  itemsPerPage: number;
  setItemsPerPage: Dispatch<SetStateAction<number>>;
  currentPage: number;
  setCurrentPage: Dispatch<SetStateAction<number>>;
  selected: SelectedTableRowType;
  setSelected: Dispatch<SetStateAction<(string | number)[]>>;
  selectAll: boolean;
  setSelectAll: Dispatch<SetStateAction<boolean>>;
  localLoading: boolean;
  setLocalLoading: Dispatch<SetStateAction<boolean>>;
  tableScrollTop: number;
  tableScrollLeft: number;
}

const TableProvider: FC<ContextTypes> = ({
  children,
  rows,
  columns,
  ...rest
}) => {
  const deepRowClone = JSON.parse(JSON.stringify(rows?.data ?? []));
  const deepColumnClone = JSON.parse(JSON.stringify(columns?.data ?? []));

  const [rowData, setRowData] = useState<any[]>([
    ...(validArray(deepRowClone) ? deepRowClone : []),
  ]);
  const [columnData, setColumnData] = useState<ColumnsType[]>(
    columns?.data ?? []
  );

  const [tableScrollTop, setTableScrollTop] = useState<number>(0);
  const [tableScrollLeft, setTableScrollLeft] = useState<number>(0);
  const [searchValue, setSearchValue] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [selected, setSelected] = useState<(string | number)[]>([]);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const [localLoading, setLocalLoading] = useState<boolean>(true);

  useEffect(() => {
    const rowsWithId = () => {
      if (!rows?.data || rows?.data?.length === 0) return;
      setLocalLoading(true);

      setTimeout(() => {
        try {
          const rowsClone = JSON.parse(JSON.stringify(rows?.data ?? []));

          const dataWithIds = validArray(rowsClone)
            ? rowsClone?.map((item) => {
                const unique_id = v4();
                return { ...item, rowId: unique_id };
              })
            : [];
          setRowData(dataWithIds);
        } catch (error) {
          console.error(error);
        } finally {
          setLocalLoading(false);
        }
      }, 1);
    };

    rowsWithId();
  }, [rows?.data]);

  const helper = useTableHelper({
    rows: rowData,
    columns: columnData,
    setRows: setRowData,
    setColumnData,
    deepRowClone,
    deepColumnClone,
    currentPage,
    itemsPerPage,
    searchValue,
    selected,
    setSelected,
    selectAll,
    setSelectAll,
    tableScrollTop,
    setTableScrollTop,
    tableScrollLeft,
    setTableScrollLeft,
    ...rest,
  });

  const statesData: StateType = useMemo(
    () => ({
      searchValue,
      setSearchValue,
      itemsPerPage,
      setItemsPerPage,
      currentPage,
      setCurrentPage,
      selected,
      setSelected,
      selectAll,
      setSelectAll,
      localLoading,
      setLocalLoading,
      tableScrollTop,
      tableScrollLeft,
    }),
    [
      currentPage,
      itemsPerPage,
      localLoading,
      searchValue,
      selectAll,
      selected,
      tableScrollLeft,
      tableScrollTop,
    ]
  );

  const valueGroup = useMemo(
    () => ({
      variables,
      helper,
      state: statesData,
      rows: {
        ...(rows ?? {}),
        data: rowData,
      },
      columns: {
        ...(columns ?? {}),
        data: columnData,
      },
      ...rest,
    }),
    [columnData, columns, helper, rest, rowData, rows, statesData]
  );

  return (
    <Context.Provider value={{ ...valueGroup }}>{children}</Context.Provider>
  );
};

export default TableProvider;
