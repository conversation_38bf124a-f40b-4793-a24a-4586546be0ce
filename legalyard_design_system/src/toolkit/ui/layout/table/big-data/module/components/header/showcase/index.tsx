import { X } from "react-feather";
import useTable from "../../../@context";

const Showcase = () => {
  const {
    columns,
    header: { showcase } = {},
    helper: { handleColumnSort },
  } = useTable();

  const sortedCol = () => {
    try {
      return columns?.data?.find((item) => Boolean(item?.sortType));
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  if (showcase?.component) {
    return <>{showcase?.component}</>;
  }

  if (!showcase?.show) {
    return null;
  }

  return (
    <div className="h-full flex items-center gap-1 text-sm cursor-default">
      {Boolean(showcase?.dataCount) && (
        <>
          <div className="flex items-center gap-1 h-7 border border-transparent hover:border-main rounded-full px-2 p-1">
            <span></span>
            <span>
              Total <b>{showcase?.dataCount}</b> Records
            </span>
          </div>
        </>
      )}

      {Boolean(sortedCol()) && (
        <>
          <span className="h-6 w-[1px] bg-slate-300" />
          <div className="flex items-center gap-1 h-7 border border-transparent hover:border-main rounded-full px-2 p-1">
            <div className="flex items-center gap-1">
              <span>Sort By:</span>
              <span>
                {sortedCol()?.title} ({sortedCol()?.sortType})
              </span>
            </div>
            <button
              className="border-0 outline-0 bg-transparent text-font hover:text-main"
              title="delete"
              onClick={() => handleColumnSort("unsort", "", "")}
            >
              <X />
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default Showcase;
