import { ChangeEvent, ReactElement, ReactNode } from "react";

export type SelectedTableRowType = (string | number)[];

export interface NavigationType {
  url: string;
  newTab?: boolean;
}

export interface ColumnsType {
  key: string;
  title: string;
  field: string;
  renderField?: string[];
  icon?: string | ReactNode;
  iconPlaceholder?: string | ReactNode;
  width?: string | number;
  minWidth?: number;
  resize?: boolean;
  sortable?: boolean;
  sortType?: string;
  pin?: boolean;
  navigation?: NavigationType;
  /**
   * A function that returns a `ReactElement` where the root element must be a `<td>`.
   * @note Please ensure the returned element uses `<td>` as the root.
   */
  component?: (param: {
    row: any;
    column: ColumnsType;
    rowIndex: number;
  }) => ReactElement<"td">;
  thProps?: {
    [key: string]: any;
  };
  tdProps?: {
    [key: string]: any;
  };
}

export interface ScrollCountType {
  top?: number;
  left?: number;
}

export interface CheckboxType {
  onChange?: (ids?: (string | number)[], rows?: any[]) => void;
  colWidth?: number;
  className?: string;
  pin?: boolean;
  thProps?: {
    [key: string]: any;
  };
}

export interface ActionType {
  component: (data?: any) => ReactNode;
  colWidth?: number;
  inHeader?: boolean;
  onRowHover?: boolean;
  className?: string;
  pin?: boolean;
  thProps?: {
    [key: string]: any;
  };
}

export interface ErrorType {
  component: ReactNode;
  message?: string;
}

export interface ShowcaseType {
  show: boolean;
  component?: ReactNode;
  dataCount?: number;
}

export interface OperationType {
  show: boolean;
  component?: ReactNode;
  onEdit?: (ids?: SelectedTableRowType, rows?: any[]) => void;
  onDelete?: (ids?: SelectedTableRowType, rows?: any[]) => void;
  deleteComplete?: boolean;
  additional?: {
    title?: ReactNode;
    event?: () => void;
  }[];
}

export interface HeaderType {
  show: boolean;
  loading?: boolean;
  className?: string;
  component?: ReactNode;
  showcase?: ShowcaseType;
  operation?: OperationType;
  search?: {
    show: boolean;
    loading?: boolean;
    onChange?: (d?: ChangeEvent<HTMLInputElement>) => void;
    onRemove?: (d?: any) => void;
    getFiltered?: (count?: number, d?: any[]) => void;
  };
  pagination?: {
    show: boolean;
    loading?: boolean;
    onPageChange?: (d?: number) => void;
    onRowLimitChange?: (d?: number) => void;
    onPrev?: (d?: number) => void;
    onNext?: (d?: number) => void;
  };
}

export interface TableType {
  loading?: boolean;
  className?: string;
  wrapperClassName?: string;
  rows?: {
    data?: any[];
    loading?: boolean;
  };
  columns?: {
    data?: ColumnsType[];
    loading?: boolean;
  };

  action?: ActionType;
  checkbox?: CheckboxType;
  error?: ErrorType;
  header?: HeaderType;
  navigation?: NavigationType;
  onNavigate?: (path?: string, row?: any) => void;
}
