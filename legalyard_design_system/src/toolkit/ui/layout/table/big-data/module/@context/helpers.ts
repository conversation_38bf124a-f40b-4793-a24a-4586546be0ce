import { Dispatch, SetStateAction, UIEvent, useCallback } from "react";
import { validArray } from "../../../../../../utils/helpers/data/array";
import {
  ActionType,
  CheckboxType,
  ColumnsType,
  HeaderType,
  NavigationType,
} from "../types";

export interface VariablesType {
  checkbox: {
    width: number;
  };
  action: {
    width: number;
  };
  column: {
    width: number;
  };
}

export const variables: VariablesType = {
  checkbox: {
    width: 48,
  },
  action: {
    width: 32,
  },
  column: {
    width: 200,
  },
};

interface HelperType {
  rows?: any[];
  onNavigate?: (path?: string, row?: any) => void;
  deepRowClone: any[];
  columns: ColumnsType[];
  deepColumnClone: ColumnsType[];
  checkbox?: CheckboxType;
  action?: ActionType;
  setColumnData: Dispatch<SetStateAction<ColumnsType[]>>;
  setRows: Dispatch<SetStateAction<any[]>>;
  tableScrollTop: number;
  setTableScrollTop: Dispatch<SetStateAction<number>>;
  tableScrollLeft: number;
  setTableScrollLeft: Dispatch<SetStateAction<number>>;
  setSelected: Dispatch<SetStateAction<(string | number)[]>>;
  setSelectAll: Dispatch<SetStateAction<boolean>>;
  selectAll?: boolean;
  selected: (string | number)[];
  currentPage: number;
  itemsPerPage: number;
  header?: HeaderType;
  searchValue?: string;
  navigation?: NavigationType;
}

export const useTableHelper = ({
  rows,
  onNavigate,
  deepRowClone,
  deepColumnClone,
  columns,
  checkbox,
  action,
  setColumnData,
  setRows,
  tableScrollTop,
  setTableScrollTop,
  tableScrollLeft,
  setTableScrollLeft,
  setSelected,
  selectAll,
  setSelectAll,
  currentPage,
  itemsPerPage,
  header,
  searchValue,
  navigation,
}: HelperType): HelperExportType => {
  const handlePin = useCallback(
    (type?: string, key?: string) => {
      try {
        const removePin = (dataToSort: ColumnsType[]) => {
          return dataToSort?.map((item) => {
            if ("pin" in item) delete item?.pin;
            return { ...item };
          });
        };
        if (type === "unpin") {
          setColumnData((prev) => {
            const originalIndex = deepColumnClone?.findIndex(
              (item) => item?.key === key
            );
            let dataToChange = removePin(prev);
            const currentItem = dataToChange?.find((item) => item?.key === key);
            const currentIndex = dataToChange?.findIndex(
              (item) => item?.key === key
            );

            if (currentIndex > -1 && originalIndex > -1 && currentItem) {
              dataToChange.splice(currentIndex, 1);
              dataToChange.splice(originalIndex, 0, currentItem);
            }

            return dataToChange;
          });
        } else {
          setColumnData((prev) => {
            const currentData = validArray(prev) ? Array.from(prev) : [];
            let dataToChange = removePin(currentData);

            const currentIndex = dataToChange?.findIndex(
              (item) => item?.key === key
            );

            if (currentIndex > -1) {
              const [item] = dataToChange.splice(currentIndex, 1);
              item.pin = true;
              dataToChange.unshift(item);
            }

            return dataToChange;
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    [deepColumnClone, setColumnData]
  );

  const getActionPinnedWidth = useCallback((): number => {
    try {
      const act = action?.pin ? action?.colWidth ?? variables.action.width : 0;
      const chk = checkbox?.pin
        ? checkbox?.colWidth ?? variables.checkbox.width
        : 0;

      return Number(act) + Number(chk);
    } catch (error) {
      console.error(error);
      return 0;
    }
  }, [action?.colWidth, action?.pin, checkbox?.colWidth, checkbox?.pin]);

  const getPinnedWidth = useCallback((): number => {
    try {
      const act = action?.pin ? action?.colWidth ?? variables.action.width : 0;
      const chk = checkbox?.pin
        ? checkbox?.colWidth ?? variables.checkbox.width
        : 0;

      const pinnedCol = columns?.find((item) => Boolean(item?.pin));

      const colWidth = pinnedCol
        ? pinnedCol?.width ?? pinnedCol?.minWidth ?? variables.column.width
        : 0;

      return Number(act) + Number(chk) + Number(colWidth);
    } catch (error) {
      console.error(error);
      return 0;
    }
  }, [
    action?.colWidth,
    action?.pin,
    checkbox?.colWidth,
    checkbox?.pin,
    columns,
  ]);

  const getColumnCount = useCallback((): number => {
    try {
      let columnCount = columns?.length;

      if (action) {
        columnCount = columnCount + 1;
      }

      if (checkbox) {
        columnCount = columnCount + 1;
      }

      return columnCount;
    } catch (error) {
      console.error(error);
      return columns?.length;
    }
  }, [action, checkbox, columns?.length]);

  const handleScroll = useCallback(
    (e: UIEvent<HTMLDivElement>) => {
      try {
        const currentTarget = e?.currentTarget;
        if (currentTarget) {
          const { scrollTop, scrollLeft } = e.currentTarget;
          if (tableScrollTop <= 2 || scrollTop === 0) {
            setTableScrollTop(scrollTop ?? 0);
          }
          if (tableScrollLeft <= 2 || scrollLeft === 0) {
            setTableScrollLeft(scrollLeft ?? 0);
          }
        }
      } catch (error) {
        console.error(error);
      }
    },
    [setTableScrollLeft, setTableScrollTop, tableScrollLeft, tableScrollTop]
  );

  const handleSearchTable = useCallback(
    (searchValue: string): any[] => {
      try {
        const searchInObject = (obj: any, searchValue: string): boolean => {
          const searchValueLower = searchValue?.toString().toLowerCase();

          for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
              const value = obj[key];

              if (typeof value === "object" && value !== null) {
                if (searchInObject(value, searchValue)) {
                  return true;
                }
              } else if (
                value?.toString().toLowerCase().includes(searchValueLower)
              ) {
                return true;
              }
            }
          }
          return false;
        };

        if (validArray(rows) && Boolean(searchValue)) {
          return (
            rows?.filter((item) => {
              return searchInObject(item, searchValue);
            }) ?? []
          );
        } else {
          return [];
        }
      } catch (error) {
        console.error(error);
        return [];
      }
    },
    [rows]
  );

  const filteredData = useCallback((): any[] => {
    try {
      return (
        (Boolean(searchValue) ? handleSearchTable(searchValue ?? "") : rows) ??
        []
      );
    } catch (error) {
      console.error(error);
      return [];
    }
  }, [handleSearchTable, rows, searchValue]);

  const handlePageData = useCallback(() => {
    try {
      const { pagination: { show: PaginationShow = true } = {} } =
        header as HeaderType;

      const searchFilterData = Boolean(searchValue)
        ? handleSearchTable(searchValue ?? "")
        : rows;

      const indexOfLastItem = currentPage * itemsPerPage;
      const indexOfFirstItem = indexOfLastItem - itemsPerPage;
      const perPageRowData = searchFilterData?.slice(
        indexOfFirstItem,
        indexOfLastItem
      );
      const finalData =
        (PaginationShow ? perPageRowData : searchFilterData) ?? [];

      if (header?.search?.getFiltered) {
        header?.search?.getFiltered(finalData?.length, finalData);
      }
      return finalData;
    } catch (error) {
      console.error(error);
      return [];
    }
  }, [currentPage, handleSearchTable, header, itemsPerPage, rows, searchValue]);

  const getSelectRows = useCallback(
    (ids: (string | number)[], rowData?: any[]): any[] => {
      try {
        const idsSet = new Set(ids);
        const currentRowData = validArray(rowData) ? rowData : [];
        return currentRowData?.filter((item) => idsSet.has(item?.rowId));
      } catch (error) {
        console.error(error);
        return [];
      }
    },
    []
  );

  const handleSelectCallBack = useCallback(
    (ids: (string | number)[], rowData?: any[]) => {
      try {
        if (checkbox?.onChange) {
          const selectedRows = getSelectRows(ids, rowData);
          checkbox?.onChange(ids, selectedRows);
        }
      } catch (error) {
        console.error(error);
      }
    },
    [checkbox, getSelectRows]
  );

  const handleSelect = useCallback(
    (row: Record<string, any>) => {
      try {
        let currentRow = { ...(row ?? {}) };
        const rowData = handlePageData();

        setSelected((prev: (string | number)[]) => {
          let selectedData = validArray(prev) ? Array.from(prev) : [];
          if (selectedData?.includes(currentRow?.rowId)) {
            selectedData = selectedData?.filter(
              (item: string | number) => item !== currentRow.rowId
            );
            setSelectAll(false);
          } else {
            selectedData.push(currentRow.rowId);
            if (rowData?.length === selectedData?.length) {
              setSelectAll(true);
            }
          }

          handleSelectCallBack(selectedData, rowData);

          return selectedData;
        });
      } catch (error) {
        console.error(error);
      }
    },
    [handlePageData, handleSelectCallBack, setSelectAll, setSelected]
  );

  const handleSelectAll = useCallback(
    (e: any) => {
      try {
        if (selectAll) {
          setSelected([]);
          setSelectAll(false);
          handleSelectCallBack([]);
        } else if (e?.target?.value) {
          const rowData = handlePageData();
          const currentIds = validArray(rowData)
            ? rowData.map((item) => {
                return item?.rowId;
              })
            : [];

          setSelected(currentIds);
          handleSelectCallBack(currentIds, rowData);
          setSelectAll(true);
        }
      } catch (error) {
        console.error(error);
      }
    },
    [handlePageData, handleSelectCallBack, selectAll, setSelectAll, setSelected]
  );

  const removeSelect = useCallback(() => {
    try {
      setSelected([]);
      setSelectAll(false);
    } catch (error) {
      console.error(error);
    }
  }, [setSelectAll, setSelected]);

  const getFieldValue = useCallback(
    (row: any, field: string | string[]): string | number => {
      try {
        const extractValue = (obj: any, fieldPath: string): string | number => {
          const keys = fieldPath.split(".");
          let value = obj;

          for (const key of keys) {
            if (value && typeof value === "object" && key in value) {
              value = value[key];
            } else {
              return "";
            }
          }

          return typeof value === "string" || typeof value === "number"
            ? value
            : "";
        };

        if (typeof field === "string") {
          return extractValue(row, field);
        }

        if (Array.isArray(field)) {
          return field
            .map((singleField) => extractValue(row, singleField))
            .filter(Boolean)
            .join(" ");
        }

        return "";
      } catch (error) {
        return "";
      }
    },
    []
  );

  const handleColumnSort = useCallback(
    (type: string, key: string, field: string | string[]) => {
      try {
        const removeSort = (dataToSort: ColumnsType[]) => {
          return dataToSort?.map((item) => {
            return { ...item, sortType: "" };
          });
        };

        if (type === "unsort") {
          setColumnData((prev: ColumnsType[]) => {
            return removeSort(prev);
          });
          setRows(deepRowClone);
        } else {
          setColumnData((prev: ColumnsType[]) => {
            const currentData = validArray(prev) ? Array.from(prev) : [];
            const dataToChange = removeSort(currentData);

            const currentIndex = dataToChange?.findIndex(
              (item) => item?.key === key
            );

            if (currentIndex > -1) {
              dataToChange[currentIndex].sortType = type;
            }

            return dataToChange;
          });

          setRows((prev: any[]) => {
            let currentData: any[] = [];

            if (validArray(prev)) {
              currentData = [...prev];
              const sortFn = (a: any, b: any) => {
                const fieldArray = Array.isArray(field) ? field : [field];
                for (let field of fieldArray) {
                  const aValue = getFieldValue(a, field)?.toString();
                  const bValue = getFieldValue(b, field)?.toString();

                  if (aValue !== bValue) {
                    if (type === "asc") {
                      return aValue.localeCompare(bValue);
                    } else if (type === "desc") {
                      return bValue.localeCompare(aValue);
                    }
                  }
                }
                return 0;
              };

              currentData.sort(sortFn);
            }

            return currentData;
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    [deepRowClone, getFieldValue, setColumnData, setRows]
  );

  const handleNavigation = useCallback(
    (row: any, nav?: NavigationType) => {
      try {
        const replaceParamsInPath = (path?: string): string => {
          try {
            if (path) {
              return path.replace(/\[([^\]]+)\]/g, (match, paramName) => {
                const value = getFieldValue(row, paramName);
                return Boolean(value) && typeof value == "string"
                  ? value
                  : match;
              });
            } else {
              return path ?? "";
            }
          } catch (error) {
            return path ?? "";
          }
        };

        const navConfig = nav ?? navigation;

        const finalPath = replaceParamsInPath(navConfig?.url ?? "");

        if (finalPath) {
          if (navConfig?.newTab) {
            if (window) {
              window.open(finalPath, "_blank");
            }
          } else {
            if (onNavigate) {
              onNavigate(finalPath, row);
            }
          }
        }
      } catch (error) {
        console.error(error);
      }
    },
    [getFieldValue, navigation, onNavigate]
  );

  return {
    handleColumnSort,
    handlePin,
    getActionPinnedWidth,
    getPinnedWidth,
    handleScroll,
    handleSearchTable,
    getColumnCount,
    handlePageData,
    getSelectRows,
    handleSelect,
    handleSelectAll,
    filteredData,
    removeSelect,
    getFieldValue,
    handleNavigation,
  };
};

export interface HelperExportType {
  handleColumnSort: (
    type: string,
    key: string,
    field: string | string[]
  ) => void;
  handlePin: (type?: string, d?: string) => void;
  getActionPinnedWidth: () => number;
  getPinnedWidth: () => number;
  handleScroll: (e: UIEvent<HTMLDivElement>) => void;
  handleSearchTable: (s: string) => any[];
  getColumnCount: () => number;
  handlePageData: () => any[];
  getSelectRows: (ids: (string | number)[], rowData?: any[]) => any[];
  handleSelect: (row: Record<string, any>) => void;
  handleSelectAll: (d?: any) => void;
  filteredData: () => any[];
  removeSelect: () => void;
  getFieldValue: (row: any, field: string | string[]) => string | number;
  handleNavigation: (row: any, nav?: NavigationType) => void;
}
