import Placeholder from "../../../../../../..//assets/placeholder/2.svg";
import Image from "../../../../../../image";
import useTable from "../../@context";

const TableNotFoundError = () => {
  const { error } = useTable();

  if (error?.component) {
    return <>{error?.component}</>;
  }

  return (
    <div className="w-full min-h-56 h-auto max-h-max mt-[1px] bg-slate-100 py-5 px-4">
      <div className="w-full max-w-max sm:max-w-[75%]  md:max-w-[50%] h-full mx-auto relative top-5">
        <div className="w-full h-full flex items-center sm:flex-row flex-col-reverse gap-5">
          <div className="flex flex-col items-start gap-3">
            <h2 className="teko_font text-5xl">Oops, No Matches!</h2>
            <p className="max">
              It looks like there are no results for current filter. Consider
              modifying your filter terms for better results.
            </p>
          </div>
          <div>
            <Image
              src={Placeholder}
              className="max-h-48 w-auto object-contain"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TableNotFoundError;
