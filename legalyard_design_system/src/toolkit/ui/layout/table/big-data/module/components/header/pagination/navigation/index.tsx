import classNames from "classnames";
import { ChevronLeft, ChevronRight } from "react-feather";
import useTable from "../../../../@context";
import { HeaderType } from "../../../../types";

const Navigation = () => {
  const {
    state: { searchValue, currentPage, itemsPerPage, setCurrentPage },
    helper: { handleSelectAll, filteredData },
    header,
    rows,
  } = useTable();

  const { pagination: { onPrev, onNext, onPageChange } = {} } =
    header as HeaderType;

  const filterData = filteredData();
  const dataCount = searchValue ? filterData?.length : rows?.data?.length;
  const rowLength = dataCount ?? 0;
  const totalPages = Math.ceil(rowLength / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const itsLastPage = currentPage === totalPages;
  const firstCount = Number(indexOfFirstItem) + 1;
  const lastCount = rowLength < indexOfLastItem ? rowLength : indexOfLastItem;

  const handlePrev = () => {
    try {
      setCurrentPage((prev: number) => {
        const page = prev > 1 ? prev - 1 : prev;

        if (onPrev) {
          onPrev(page);
        }

        if (onPageChange) {
          onPageChange(page);
        }

        return page;
      });
      handleSelectAll();
    } catch (error) {
      console.error(error);
    }
  };

  const handleNext = () => {
    try {
      setCurrentPage((prev: number) => {
        const page = prev < totalPages ? prev + 1 : prev;

        if (onNext) {
          onNext(page);
        }

        if (onPageChange) {
          onPageChange(page);
        }

        return page;
      });
      handleSelectAll();
    } catch (error) {
      console.error(error);
    }
  };

  const btnClass =
    "group border border-transparent outline-none rounded-base p-1 bg-slate-50 hover:bg-white hover:border-main active:bg-slate-100 disabled:border-0 disabled:bg-transparent";
  return (
    <div className="flex items-center gap-1.5">
      <button
        className={classNames(btnClass)}
        disabled={currentPage === 1}
        onClick={() => handlePrev()}
      >
        <span className="group-disabled:text-slate-400">
          <ChevronLeft />
        </span>
      </button>
      <span className="text-sm font-medium">
        {firstCount} - {lastCount}
      </span>
      <button
        className={classNames(btnClass)}
        disabled={itsLastPage || firstCount > lastCount}
        onClick={() => handleNext()}
      >
        <span className="group-disabled:text-slate-400">
          <ChevronRight />
        </span>
      </button>
    </div>
  );
};

export default Navigation;
