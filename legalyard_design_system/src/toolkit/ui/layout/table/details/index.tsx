import { FC, Fragment } from "react";
import classNames from "classnames";
import { validArray } from "../../../../utils/helpers/data/array";
import { propsType } from "../types";

const DetailsTable: FC<propsType> = ({ data, className, tdClassName }) => {
  return (
    <table
      className={classNames(
        "details_table border-none border-0 w-full",
        className
      )}
    >
      <tbody>
        {validArray(data) &&
          data?.map((item: any, index: any) => {
            return (
              <tr key={index}>
                {validArray(item?.td) &&
                  item?.td?.map((idItem: string, tdIndex: any) => {
                    const parts = String(idItem)?.split("\n") ?? [];

                    return (
                      <td
                        key={tdIndex}
                        className={classNames(
                          "p-1 font-bold text-sm",
                          tdClassName,
                          tdIndex === 0 && "bg-muted capitalize font-normal"
                        )}
                      >
                        {validArray(parts)
                          ? parts?.map((part, index) => (
                              <Fragment key={index}>
                                {part}
                                {index !== parts.length - 1 && <br />}
                              </Fragment>
                            ))
                          : idItem}
                      </td>
                    );
                  })}
              </tr>
            );
          })}
      </tbody>
    </table>
  );
};

export default DetailsTable;
