import { Fragment } from "react";
import useTiles from "../../@context";

const TilesBody = ({ data }: { data?: any[] }) => {
  const { tileCard } = useTiles();

  return (
    <div className="relative grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4">
      {data?.map((dt: any, index: number) => {
        return (
          <Fragment key={String(index) + dt?._id ? String(dt?._id) : ""}>
            {tileCard ? tileCard({ card: dt }) : null}
          </Fragment>
        );
      })}
    </div>
  );
};

export default TilesBody;
