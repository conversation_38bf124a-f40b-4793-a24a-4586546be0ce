import useTable from "../../../@context";

const Showcase = () => {
  const { header } = useTable();

  if (header?.showcase?.component) {
    return <>{header?.showcase?.component}</>;
  }

  if (!header?.showcase?.show) {
    return null;
  }

  return (
    <div className="h-full flex items-center gap-1 text-sm cursor-default">
      {Boolean(header?.showcase?.dataCount) && (
        <>
          <div className="flex items-center gap-1 h-7 border border-transparent hover:border-main rounded-full px-2 p-1">
            <span></span>
            <span>
              Total <b>{header?.showcase?.dataCount}</b> Records
            </span>
          </div>
        </>
      )}
    </div>
  );
};

export default Showcase;
