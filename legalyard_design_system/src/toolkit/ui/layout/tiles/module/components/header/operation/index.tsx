import Button from "../../../../../../button";
import Loader from "../../../../../../loader";
import { validArray } from "../../../../../../../utils/helpers/data/array";
import { lazy, Suspense, useEffect, useState } from "react";
import { ArrowRight, Edit2, Trash2, X } from "react-feather";
import useTable from "../../../@context";

const Modal = lazy(() => import("../../../../../../modal"));
const Delete = lazy(() => import("../../../../../../modal/components/delete"));

const Operation = () => {
  const {
    helper: { removeSelect, getSelectRows },
    state: { selected },
    operation,
    tiles,
  } = useTable();

  const [deleteModal, setDeleteModal] = useState(false);

  const selectedRows = getSelectRows(selected, tiles?.data);

  const handleEdit = () => {
    try {
      if (operation?.onEdit) {
        operation?.onEdit(selected, selectedRows);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleDelete = () => {
    try {
      if (operation?.onDelete) {
        operation?.onDelete(selected, selectedRows);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (operation?.deleteComplete) {
      setDeleteModal(false);
    }
  }, [operation?.deleteComplete]);

  if (operation?.component) {
    return <>{operation?.component}</>;
  }

  if (!operation?.show) {
    return null;
  }

  if (!validArray(selected)) {
    return null;
  }

  return (
    <>
      <div className="operation h-full absolute top-0 left-0 right-0 bottom-0 bg-white z-20">
        <div className="h-full flex items-center gap-2 bg-green bg-opacity-20 px-6 py-2">
          <div className="flex items-center gap-2 text-sm">
            <button
              className="operation_cta flex items-center justify-center text-sm border-0 outline-0 p-0.5 hover:bg-red hover:bg-opacity-30"
              onClick={() => removeSelect()}
              title="Clear Selections"
            >
              <X className="w-4 h-4" />
            </button>
            <div className="flex items-center gap-1 text-sm">
              <span>{selected?.length}</span>
              <span>Selected</span>
              <ArrowRight className="w-3 h-3" />
            </div>
          </div>

          <Button
            effect
            variant="action"
            className="operation_cta text-sm flex items-center gap-1"
            onClick={() => handleEdit()}
          >
            <Edit2 className="w-3 h-3" />
            <span>Edit</span>
          </Button>

          <Button
            effect
            variant="action"
            className="operation_cta text-sm flex items-center gap-1"
            onClick={() => setDeleteModal(true)}
          >
            <Trash2 className="w-3 h-3" />
            <span>Delete</span>
          </Button>

          {validArray(operation?.additional) &&
            operation?.additional?.map((act, index) => {
              return (
                <Button
                  key={index}
                  effect
                  variant="action"
                  className="operation_cta text-sm flex items-center gap-1"
                  onClick={() => {
                    try {
                      if (act?.event) {
                        act?.event();
                      }
                    } catch (error) {
                      console.error(error);
                    }
                  }}
                >
                  {act?.title}
                </Button>
              );
            })}
        </div>
      </div>
      <Suspense fallback={<Loader screen center />}>
        <Modal
          open={deleteModal}
          onClose={() => setDeleteModal(false)}
          outsideClose
          close
          wrapperClassName="!-top-[30%]"
        >
          <div className="w-96">
            <Suspense fallback={<Loader center />}>
              <Delete
                count={selected?.length}
                validation
                onCancel={() => setDeleteModal(false)}
                onDelete={() => handleDelete()}
              />
            </Suspense>
          </div>
        </Modal>
      </Suspense>
    </>
  );
};

export default Operation;
