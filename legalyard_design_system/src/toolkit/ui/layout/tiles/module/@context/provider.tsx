import {
  Dispatch,
  FC,
  ReactNode,
  SetStateAction,
  useMemo,
  useState,
} from "react";
import { Context } from ".";
import { SelectedTileType, TileViewType } from "../types";
import { useTilesHelper } from "./helper";

export interface StateType {
  searchValue: string;
  setSearchValue: Dispatch<SetStateAction<string>>;
  selected: SelectedTileType;
  setSelected: Dispatch<SetStateAction<SelectedTileType>>;
  currentPage: number;
  setCurrentPage: Dispatch<SetStateAction<number>>;
}

interface ContextTypes extends TileViewType {
  children?: ReactNode;
}
const TilesViewProvider: FC<ContextTypes> = ({ children, ...rest }) => {
  const [selected, setSelected] = useState<SelectedTileType>([]);
  const [searchValue, setSearchValue] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);

  const helper = useTilesHelper({
    searchValue,
    setSelected,
    ...rest,
  });

  const statesData: StateType = useMemo(
    () => ({
      searchValue,
      setSearchValue,
      selected,
      setSelected,
      currentPage,
      setCurrentPage,
    }),
    [currentPage, searchValue, selected]
  );

  const valueGroup = useMemo(
    () => ({
      helper: helper,
      state: statesData,
      ...rest,
    }),
    [helper, rest, statesData]
  );

  return (
    <Context.Provider value={{ ...valueGroup }}>{children}</Context.Provider>
  );
};

export default TilesViewProvider;
