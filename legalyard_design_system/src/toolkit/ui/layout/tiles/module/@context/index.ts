import { createContext, useContext } from "react";
import { TileViewType } from "../types";
import { HelperExportType } from "./helper";
import { StateType } from "./provider";

export interface ContextValueType extends TileViewType {
  state: StateType;
  helper: HelperExportType;
}

export const Context = createContext<ContextValueType | undefined>(undefined);

const useTiles = (): ContextValueType => {
  const context = useContext(Context);

  if (!context) {
    throw new Error("useTiles must be used within a TilesViewProvider");
  }

  return context;
};

export default useTiles;
