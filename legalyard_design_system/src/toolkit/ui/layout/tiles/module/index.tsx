import { validArray } from "../../../../utils/helpers/data/array";
import classNames from "classnames";
import React, { FC } from "react";
import { ErrorBoundary } from "react-error-boundary";
import useTiles from "./@context";
import TilesViewProvider from "./@context/provider";
import TilesNotFoundError from "./components/error";
import TilesNoDataError from "./components/error/no-data";
import Header from "./components/header";
import ListBody from "./components/list";
import TilesBody from "./components/tiles";
import { TileViewType } from "./types";

const TileViewComp: FC<TileViewType> = React.memo(
  ({ className, wrapperClassName, tiles }) => {
    const {
      state: { searchValue },
      helper: { handleScroll, filteredData },
      view,
    } = useTiles();

    const finalData = filteredData();

    return (
      <div
        className={classNames(
          "ui_layout_tiles bg-slate-200 rounded-base p-1 h-full w-full max-w-full max-h-full",
          className
        )}
      >
        <Header />

        <div
          className={classNames(
            "w-full h-full max-w-full max-h-full overflow-auto",
            wrapperClassName
          )}
          onScroll={(e) => handleScroll(e)}
        >
          {validArray(tiles?.data) ? (
            <>
              {validArray(finalData) ? (
                <>
                  {view && view === "list" ? (
                    <ListBody data={finalData} />
                  ) : (
                    <TilesBody data={finalData} />
                  )}
                </>
              ) : (
                searchValue && <TilesNotFoundError />
              )}
            </>
          ) : (
            <TilesNoDataError />
          )}
        </div>
      </div>
    );
  }
);

const DataTiles: FC<TileViewType> = React.memo(({ ...rest }) => {
  return (
    <ErrorBoundary fallback={<div>Something went wrong with Tiles View</div>}>
      <TilesViewProvider {...rest}>
        <TileViewComp {...rest} />
      </TilesViewProvider>
    </ErrorBoundary>
  );
});

export default DataTiles;