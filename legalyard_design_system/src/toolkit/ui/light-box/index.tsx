import Placeholder from "../../assets/placeholder/image.png";
import { SyntheticEvent, useCallback, useEffect, useState } from "react";
import { X } from "react-feather";
import Loader from "../loader";

const LightBox = ({
  src,
  title,
  open,
  onClose,
}: {
  src: string;
  title?: string;
  open: boolean;
  onClose: () => void;
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const handleError = useCallback(
    (e: SyntheticEvent<HTMLImageElement, Event>) => {
      try {
        const imgElement = e.target as HTMLImageElement;
        imgElement.src = Placeholder;
        setIsLoading(false);
      } catch (err) {
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    try {
      const img = new Image();
      img.src = src ?? "";

      img.onload = () => {
        setIsLoading(false);
      };

      img.onerror = () => {
        setIsLoading(false);
      };

      return () => {
        img.onload = null;
        img.onerror = null;
      };
    } catch (error) {
      console.error(error);
      setIsLoading(false);
    }
  }, [handleError, src]);

  if (!open) {
    return null;
  }

  return (
    <div
      aria-label="lightbox"
      className="lightbox fixed left-0 right-0 top-0 bottom-0 h-screen w-screen z-[112] overflow-hidden"
    >
      <div
        aria-label="Backdrop"
        className="bg-zinc-700 bg-opacity-95 backdrop-blur-sm w-screen h-screen absolute left-0 right-0 top-0 bottom-0"
        onClick={() => onClose()}
      />

      <div className="absolute top-0 left-0 right-0 bg-transparent text-white w-full h-auto overflow-auto p-2 mb-5 flex items-center justify-between">
        <h3 className="ms-4 w-full text-sb whitespace-nowrap text-ellipsis overflow-hidden font-medium">
          {title || "File"}
        </h3>

        <div
          className="cursor-pointer text-white p-1 hover:bg-main"
          title="Close"
          onClick={() => onClose()}
        >
          <X className="w-10 h-10" />
        </div>
      </div>

      <div className="absolute left-0 right-0 top-1/2 -translate-y-1/2 bg-transparent max-w-[95vw] sm:max-w-[80vw] max-h-[95vh] h-max m-auto shadow-lg">
        {isLoading ? (
          <Loader big center box />
        ) : (
          <img
            src={src || ""}
            alt="preview"
            className="w-full h-full object-contain"
            onError={handleError}
          />
        )}
      </div>
    </div>
  );
};

export default LightBox;
