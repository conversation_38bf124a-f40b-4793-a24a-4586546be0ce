import useOutsideAlert from "../../utils/hooks/useOutsideAlert";
import classNames from "classnames";
import { FC, MouseEvent, useEffect, useRef, useState } from "react";
import { X } from "react-feather";
import { closeClass, contentClass, openClass, openerPosition } from "./helpers";
import { DrawerType } from "./types";

const Drawer: FC<DrawerType> = ({
  id,
  open,
  position,
  close,
  onClose,
  children,
  className,
  backdropClassName,
  hideBackdrop,
  contentClassName,
  outsideClose,
  ...rest
}) => {
  const contentRef = useRef(null);

  const [display, setDisplay] = useState(false);

  const handleClose = (e: MouseEvent<HTMLButtonElement | HTMLDivElement>) => {
    try {
      e?.stopPropagation();
      e?.preventDefault();

      if (onClose) {
        onClose(false);
      }

      return false;
    } catch (error) {
      console.error(error);
    }
  };

  const handleOutsideClose = (e: MouseEvent<HTMLDivElement>) => {
    try {
      e?.stopPropagation();
      e?.preventDefault();

      if (outsideClose) {
        handleClose(e);
        if (onClose) {
          onClose(false);
        }
      }

      return false;
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (open) {
      setDisplay(true);
    } else {
      setTimeout(() => {
        setDisplay(false);
      }, 100);
    }
  }, [open]);

  useOutsideAlert(contentRef, handleOutsideClose, !display);

  if (!open) {
    return null;
  }

  return (
    <div
      id={id ?? "drawer"}
      aria-label="drawer"
      className={classNames(
        "fixed w-screen h-screen transition-[transform] z-[115] overflow-hidden",
        openerPosition(position),
        openClass(position, display),
        className
      )}
      {...rest}
    >
      <div className="relative w-full h-full">
        {open && !hideBackdrop && (
          <div
            role="button"
            aria-label="backdrop"
            onClick={(e) => handleOutsideClose(e)}
            className={classNames(
              "cursor-default bg-main bg-opacity-10 backdrop-blur-sm h-screen w-screen absolute top-0 right-0 bottom-0 left-0",
              backdropClassName
            )}
          />
        )}

        {open && close && (
          <button
            className={classNames(
              "close_icon z-10 absolute cursor-pointer border-0 rounded-base bg-red bg-opacity-100 flex items-center justify-center",
              closeClass(position)
            )}
            onClick={(e) => handleClose(e)}
          >
            <X className="w-8 h-8 text-white" />
          </button>
        )}

        <div
          ref={contentRef}
          aria-label="Drawer Content"
          className={classNames(
            "z-[51] bg-white absolute shadow-lg",
            contentClass(position),
            contentClassName
          )}
        >
          {children ?? "Drawer Content"}
        </div>
      </div>
    </div>
  );
};

export default Drawer;
