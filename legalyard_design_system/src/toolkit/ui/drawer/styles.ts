import styled from "styled-components";
import { StyledType } from "./types";

export const Container = styled.div<StyledType>`
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  overflow: hidden;
  height: calc(100vh - 80px);
  width: 100vw;
  transform: ${({ open }) => (open ? "translateX(0)" : "translateX(-100%)")};
  transition: all 0.5s ease-in-out;
`;

export const Backdrop = styled.div`
  background-color: ${({ theme: { color } }) => `${color.main}40`};
  width: 100vw;
  height: 100vh;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
`;

export const ContentWrapper = styled.div`
  position: absolute;
  left: 0;
  z-index: 999;
  /* padding: 10px; */
  background-color: ${({ theme: { color } }) => color.white};
  min-width: 250px;
  width: max-content;
  height: calc(100vh - 80px);
  overflow: auto;
  margin-inline-end: auto;
  box-shadow: 2px 0 10px ${({ theme: { color } }) => `${color.main}10`},
    inset 0 0 10px ${({ theme: { color } }) => `${color.main}40`};
`;
