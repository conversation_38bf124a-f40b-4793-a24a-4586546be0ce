import { useActive, useCommands } from "@remirror/react";
import {
  CommandButtonGroup,
  CommandMenuItem,
  DropdownButton,
} from "@remirror/react-ui";

const FontSizeButtons = () => {
  const { setFontSize } = useCommands();
  const { fontSize } = useActive();
  const FONT_SIZES = [
    "8",
    "10",
    "12",
    "14",
    "16",
    "18",
    "24",
    "30",
    "34",
    "38",
    "42",
    "46",
    "50",
    "64",
    "72",
    "85",
    "92",
    "100",
  ];
  return (
    <CommandButtonGroup>
      <DropdownButton
        aria-label="Font Size"
        icon="fontSize"
        sx={{ maxHeight: "300px" }}
      >
        {FONT_SIZES.map((size) => (
          <CommandMenuItem
            key={size}
            commandName="setFontSize"
            onSelect={() => setFontSize(size)}
            enabled={setFontSize.enabled(size)}
            active={fontSize({ size })}
            label={size}
            icon={null}
            displayDescription={false}
          />
        ))}
      </DropdownButton>
    </CommandButtonGroup>
  );
};

export default FontSizeButtons;
