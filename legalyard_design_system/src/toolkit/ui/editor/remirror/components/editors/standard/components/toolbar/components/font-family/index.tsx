import { useActive, useCommands } from "@remirror/react";
import {
  CommandButtonGroup,
  CommandMenuItem,
  DropdownButton,
} from "@remirror/react-ui";

const FontFamilyButtons = () => {
  const { setFontFamily } = useCommands();
  const active = useActive();

  const FONT_FAMILIES: Array<[React.CSSProperties["fontFamily"], string]> = [
    ["Arial, sans-serif", "Arial"],
    ["'Roboto', sans-serif", "Roboto"],
    ["'Teko', sans-serif", "Teko"],
    ["'Times New Roman', serif", "Times New Roman"],
    ["'Cursive', cursive", "Cursive"],
    ["'Fantasy', fantasy", "Fantasy"],
    ["'Georgia', serif", "Georgia"],
    ["'Courier New', monospace", "Courier New"],
    ["'Helvetica', sans-serif", "Helvetica"],
    ["'Verdana', sans-serif", "Verdana"],
  ];

  return (
    <CommandButtonGroup>
      <DropdownButton aria-label="Font family" icon="text">
        {FONT_FAMILIES.map(([fontFamily, label]) => (
          <CommandMenuItem
            key={fontFamily}
            commandName="setFontFamily"
            onSelect={() => setFontFamily(fontFamily as string)}
            enabled={setFontFamily.enabled(fontFamily as string)}
            active={active.fontFamily({ fontFamily })}
            label={<span style={{ fontFamily }}>{label}</span>}
          />
        ))}
      </DropdownButton>
    </CommandButtonGroup>
  );
};

export default FontFamilyButtons;
