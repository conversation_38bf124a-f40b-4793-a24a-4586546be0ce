import { useCommands } from "@remirror/react";
import {
  CommandButtonGroup,
  CommandMenuItem,
  DropdownButton,
} from "@remirror/react-ui";

const TableOptionsButton = () => {
  const {
    deleteTableColumn,
    deleteTableRow,
    addTableColumnBefore,
    addTableColumnAfter,
    addTableRowBefore,
    addTableRowAfter,
    deleteTable,
  } = useCommands();
  return (
    <CommandButtonGroup>
      <DropdownButton aria-label="Table Options" icon="tableLine">
        <CommandMenuItem
          commandName="deleteTableColumn"
          onSelect={() => deleteTableColumn()}
          enabled={true}
          label="Delete current column"
        />
        <CommandMenuItem
          commandName="deleteTableRow"
          onSelect={() => deleteTableRow()}
          enabled={true}
          label="Delete current row"
        />
        <CommandMenuItem
          commandName="addTableColumnBefore"
          onSelect={() => addTableColumnBefore()}
          enabled={true}
          label="Add column before"
        />
        <CommandMenuItem
          commandName="addTableColumnAfter"
          onSelect={() => addTableColumnAfter()}
          enabled={true}
          label="Add column after"
        />
        <CommandMenuItem
          commandName="addTableRowBefore"
          onSelect={() => addTableRowBefore()}
          enabled={true}
          label="Add row before"
        />
        <CommandMenuItem
          commandName="addTableRowAfter"
          onSelect={() => addTableRowAfter()}
          enabled={true}
          label="Add row after "
        />
        <CommandMenuItem
          commandName="deleteTable"
          onSelect={() => deleteTable()}
          enabled={true}
          label="delete table"
        />
      </DropdownButton>
    </CommandButtonGroup>
  );
};

export default TableOptionsButton;
