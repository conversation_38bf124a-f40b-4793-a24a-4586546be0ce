import { OnChangeHTML, OnChangeJSON } from "@remirror/react";
import { SocialEditor } from "@remirror/react-editors/social";
import useEditor from "../../@context";

const Social = () => {
  const {
    editorTheme,
    valueFormat,
    initialContent,
    helper: { handleOnchangeCallback },
    placeholder = "Start typing here...",
    autoFocus = false,
  } = useEditor();

  return (
    <SocialEditor
      theme={editorTheme}
      placeholder={placeholder}
      initialContent={initialContent}
      autoFocus={autoFocus}
    >
      {valueFormat === "html" ? (
        <OnChangeHTML onChange={handleOnchangeCallback} />
      ) : (
        <OnChangeJSON onChange={handleOnchangeCallback} />
      )}
    </SocialEditor>
  );
};

export default Social;
