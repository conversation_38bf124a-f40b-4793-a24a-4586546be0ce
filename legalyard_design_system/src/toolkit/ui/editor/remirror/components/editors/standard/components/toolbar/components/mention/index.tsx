import { cx } from "@remirror/core";
import {
  Floating<PERSON><PERSON><PERSON>,
  MentionAtomNodeAttributes,
  useMentionAtom,
} from "@remirror/react";
import { useEffect, useState } from "react";
import useEditor from "../../../../../../../@context";

const MentionSuggester: React.FC = () => {
  const { users: UsersData, tags: TagsData } = useEditor();

  const [options, setOptions] = useState<MentionAtomNodeAttributes[]>([]);
  const { state, getMenuProps, getItemProps, indexIsHovered, indexIsSelected } =
    useMentionAtom({
      items: options,
    });

  useEffect(() => {
    try {
      if (!state) {
        return;
      }

      const searchTerm = state.query.full.toLowerCase();
      let filteredOptions: MentionAtomNodeAttributes[] = [];

      if (
        state.name === "tag" &&
        Array.isArray(TagsData) &&
        TagsData?.length > 0
      ) {
        filteredOptions = TagsData?.filter((tag) =>
          tag.label.toLowerCase().includes(searchTerm)
        );
      } else if (
        state.name === "at" &&
        Array.isArray(UsersData) &&
        UsersData?.length > 0
      ) {
        filteredOptions = UsersData?.filter((user) =>
          user.label.toLowerCase().includes(searchTerm)
        );
      }

      filteredOptions = filteredOptions.sort().slice(0, 5);
      setOptions(filteredOptions);
    } catch (error) {
      console.error(error);
    }
  }, [state]);

  const enabled = Boolean(state);

  if (!UsersData && !TagsData) {
    return null;
  }

  return (
    <FloatingWrapper
      positioner="cursor"
      enabled={enabled}
      placement="left-start"
    >
      <div
        {...getMenuProps()}
        className="mention-suggestions shadow-lg rounded-base bg-white border border-slate-50 p-1"
      >
        {enabled &&
          options.map((user, index) => {
            const isHighlighted = indexIsSelected(index);
            const isHovered = indexIsHovered(index);

            return (
              <div
                key={user.id}
                role="button"
                {...getItemProps({
                  item: user,
                  index,
                })}
                className={cx(
                  "mention-suggestion-item min-w-32 px-3 py-2 hover:bg-slate-200 cursor-pointer rounded-base text-base",
                  isHighlighted && "highlighted",
                  isHovered && "hovered"
                )}
              >
                {user?.label}
              </div>
            );
          })}
      </div>
    </FloatingWrapper>
  );
};
export default MentionSuggester;
