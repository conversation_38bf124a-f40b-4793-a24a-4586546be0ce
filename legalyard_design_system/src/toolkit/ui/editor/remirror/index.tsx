import classNames from "classnames";
import React, {
  FC,
  forwardRef,
  ForwardRefRenderFunction,
  lazy,
  Ref,
  Suspense,
} from "react";
import { ErrorBoundary } from "react-error-boundary";
import Loader from "../../loader";
import EditorProvider from "./@context/provider";
import Standard from "./components/editors/standard";
import Styles from "./style.module.css";
import { RemirrorEditorReferenceType, RemirrorEditorType } from "./types";

// TODO
// We need Previewer component which will read provided HTML or JSON
// & will show the note content only readable
// should include same styles of editor

const Wysiwyg = lazy(() => import("./components/editors/wysiwyg"));
const Social = lazy(() => import("./components/editors/social"));
const Markdown = lazy(() => import("./components/editors/markdown"));

const EditorRender: FC<RemirrorEditorType> = ({ variant, className }) => {
  return (
    <div
      className={classNames(
        Styles.text_editor_styles,
        "before:[&_.remirror-editor_.remirror-is-empty]:content-[attr(data-placeholder)]",
        "before:[&_.remirror-editor_.remirror-is-empty]:text-grey",
        "before:[&_.remirror-editor_.remirror-is-empty]:absolute",
        "before:[&_.remirror-editor_.remirror-is-empty]:italic",
        "before:[&_.remirror-editor_.remirror-is-empty]:text-sb",
        "before:[&_.remirror-editor_.remirror-is-empty]:pointer-events-none",
        "[&_.remirror-list-item-marker-container]:inline-block [&_.remirror-list-item-marker-container]:absolute [&_.remirror-list-item-marker-container]:-left-5 [&_.remirror-list-item-marker-container]:text-center [&_.remirror-list-item-marker-container]:select-none",
        "[&_.remirror-list-item-marker-container_input[type='checkbox']]:accent-main",
        "[&_.column-resize-handle]:absolute [&_.column-resize-handle]:top-0 [&_.column-resize-handle]:bottom-0 [&_.column-resize-handle]:right-[-3px] [&_.column-resize-handle]:w-[4px] [&_.column-resize-handle]:bg-transparent [&_.column-resize-handle]:hover:bg-main [&_.column-resize-handle]:cursor-col-resize [&_.column-resize-handle]:transition-colors [&_.column-resize-handle]:duration-200 [&_.column-resize-handle]:z-[100]",
        "[&_.remirror-mention-atom-at]:bg-main [&_.remirror-mention-atom-at]:bg-opacity-10 [&_.remirror-mention-atom-at]:text-main",
        "[&_.remirror-mention-atom-tag]:bg-blue [&_.remirror-mention-atom-tag]:bg-opacity-10 [&_.remirror-mention-atom-tag]:text-blue",
        className
      )}
    >
      <Suspense fallback={<Loader center big />}>
        {variant === "wysiwyg" ? (
          <Wysiwyg />
        ) : variant === "social" ? (
          <Social />
        ) : variant === "markdown" ? (
          <Markdown />
        ) : (
          <Standard />
        )}
      </Suspense>
    </div>
  );
};

const EditorWrapper: ForwardRefRenderFunction<
  RemirrorEditorReferenceType,
  RemirrorEditorType
> = ({ ...rest }, ref: Ref<RemirrorEditorReferenceType>) => {
  return (
    <ErrorBoundary
      FallbackComponent={({ resetErrorBoundary }) => (
        <div className="flex flex-col items-center justify-center gap-3">
          Something went wrong with Editor{" "}
          <button onClick={() => resetErrorBoundary()}>Try Again</button>
        </div>
      )}
    >
      <EditorProvider ref={ref} {...rest}>
        <EditorRender {...rest} />
      </EditorProvider>
    </ErrorBoundary>
  );
};

const ForwardedRemirrorEditor = forwardRef(EditorWrapper);
const RemirrorEditor = React.memo(ForwardedRemirrorEditor);

export default RemirrorEditor;
