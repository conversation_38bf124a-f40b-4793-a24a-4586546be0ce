/* base styles */
.text_editor_styles {
  @apply rounded-base p-1 h-full w-full max-w-full max-h-full font-normal;
}

/* Blockquote */
.text_editor_styles blockquote {
  @apply border-l-4 border-slate-200 ps-2.5 italic;
}

/* Link Styles */
.text_editor_styles a {
  @apply text-blue cursor-pointer;
}

/* List Styles */
.text_editor_styles ul,
.text_editor_styles ol {
  @apply pl-6 list-outside marker:text-slate-600;
}

.text_editor_styles ul {
  @apply !list-disc;
}

.text_editor_styles ol {
  @apply !list-decimal;
}

.text_editor_styles li {
  @apply relative break-words;
}

/* Table styles */
.text_editor_styles table {
  @apply border-collapse table-fixed w-full overflow-hidden border-spacing-0.5;
}

.text_editor_styles table tr:nth-child(odd) {
  @apply bg-transparent;
}

.text_editor_styles table tr:nth-child(even) {
  @apply bg-slate-50;
}

.text_editor_styles table tr {
  @apply border-t border-zinc-500;
}

.text_editor_styles table th,
.text_editor_styles table td {
  @apply box-border relative align-top border border-zinc-500 p-2;
}

/* Backup */
/* "text-editor rounded-base p-1 h-full w-full max-w-full max-h-full",
"font-normal",
"before:[&_.remirror-editor_.remirror-is-empty]:content-[attr(data-placeholder)]",
"before:[&_.remirror-editor_.remirror-is-empty]:text-grey",
"before:[&_.remirror-editor_.remirror-is-empty]:absolute",
"before:[&_.remirror-editor_.remirror-is-empty]:italic",
"before:[&_.remirror-editor_.remirror-is-empty]:text-sb",
"before:[&_.remirror-editor_.remirror-is-empty]:pointer-events-none",
"[&_blockquote]:border-l-4 [&_blockquote]:border-slate-200 [&_blockquote]:ps-2.5 [&_blockquote]:italic",
"[&_a]:text-blue [&_a]:cursor-pointer",
"[&_ul]:!list-disc [&_ul]:pl-6 [&_ul]:list-outside [&_ul]:marker:text-slate-600",
"[&_ol]:!list-decimal [&_ol]:pl-6 [&_ol]:list-outside [&_ol]:marker:text-slate-600",
"[&_li]:relative [&_li]:break-words",
"[&_.remirror-list-item-marker-container]:inline-block [&_.remirror-list-item-marker-container]:absolute [&_.remirror-list-item-marker-container]:-left-5 [&_.remirror-list-item-marker-container]:text-center [&_.remirror-list-item-marker-container]:select-none",
"[&_.remirror-list-item-marker-container_input[type='checkbox']]:accent-main",
"[&_table]:border-collapse [&_table]:table-fixed [&_table]:w-full [&_table]:overflow-hidden [&_table]:border-spacing-0.5",
"[&_table_tr:nth-child(odd)]:bg-transparent [&_table_tr:nth-child(even)]:bg-slate-50 [&_table_tr]:border-t [&_table_tr]:border-zinc-500",
"[&_table_td]:box-border [&_table_td]:relative [&_table_td]:align-top [&_table_td]:border [&_table_td]:border-zinc-500 [&_table_td]:p-2",
"[&_table_th]:box-border [&_table_th]:relative [&_table_th]:align-top [&_table_th]:border [&_table_th]:border-zinc-500 [&_table_th]:p-2",
"[&_table_.column-resize-handle]:absolute [&_table_.column-resize-handle]:top-0 [&_table_.column-resize-handle]:bottom-0 right-[-2px] w-[4px] bg-[rgba(0,0,0,0.2)] hover:bg-[rgba(0,0,0,0.5)] cursor-col-resize transition-colors duration-200", */
