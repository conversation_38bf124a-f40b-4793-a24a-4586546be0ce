import styled, { css } from "styled-components";

const height = css`
  height: 15px;
`;

export const Container = styled.div`
  position: relative;
  ${height};
  max-width: 100%;
  width: 100%;
  background-color: ${({ theme: { color } }) => color?.grey};
  border-radius: ${({ theme: { element } }) => element?.radius};
  overflow: hidden;

  margin: 0.25rem 0;
`;

export const Number = styled.div`
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;

  font-size: ${({ theme: { font } }) => font?.small};
  color: ${({ theme: { color } }) => color?.white};
  font-weight: 600;
`;

export const ProgressHolder = styled.div<{ $width: any }>`
  ${height};
  width: ${({ $width }) => `${$width}%`};
  background-color: ${({ theme: { color } }) => color?.main};

  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
`;
