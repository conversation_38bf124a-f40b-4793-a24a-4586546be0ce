import { validArray } from "../../../../../../utils/helpers/data/array";
import classNames from "classnames";
import { FC } from "react";
import { Filter, PlusSquare } from "react-feather";
import { FilterGroup } from "../../types";

interface PropsType {
  localFilterGroups: FilterGroup[];
  applyFiltersNow: () => void;
  InitiateAddFilter: () => void;
}

const Actions: FC<PropsType> = ({
  localFilterGroups,
  applyFiltersNow,
  InitiateAddFilter,
}) => {
  return (
    <div className="w-full mt-4 px-4 py-3 sm:py-4 bg-white sticky bottom-0 shadow-inner">
      <div className="w-full text-center mb-4">
        <h6 className="text-slate-700">
          You don't have any advanced filter added.
        </h6>
      </div>
      <div className="w-full max-w-full md:max-w-[85%] lg:max-w-[95%] xl:max-w-[85%] mx-auto flex items-center justify-around flex-wrap-reverse gap-3 sm:gap-4">
        {validArray(localFilterGroups) && (
          <button
            className={classNames(
              "flex-1 flex items-center justify-center gap-2 sticky bottom-0 px-6 py-2",
              "transition-all bg-main bg-opacity-90 hover:bg-opacity-100 hover:shadow",
              "text-white text-sm sm:text-sb md:text-base leading-tight font-semibold rounded-base whitespace-nowrap",
              "disabled:bg-grey disabled:opacity-45"
            )}
            onClick={applyFiltersNow}
            disabled={!validArray(localFilterGroups)}
          >
            <Filter />
            Apply Filters
          </button>
        )}

        <button
          className={classNames(
            "flex-1 flex items-center justify-center gap-2 px-6 py-2 transition-all bg-blue bg-opacity-90 hover:bg-opacity-100 hover:shadow",
            "text-white text-sm sm:text-sb md:text-base leading-tight  font-medium",
            "rounded-base whitespace-nowrap"
          )}
          onClick={() => InitiateAddFilter()}
        >
          <PlusSquare /> Add Filter Group
        </button>
      </div>
    </div>
  );
};

export default Actions;
