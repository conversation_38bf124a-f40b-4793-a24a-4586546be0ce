import { FC, lazy, Suspense } from "react";
import { Loader } from "react-feather";
import { FilterCondition } from "../../../types";
const Select = lazy(() => import("../../../../../../input/select"));
const InputField = lazy(() => import("../../../../../../input/field"));
const DateSelector = lazy(() => import("../../../../../../input/date"));

interface PropsType {
  condition: FilterCondition;
  handleChange: (condition: FilterCondition) => void;
}

const FilterInput: FC<PropsType> = ({ condition, handleChange }) => {
  return (
    <div className="flex-1 w-full">
      {condition?.field?.inputType === "date" ? (
        <Suspense fallback={<Loader className="animate-spin text-main" />}>
          <DateSelector
            name="date"
            label={{ name: "date" }}
            onChange={(e) => handleChange(e)}
            value={String(condition.value) ?? ""}
          />
        </Suspense>
      ) : condition?.field?.inputType === "select" ? (
        <Suspense fallback={<Loader className="animate-spin text-main" />}>
          <Select
            value={String(condition.value) ?? ""}
            option={{ data: condition?.field?.options ?? [] }}
            onChange={(e) =>
              handleChange({
                ...condition,
                value: e.target.value,
              })
            }
            className="w-full min-w-max sm:w-4/12 sm:max-w-[50%]"
          />
        </Suspense>
      ) : condition?.field?.inputType === "multiselect" ? (
        // TODO
        // Add multi select input
        <Suspense fallback={<Loader className="animate-spin text-main" />}>
          <Select
            value={String(condition.value) ?? ""}
            option={{ data: condition?.field?.options ?? [] }}
            onChange={(e) =>
              handleChange({
                ...condition,
                value: e.target.value,
              })
            }
            className="w-full min-w-max sm:w-4/12 sm:max-w-[50%]"
          />
        </Suspense>
      ) : (
        <Suspense fallback={<Loader className="animate-spin text-main" />}>
          <InputField
            type="text"
            value={String(condition.value) ?? ""}
            onChange={(e) =>
              handleChange({
                ...condition,
                value: e.target.value,
              })
            }
            placeholder="Value"
            className="flex-1 w-full"
          />
        </Suspense>
      )}
    </div>
  );
};

export default FilterInput;
