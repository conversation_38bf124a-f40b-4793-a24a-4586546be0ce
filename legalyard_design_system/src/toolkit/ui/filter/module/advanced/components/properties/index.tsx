import classNames from "classnames";
import { FC, lazy, Suspense, useState } from "react";
import { <PERSON>Right, ChevronLeft, PlusSquare } from "react-feather";
import { validArray } from "../../../../../../utils/helpers/data/array";
import Loader from "../../../../../loader";
import { FieldType, PropertiesType } from "../../types";
import { propertiesMockData } from "./data";
const FilterError = lazy(() => import("../error/no-data"));

export interface propsType {
  onClose: (d: boolean) => void;
  onSelect: (selected: FieldType) => void;
}

const Properties: FC<propsType> = ({ onClose, onSelect }) => {
  const baseData: PropertiesType[] = propertiesMockData;
  const dataDeepClone = JSON.parse(JSON.stringify(baseData));
  const [loading, setLoading] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>("");
  const [propertiesData, setPropertiesData] =
    useState<PropertiesType[]>(dataDeepClone);

  // useEffect(() => {
  //   setLoading(true);
  //   const propertyWithId = () => {
  //     const dataClone = JSON.parse(JSON.stringify(baseData ?? []));
  //     const dataWithIds = validArray(dataClone)
  //       ? dataClone?.map((item) => {
  //           const unique_id = v4();

  //           return {
  //             ...item,
  //             fields:
  //               validArray(item?.fields) &&
  //               item?.fields?.map((fld: any) => {
  //                 return { ...fld, fieldId: unique_id };
  //               }),
  //           };
  //         })
  //       : [];

  //     setPropertiesData(dataWithIds);
  //   };

  //   propertyWithId();
  //   setLoading(false);
  // }, [baseData]);

  const filteredData = (): any[] => {
    try {
      if (validArray(propertiesData)) {
        const lowercasedTerm = searchValue.toLowerCase();
        return propertiesData?.reduce<PropertiesType[]>(
          (filteredGroups, property) => {
            const filteredFields = property?.fields.filter(
              (field) =>
                field?.name?.toLowerCase()?.includes(lowercasedTerm) ||
                field?.key?.toLowerCase()?.includes(lowercasedTerm)
            );

            if (filteredFields.length > 0) {
              filteredGroups.push({ ...property, fields: filteredFields });
            }

            return filteredGroups;
          },
          []
        );
      }
      return [];
    } catch (error) {
      console.error(error);
      return [];
    }
  };
  const finalData = Boolean(searchValue) ? filteredData() : propertiesData;

  return (
    <div className="absolute top-0 right-0 bottom-0 left-0 pb-4 h-full overflow-auto">
      <div className="flex flex-col gap-2 sticky top-0 px-4 pt-4 pb-2 shadow bg-white">
        <button
          onClick={() => onClose(false)}
          className="max-w-max flex items-center gap-0.5 text-sm bg-white border border-blue pl-2 pr-3 py-1 hover:shadow hover:bg-blue hover:bg-opacity-5 rounded-base"
        >
          <ChevronLeft />
          Back
        </button>

        <input
          id="property_search"
          type="search"
          placeholder="Search Property, e.g. Name"
          className={classNames(
            "border-2 transition-all border-slate-400 focus:border-skyBlue outline-0 rounded-base w-full p-2 bg-slate-100 focus:bg-white"
          )}
          onChange={(e) => setSearchValue(e?.target?.value)}
          value={searchValue}
        />
      </div>

      <div className="my-4 px-4 pb-6">
        {loading ? (
          <Loader />
        ) : (
          <>
            {validArray(finalData) ? (
              <div className="flex flex-col gap-2">
                {finalData?.map((prp, prpIndex) => {
                  return (
                    <div key={prpIndex} className="my-2">
                      <h5 className="text-sb font-semibold mb-3">
                        {prp?.title}
                      </h5>

                      {validArray(prp?.fields) ? (
                        <div className="flex flex-col gap-1">
                          {prp?.fields?.map((fld: FieldType, index: number) => {
                            return (
                              <button
                                key={index + String(fld?.id)}
                                className="group flex items-center justify-start gap-2 bg-transparent hover:bg-blue hover:bg-opacity-10 border border-transparent hover:border-blue px-1.5 py-2 transition-all"
                                onClick={() => onSelect(fld)}
                              >
                                <PlusSquare className="hidden group-hover:block" />
                                <span className="block group-hover:hidden">
                                  {Boolean(fld?.icon) ? (
                                    <img
                                      alt="property icon"
                                      src={fld?.icon}
                                      className="w-4 h-4 object-contain"
                                    />
                                  ) : (
                                    <ArrowRight className="w-4 h-4" />
                                  )}
                                </span>

                                <h6>{fld?.name}</h6>
                              </button>
                            );
                          })}
                        </div>
                      ) : (
                        <div>NO Fields</div>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <Suspense fallback={<Loader big center />}>
                <FilterError />
              </Suspense>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Properties;
