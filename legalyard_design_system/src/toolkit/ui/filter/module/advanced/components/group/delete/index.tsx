import { Dispatch, forwardRef, Ref, SetStateAction } from "react";

interface PropsType {
  index: number;
  removeGroup: () => void;
  setDeleteOn: Dispatch<SetStateAction<boolean>>;
}

const DeletePop = forwardRef<HTMLDivElement, PropsType>(
  ({ index, removeGroup, setDeleteOn }, ref: Ref<HTMLDivElement>) => {
    return (
      <div
        ref={ref}
        className="z-[2] h-min border border-red bg-opacity-95 w-full absolute top-0 left-0 rounded-base shadow-md flex flex-col items-start"
      >
        <div className="flex flex-col items-start gap-2 bg-white w-full px-4 pt-5 pb-2">
          <h5 className="text-base font-semibold leading-tight">
            Delete Group-{index + 1}?
          </h5>
          <p className="text-sb font-normal xs:max-w-[70%] leading-5">
            Deleting this group will also delete the filters you've added to it.
          </p>
        </div>
        <div className="flex items-center justify-start gap-2 bg-white w-full p-4">
          <button
            className="bg-red border border-red hover:bg-opacity-30 text-white hover:text-fon font-semibold px-3 py-1.5 rounded-base transition-all"
            onClick={() => removeGroup()}
          >
            Delete
          </button>
          <button
            className="bg-transparent hover:bg-slate-300 border border-transparent px-2 py-1.5 transition-all"
            onClick={() => setDeleteOn(false)}
          >
            Cancel
          </button>
        </div>
      </div>
    );
  }
);

export default DeletePop;
