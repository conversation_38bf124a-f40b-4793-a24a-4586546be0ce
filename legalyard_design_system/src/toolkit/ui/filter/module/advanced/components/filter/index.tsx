import { validArray } from "../../../../../../utils/helpers/data/array";
import classNames from "classnames";
import { forwardRef, lazy, Suspense, useState } from "react";
import { Loader, Trash } from "react-feather";
import { FilterCondition } from "../../types";
import { FilterOperator } from "../../../../types";
import { filterOperators } from "./data";
const FilterInput = lazy(() => import("./input"));
const Select = lazy(() => import("../../../../../input/select"));

interface propsType {
  id: string;
  condition: FilterCondition;
  groupIndex: number;
  index: number;
  removeFilter: (groupIndex: number, filterIndex: number) => void;
  handleChange: (
    groupIndex: number,
    filterIndex: number,
    condition: FilterCondition
  ) => void;
}

const Filter = forwardRef<HTMLDivElement, propsType>(
  ({ id, condition, groupIndex, index, handleChange, removeFilter }, ref) => {
    const [filterHover, setFilterHover] = useState(false);

    const selectOptions = (): any[] => {
      try {
        return condition?.field?.operators &&
          validArray(condition?.field?.operators)
          ? condition?.field?.operators?.map?.((item: string) => {
              return {
                title: item?.toString()?.replaceAll("_", " "),
                key: item,
              };
            })
          : filterOperators;
      } catch (error) {
        return [];
      }
    };
    return (
      <div
        ref={ref}
        id={String(id)}
        className={classNames(
          "transition-all border rounded-base",
          filterHover
            ? "bg-red bg-opacity-10 border-red border-opacity-80"
            : "bg-transparent border-blue"
        )}
      >
        <div
          className={classNames(
            "flex items-center justify-between gap-3 rounded-tl-base rounded-tr-base px-2 py-0.5 transition-all",
            filterHover
              ? "bg-red bg-opacity-80 text-white"
              : "bg-sky-100 text-font"
          )}
        >
          <h5 className="text-sb font-semibold leading-tight">
            {condition?.field?.name}
          </h5>
          <button
            onMouseOver={() => setFilterHover(true)}
            onMouseLeave={() => setFilterHover(false)}
            onClick={() => removeFilter(groupIndex, index)}
            className="p-1 rounded-base border-0 outline-0 bg-transparent hover:bg-rose-50 hover:bg-opacity-95 hover:text-font"
          >
            <Trash />
          </button>
        </div>
        <div
          className={classNames(
            "p-2 transition-all rounded-bl-base rounded-br-base ",
            filterHover ? "bg-red bg-opacity-10 " : "bg-white"
          )}
        >
          <div className="flex items-center flex-col sm:flex-row flex-wrap gap-2">
            <Suspense fallback={<Loader className="animate-spin" />}>
              <Select
                value={condition?.operator}
                option={{ data: selectOptions() }}
                onChange={(e) =>
                  handleChange(groupIndex, index, {
                    ...condition,
                    operator: e.target.value as FilterOperator,
                  })
                }
                className="w-full min-w-max sm:w-4/12 sm:max-w-[50%]"
              />
            </Suspense>

            <Suspense fallback={<Loader className="animate-spin  " />}>
              <FilterInput
                condition={condition}
                handleChange={(cnd) => handleChange(groupIndex, index, cnd)}
              />
            </Suspense>
          </div>
        </div>
      </div>
    );
  }
);

export default Filter;
