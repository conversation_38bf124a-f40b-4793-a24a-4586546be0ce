import classNames from "classnames";
import { FC, ReactNode, useRef, useState } from "react";
import { Trash2 } from "react-feather";
import DeletePop from "./delete";
import useOutsideAlert from "../../../../../../utils/hooks/useOutsideAlert";

interface propsType {
  children: ReactNode;
  index: number;
  removeGroup: () => void;
}

const Group: FC<propsType> = ({ children, index, removeGroup }) => {
  const deleteRef = useRef<HTMLDivElement>(null);
  const [deleteOn, setDeleteOn] = useState<boolean>(false);
  const [deleteHover, setDeleteHover] = useState<boolean>(false);

  useOutsideAlert(deleteRef, () => setDeleteOn(false));

  return (
    <>
      <div
        className={classNames(
          "relative p-2 rounded-base transition-all",
          deleteHover ? "bg-red bg-opacity-20" : "bg-transparent"
        )}
      >
        <div className="flex items-center justify-between gap-2 mb-1">
          <h3 className="text-sm font-medium leading-tight ">
            Group-{index + 1}
          </h3>
          <button
            onMouseOver={() => setDeleteHover(true)}
            onMouseLeave={() => setDeleteHover(false)}
            onClick={() => setDeleteOn(true)}
            className="p-2 rounded-base border-0 outline-0 bg-transparent hover:bg-red hover:bg-opacity-30"
          >
            <Trash2 />
          </button>
        </div>
        {children}

        {deleteOn && (
          <DeletePop
            ref={deleteRef}
            index={index}
            removeGroup={() => {
              removeGroup();
              setDeleteOn(false);
            }}
            setDeleteOn={setDeleteOn}
          />
        )}
      </div>
      <div className="mt-1 mb-0 w-full mx-auto">
        <div className="border-t border-slate-400 flex items-center justify-center">
          <span className="inline-block -translate-y-1/2 bg-white px-3 py-[1px] rounded-base text-sm font-semibold">
            OR
          </span>
        </div>
      </div>
    </>
  );
};

export default Group;
