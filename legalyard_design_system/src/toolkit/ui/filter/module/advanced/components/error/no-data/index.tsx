import Placeholder from "../.././../../../../../assets/placeholder/2.svg";
import Image from "../../../../../../image";

const FilterError = () => {
  return (
    <div className="w-full min-h-56 h-auto max-h-max mt-[1px] py-5 px-4">
      <div className="w-full max-w-max sm:max-w-[75%] md:max-w-[50%] h-full mx-auto relative top-5 pb-6">
        <div className="w-full h-full flex flex-col items-center gap-5">
          <Image
            src={Placeholder}
            className="max-h-36 w-auto object-contain text-center"
          />
          <div className="flex flex-col items-center gap-3">
            <h2 className="teko_font text-2xl">Oops, No Matches!</h2>
            <p className="text-center">
              It looks like there are no results for current filter. Consider
              modifying your filter terms for better results.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterError;
