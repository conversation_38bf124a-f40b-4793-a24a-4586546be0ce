import { PropertiesType } from "../../types";

export const propertiesMockData: PropertiesType[] = [
  {
    title: "Contact Fields",
    fields: [
      {
        _id: "4856",
        name: "Assistant",
        key: "contact.contact_assistant",
        icon: "https://www.svgrepo.com/show/137590/email.svg",
        operators: ["contains", "does_not_contain"],
      },
      {
        _id: "4856",
        name: "Contact Owner",
        key: "contact.contact_contact_owner",
      },
      {
        _id: "4856",
        name: "Date of Birth",
        key: "contact.contact_dob",
      },
      {
        _id: "4856",
        name: "<PERSON><PERSON>",
        key: "contact.contact_email",
      },
    ],
  },
  {
    title: "Contact Activity",
    fields: [
      {
        _id: "4856",
        name: "Assistant",
        key: "contact.contact_assistant",
      },
      {
        _id: "4856",
        name: "Contact Owner",
        key: "contact.contact_contact_owner",
      },
      {
        _id: "4856",
        name: "Date of Birth",
        key: "contact.contact_dob",
      },
      {
        _id: "4856",
        name: "<PERSON>ail",
        key: "contact.contact_email",
      },
      {
        _id: "4856",
        name: "Extra Field",
        key: "contact.contact_extra_field",
      },
    ],
  },
  {
    title: "Contact Activity",
    fields: [
      {
        _id: "4856",
        name: "Assistant",
        key: "contact.contact_assistant",
      },
      {
        _id: "4856",
        name: "Contact Owner",
        key: "contact.contact_contact_owner",
      },
      {
        _id: "4856",
        name: "Date of Birth",
        key: "contact.contact_dob",
      },
      {
        _id: "4856",
        name: "Email",
        key: "contact.contact_email",
      },
      {
        _id: "4856",
        name: "Extra Field",
        key: "contact.contact_extra_field",
      },
    ],
  },
  {
    title: "Contact Activity",
    fields: [
      {
        _id: "4856",
        name: "Assistant",
        key: "contact.contact_assistant",
      },
      {
        _id: "4856",
        name: "Contact Owner",
        key: "contact.contact_contact_owner",
      },
      {
        _id: "4856",
        name: "Date of Birth",
        key: "contact.contact_dob",
      },
      {
        _id: "4856",
        name: "Email",
        key: "contact.contact_email",
      },
      {
        _id: "4856",
        name: "Extra Field",
        key: "contact.contact_extra_field",
      },
    ],
  },
];
