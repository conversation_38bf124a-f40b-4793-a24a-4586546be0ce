import classNames from "classnames";
import { forwardRef, Ref } from "react";
import { FilterByFieldsType } from "./types";
import { DataFilterReferenceType } from "../../types";

const FilterByFields = forwardRef(
  (
    { data, onFilter, className }: FilterByFieldsType,
    ref: Ref<DataFilterReferenceType>
  ) => {
    return <div className={classNames(className)}>FilterByFields</div>;
  }
);

export type { DataFilterReferenceType };

export default FilterByFields;
