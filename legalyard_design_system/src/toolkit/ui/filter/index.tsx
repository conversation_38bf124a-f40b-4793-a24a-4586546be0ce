import { forwardRef, ForwardRefRenderFunction, Ref } from "react";
import ActivityFilter from "./module/activity";
import AdvancedFilter from "./module/advanced";
import { AdvanceFilterType } from "./module/advanced/types";
import { DataFilterReferenceType } from "./types";
import FilterByFields from "./module/fields";
import { FilterByFieldsType } from "./module/fields/types";

type Combine = AdvanceFilterType & FilterByFieldsType;

interface FilterType extends Combine {
  variant: "advanced" | "fields" | "activity";
}

const DataFilter: ForwardRefRenderFunction<
  DataFilterReferenceType,
  FilterType
> = ({ variant, ...rest }, ref: Ref<DataFilterReferenceType>) => {
  return (
    <>
      {variant === "advanced" ? (
        <AdvancedFilter ref={ref} {...rest} />
      ) : variant === "activity" ? (
        <ActivityFilter />
      ) : (
        <FilterByFields ref={ref} {...rest} />
      )}
    </>
  );
};

export default forwardRef(DataFilter);
