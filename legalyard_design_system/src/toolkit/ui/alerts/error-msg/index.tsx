import { FC } from "react";
import Alerts from "..";

interface errorType {
  message?: string;
}

interface propsGroup {
  error?: string | object | any[];
  box?: boolean;
  close?: boolean;
}

const ErrorMessage: FC<propsGroup> = ({ error, box, close }) => {
  return (
    <>
      {Array.isArray(error) && error?.length > 0 ? (
        <>
          {error?.map((err: errorType, index: number) => {
            return (
              <Alerts
                key={index}
                title={err?.message}
                error
                box={box}
                close={close}
              />
            );
          })}
        </>
      ) : typeof error === "object" && error.hasOwnProperty("message") ? (
        <>
          <Alerts
            title={
              (error && "message" in error && (error as any).message) ||
              undefined
            }
            error
            box={box}
            close={close}
          />
        </>
      ) : typeof error === "string" || typeof error === "number" ? (
        <>
          <Alerts
            title={String(error) || undefined}
            error
            box={box}
            close={close}
          />
        </>
      ) : (
        <Alerts title="Something went wrong" error box={box} close={close} />
      )}
    </>
  );
};

export default ErrorMessage;
