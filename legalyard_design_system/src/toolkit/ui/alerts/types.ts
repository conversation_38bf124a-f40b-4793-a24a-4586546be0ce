export interface propsType {
  title?: string;
  text?: string | boolean;
  type?: string;
  icon?: any;
  success?: boolean;
  error?: boolean;
  warning?: boolean;
  info?: boolean;
  autoRemove?: boolean | number;
  box?: boolean;
  fixed?: boolean;
  topRight?: boolean;
  topLeft?: boolean;
  bottomRight?: boolean;
  bottomLeft?: boolean;
  close?: boolean;
  onRemove?: () => void | null;
  className?: string;
}

export interface styles {
  $isTitle?: boolean;
  $bold?: boolean;
  $alertType?: any;
  $box?: boolean;
  $fixed?: boolean;
  $topRight?: boolean;
  $topLeft?: boolean;
  $bottomRight?: boolean;
  $bottomLeft?: boolean;
  $text?: boolean;
}

export type StyledType = propsType & styles;
