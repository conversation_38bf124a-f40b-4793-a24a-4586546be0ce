import { FC, useState, useEffect, useRef } from "react";
import { propsType } from "./types";
import classNames from "classnames";
import {
  CheckCircle,
  AlertTriangle,
  Info,
  AlertOctagon,
  X,
} from "react-feather";

import * as Styles from "./styles";

const Alerts: FC<propsType> = ({
  title,
  text,
  icon,
  type,
  success,
  error,
  warning,
  info,
  autoRemove,
  box,
  fixed,
  topRight,
  topLeft,
  bottomRight,
  bottomLeft,
  close,
  onRemove,
  className,
}) => {
  const [show, setShow] = useState(false);

  const defaultIcon =
    success || type === "success" ? (
      <CheckCircle />
    ) : error || type === "error" ? (
      <AlertOctagon />
    ) : warning || type === "warning" ? (
      <AlertTriangle />
    ) : info || type === "info" ? (
      <Info />
    ) : null;

  const timeoutId = useRef<NodeJS.Timeout>();

  useEffect(() => {
    try {
      if (<PERSON>ole<PERSON>(title) || <PERSON><PERSON><PERSON>(text)) {
        setShow(true);
      } else {
        setShow(false);
      }

      const time = typeof autoRemove === "number" ? autoRemove : 6000;

      if (<PERSON><PERSON>an(autoRemove)) {
        timeoutId.current = setTimeout(() => {
          setShow(false);
          if (onRemove) {
            onRemove();
          }
        }, time);
      }
    } catch (err) {
      console.error(err);
    }

    return () => {
      clearTimeout(timeoutId.current);
    };
  }, [autoRemove, onRemove, text, title]);

  const handleRemove = () => {
    try {
      clearTimeout(timeoutId.current);
      setShow(false);
      if (onRemove) {
        onRemove();
      }
    } catch (err) {
      console.error(err);
    }
  };

  if (!show) {
    return null;
  }

  return (
    <Styles.Container
      aria-label="Alerts"
      className={classNames("alerts animate-bounce-short", className)}
      $isTitle={Boolean(title)}
      $alertType={{
        success: Boolean(success) || type === "success",
        error: Boolean(error) || type === "error",
        warning: Boolean(warning) || type === "warning",
        info: Boolean(info) || type === "info",
      }}
      $box={box}
      $fixed={fixed}
      $topRight={topRight}
      $topLeft={topLeft}
      $bottomRight={bottomRight}
      $bottomLeft={bottomLeft}
    >
      <Styles.Wrapper>
        <Styles.IconWrapper>{icon ?? defaultIcon}</Styles.IconWrapper>
        <Styles.TextWrapper $text={Boolean(text)}>
          {title && <Styles.TextHolder $bold>{title}</Styles.TextHolder>}
          {text && <Styles.TextHolder>{text}</Styles.TextHolder>}
        </Styles.TextWrapper>
      </Styles.Wrapper>
      {Boolean(close) && (
        <Styles.CloseWrapper onClick={() => handleRemove()}>
          <X />
        </Styles.CloseWrapper>
      )}
    </Styles.Container>
  );
};

export default Alerts;
