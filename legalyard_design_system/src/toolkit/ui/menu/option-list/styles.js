import styled from "styled-components";

export const Container = styled.div`
  background-color: ${({ theme: { color } }) => color.white};
  padding: 5px 0;
  width: max-content;
`;

export const ListHolder = styled.div`
  width: 100%;
  ${({ theme: { placeholders } }) => placeholders.flex};
  align-items: flex-start;
  flex-direction: column;
  gap: 5px;
`;

export const ListItem = styled.div`
  width: 100%;
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  gap: 10px;
  padding: 12px 20px;
  cursor: pointer;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    background-color: ${({ theme: { color } }) => color.main};
    color: ${({ theme: { color } }) => color.white};
  }
`;
