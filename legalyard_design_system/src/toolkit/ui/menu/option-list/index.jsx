import * as Styles from "./styles";

const OptionListMenu = ({ data }) => {
  return (
    <Styles.Container>
      <Styles.ListHolder>
        {Array.isArray(data) && data?.length > 0 && (
          <>
            {data?.map((item, index) => {
              return (
                <Styles.ListItem
                  key={index}
                  onClick={() => item?.handleClick() || null}
                >
                  {item?.icon}
                  {item?.name}
                </Styles.ListItem>
              );
            })}
          </>
        )}
      </Styles.ListHolder>
    </Styles.Container>
  );
};

export default OptionListMenu;
