import styled from "styled-components";

export const Container = styled.div`
  position: relative;
  display: flex;
  max-width: max-content;

  margin-inline: ${({ center }) => (center ? "auto" : "unset")};
`;

export const Wrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  ${({ button }) => button && "gap: 20px"};
`;

export const PreviewWrapper = styled.div`
  height: 150px;
  width: 150px;
  border-radius: 100%;
  border: 1px solid ${({ theme: { color } }) => color.main};
  overflow: hidden;
  background-color: #e4e4e4;
`;

export const PreviewHolder = styled.img`
  height: 100%;
  width: 100%;
  object-fit: cover;
`;

export const UploadWrapper = styled.div`
  ${({ button }) =>
    !button &&
    `
  position: absolute;
  left: 50%;
  transform: translate(-50%, 50%);
  bottom: 0;
  `}
`;

export const UploadHolder = styled.input`
  display: none;
`;

export const LabelHolder = styled.label`
  cursor: pointer;
  border: 1px solid ${({ theme: { color } }) => color.main};
  border-radius: 100px;
  background-color: ${({ theme: { color } }) => color.main};
  box-shadow: 0 0 10px ${({ theme: { color } }) => `${color.sub}80`};
`;

export const ActionWrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flex};
  flex-direction: column;
  gap: 20px;
`;

export const ActionHolder = styled.div`
  padding: 10px;
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  justify-content: center;
  color: #fff;

  svg {
    width: 25px;
    height: 25px;
    color: #fff;
  }
`;

export const NoteHolder = styled.p`
  font-size: ${({ theme: { font } }) => font.sub};
  color: ${({ theme: { color } }) => color.grey};
`;
