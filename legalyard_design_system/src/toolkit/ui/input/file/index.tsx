import {
  ChangeEvent,
  forwardRef,
  ForwardRefRenderFunction,
  Ref,
  useState,
} from "react";
import { CheckCircle, Upload } from "react-feather";
import InputIcon from "../components/icon";
import InputLabel from "../components/label";
import InputWrapper from "../components/wrapper";
import InputError from "../components/error";
import { DocumentUploadTypes } from "./types";

import classNames from "classnames";

const FileInput: ForwardRefRenderFunction<
  HTMLInputElement,
  DocumentUploadTypes
> = (props, ref: Ref<HTMLInputElement>) => {
  const {
    id,
    label,
    onChange,
    getEvent,
    attributes,
    disabled,
    placeholder,
    multiple,
    ...rest
  } = props;

  const [hovered, setHovered] = useState<boolean>(false);
  const [error, setError] = useState<string | null>("");
  const [uploaded, setUploaded] = useState<boolean>(false);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      e.preventDefault();
      e.stopPropagation();
      const name = e.target.namespaceURI;
      const files = e.target.files;
      setUploaded(true);
      setError("");
      if (onChange) {
        onChange({
          target: { type: "file", name: name || "file", value: files },
        });
      }
      if (getEvent) {
        getEvent(e);
      }

      setTimeout(() => {
        setUploaded(false);
      }, 2000);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div
      aria-label="file"
      className="file w-full relative my-1 select-none"
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {Boolean(label) && !Boolean(label?.dynamic) && (
        <InputLabel {...(label ?? {})} focused={hovered} />
      )}

      <div
        className="relative select-none outline-none focus:outline-none"
        tabIndex={0}
      >
        <div className="relative w-full h-full">
          {Boolean(label?.dynamic) && (
            <InputLabel {...(label ?? {})} focused={hovered} />
          )}

          <InputWrapper
            error={Boolean(error)}
            valid={uploaded}
            focused={hovered}
            className="overflow-hidden"
          >
            <InputIcon
              type={"select"}
              focused={hovered}
              icon={{
                ...attributes?.prefix?.icon,
                src: attributes?.prefix?.icon?.src ?? <Upload />,
              }}
              prefix
            />

            <input
              ref={ref}
              type="file"
              id={id || "file-upload"}
              onChange={handleChange}
              multiple={multiple}
              disabled={disabled}
              className="hidden"
              {...rest}
            />

            <label
              id={`label-${id || "file-upload"}`}
              htmlFor={id || "file-upload"}
              className={classNames(
                "group cursor-pointer py-1.5 ps-1.5 w-full flex-1 flex items-center justify-between gap-2 transition-all",
                disabled &&
                  "pointer-events-none bg-grey bg-opacity-10 shadow-inner"
              )}
            >
              <span className="flex-1 capitalize text-start text-sm">
                {placeholder ?? `Select file${multiple ? "s" : ""}`}
              </span>

              <InputIcon
                focused={hovered}
                value={Boolean(uploaded)}
                icon={{
                  ...attributes?.suffix?.icon,
                  src: attributes?.suffix?.icon?.src ?? (
                    <span
                      className={classNames(
                        "block w-20 bg-sub group-hover:bg-main px-4 py-1 text-white text-sm text-center rounded-base leading-4 transition-all",
                        uploaded && "!bg-green !text-white"
                      )}
                    >
                      {uploaded ? <CheckCircle className="m-auto" /> : "Upload"}
                    </span>
                  ),
                  className:
                    attributes?.suffix?.icon?.className ??
                    "!mr-0.5 pointer-events-none",
                }}
                suffix
              />
            </label>
          </InputWrapper>
        </div>
      </div>
      {error && <InputError title={error}>{error}</InputError>}
    </div>
  );
};

export default forwardRef(FileInput);
