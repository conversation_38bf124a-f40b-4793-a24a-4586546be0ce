import {
  ChangeEvent,
  DragEvent,
  forwardRef,
  ForwardRefRenderFunction,
  Ref,
  useCallback,
  useState,
} from "react";
import { Image, PlusCircle } from "react-feather";
import { DocumentUploadTypes } from "../types";
import classNames from "classnames";

const DocumentUpload: ForwardRefRenderFunction<
  HTMLInputElement,
  DocumentUploadTypes
> = (
  {
    id,
    onChange,
    getEvent,
    action,
    hideAction,
    multiple,
    accept,
    actionTitle,
    name,
    ...rest
  },
  ref: Ref<HTMLInputElement>
) => {
  const [dragActive, setDragActive] = useState(false);

  const handleCallback = useCallback(
    (inputName: string, files: FileList | URL[] | null) => {
      try {
        if (onChange) {
          onChange({
            target: {
              type: "file",
              name: inputName || name || "file",
              value: files,
            },
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    [name, onChange]
  );

  const handleDrag = (e: DragEvent<HTMLInputElement>) => {
    try {
      e.preventDefault();
      e.stopPropagation();
      if (e?.type === "dragenter" || e?.type === "dragover") {
        setDragActive(true);
      } else if (e?.type === "dragleave") {
        setDragActive(false);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleDrop = (e: DragEvent<HTMLInputElement>) => {
    try {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);
      const files = e.dataTransfer.files;
      var imageUrl = e.dataTransfer.getData("URL");
      if (files && files[0]) {
        handleCallback("", files);
      } else if (imageUrl) {
        const imgURL = new URL(imageUrl);
        handleCallback("", [imgURL]);
      }
      if (getEvent) {
        getEvent(e);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      e.preventDefault();
      e.stopPropagation();
      const name = e?.target?.name;
      const files = e?.target?.files;
      handleCallback(name, files);

      if (getEvent) {
        getEvent(e);
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div
      aria-label="document upload"
      className="w-full relative"
      onDragEnter={handleDrag}
    >
      <input
        ref={ref}
        type="file"
        name={name ?? "file"}
        id={id || "document-upload"}
        onChange={handleChange}
        accept={
          accept ||
          "image/png, image/jpeg, image/jpg, image/webp, application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        }
        multiple={multiple}
        className="hidden"
        {...rest}
      />

      {!hideAction && (
        <>
          {action ? (
            <label
              id={`label-${id || "document-upload"}`}
              htmlFor={id || "document-upload"}
              className="cursor-pointer border-0 bg-transparent"
            >
              {action}
            </label>
          ) : (
            <>
              <label
                id={`label-${id || "document-upload"}`}
                htmlFor={id || "document-upload"}
                className={classNames(
                  "cursor-pointer h-full py-7 flex flex-col items-center justify-center border-2 border-dashed border-slate-200 rounded-xl bg-transparent transition-all bg-white hover:bg-slate-100",
                  dragActive && "!bg-slate-100"
                )}
              >
                <div>
                  {actionTitle || "Drag and drop your file here or browse"}
                </div>

                <div className="cursor-pointer w-fit m-auto relative mt-5">
                  <Image className="w-12 h-12 text-zinc-400" />
                  <div className="absolute bottom-0 right-0 translate-x-1/3 translate-y-1/3">
                    <PlusCircle className="w-9 h-9 fill-zinc-400 text-white" />
                  </div>
                </div>
                <div
                  className="cursor-pointer absolute w-full h-full rounded-xl top-0 right-0 bottom-0 left-0"
                  id={`drag-file-element-${id}` || "drag-file-element"}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                />
              </label>
            </>
          )}
        </>
      )}
    </div>
  );
};

export default forwardRef(DocumentUpload);
