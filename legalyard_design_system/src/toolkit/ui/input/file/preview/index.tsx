import classNames from "classnames";
import {
  CSSProperties,
  SyntheticEvent,
  useCallback,
  useEffect,
  useState,
} from "react";
import Placeholder from "../../../../assets/placeholder/image.png";
import LightBox from "../../../light-box";
import Loader from "../../../loader";
import ImageRender from "../../../image";

const ImagePreview = ({
  data,
  lightBox,
  className,
  wrapperClass,
  imageWidth,
  imageHight,
}: {
  data?: any;
  lightBox?: boolean;
  wrapperClass?: string;
  className?: string;
  imageWidth?: number;
  imageHight?: number;
}) => {
  const [currentImgSrc, setCurrentImgSrc] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [lightBoxOpen, setLightBoxOpen] = useState<boolean>(false);

  const handleLightBox = () => {
    try {
      setLightBoxOpen(!lightBoxOpen);
    } catch (error) {
      console.error({ error });
    }
  };

  const handleError = useCallback(
    (e: SyntheticEvent<HTMLImageElement, Event>) => {
      try {
        const imgElement = e.target as HTMLImageElement;
        imgElement.src = Placeholder;
        setIsLoading(false);
      } catch (err) {
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    try {
      if (data) {
        if (typeof data === "string") {
          setCurrentImgSrc(data);
        } else if (typeof data === "object" && data?.name) {
          const imgUrl = window?.URL?.createObjectURL(data);
          setCurrentImgSrc(imgUrl);
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }, [data]);

  const finalStyle = (): CSSProperties => {
    try {
      const styles: any = {};
      if (imageWidth) styles.width = imageWidth;
      if (imageHight) styles.height = imageHight;
      return styles;
    } catch (error) {
      return {};
    }
  };

  if (isLoading) {
    return <Loader />;
  }

  return (
    <>
      <div
        aria-label="image-preview"
        className={classNames(
          "image_preview w-full h-full rounded-base border border-slate-300 shadow overflow-hidden",
          className
        )}
      >
        <div
          role="button"
          className={classNames(
            "image_preview_wrapper relative w-full h-full align-middle flex justify-center items-center cursor-pointer",
            wrapperClass
          )}
          onClick={() => handleLightBox()}
          {...(imageHight || imageWidth ? { style: finalStyle() } : {})}
        >
          <ImageRender
            src={currentImgSrc}
            alt="preview"
            className="block max-w-full max-h-full w-full h-full align-middle bg-slate-200 text-center object-cover"
            onError={handleError}
          />
        </div>
      </div>

      {lightBox && lightBoxOpen && (
        <LightBox
          src={currentImgSrc}
          title={data?.name}
          open={lightBoxOpen}
          onClose={handleLightBox}
        />
      )}
    </>
  );
};

export default ImagePreview;
