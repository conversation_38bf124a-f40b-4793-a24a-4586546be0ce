import { InputHTMLAttributes, ReactNode } from "react";
import { InputLabelType } from "../components/label";

export interface SelectChangeEvent {
  target: {
    value: string | boolean | any[];
    type: string;
    name: string;
    selected: OptionDataType;
  };
}

export type MultipleSelectedType = OptionDataType[];
export type SelectedType = OptionDataType | null;

export interface InputAttributeIconTypes {
  show?: boolean;
  src?: string | ReactNode;
  className?: string;
  name?: string;
  tooltip?: {
    show?: boolean;
    name?: string;
    direction?: string;
  };
  onClick?: (d?: any) => void;
}

export interface InputAttributesTypes {
  prefix?: {
    icon: InputAttributeIconTypes;
    className?: string;
  };
  suffix?: {
    icon: InputAttributeIconTypes;
    className?: string;
  };
}

export interface GroupOptionDataType {
  group?: string;
  data?: OptionDataType[];
}

export interface OptionDataType {
  value: string;
  title: string;
  subTitle?: string;
  icon?: string | ReactNode;
  [key: string]: any;
}

type OptionsType<T> = T extends { group: true }
  ? GroupOptionDataType[]
  : OptionDataType[];

export interface OptionType {
  data?: OptionsType<this>;
  group?: boolean;
  hideIcon?: boolean;
  search?: boolean;
  checkbox?: boolean;
  removeSelected?: boolean;
  divideCamelCase?: boolean;
  dataStart?: number;
  dataEnd?: number;
  addNew?: boolean;
  addNewTitle?: string;
  errorMessage?: string;
  lastOption?: OptionDataType;
  props?: {
    [key: string]: any;
  };
}

type SelectInputValue<T> = T extends { multiple: true }
  ? OptionDataType[]
  : string | number | OptionDataType;

export interface SelectInputType
  extends Omit<InputHTMLAttributes<HTMLSelectElement>, "value" | "multiple"> {
  value?: SelectInputValue<this>;
  multiple?: boolean;
  loading?: boolean;
  id?: string;
  name?: string;
  onChange?: (data: any) => void;
  attributes?: InputAttributesTypes;
  option?: OptionType;
  inputProps?: InputHTMLAttributes<HTMLInputElement>;
  label?: InputLabelType;
  reset?: boolean;
  error?: string;
  disabled?: boolean;
  withInput?: boolean;
  className?: string;
}
