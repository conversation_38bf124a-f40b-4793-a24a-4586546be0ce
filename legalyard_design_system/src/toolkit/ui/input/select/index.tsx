import classNames from "classnames";
import { forwardRef, Ref } from "react";
import { ErrorBoundary } from "react-error-boundary";
import InputError from "../components/error";
import InputIcon from "../components/icon";
import InputLabel from "../components/label";
import InputWrapper from "../components/wrapper";
import useSelectInput from "./@context";
import SelectInputProvider from "./@context/provider";
import Actions from "./components/actions";
import SelectDropdown from "./components/dropdown";
import SelectInputBox from "./components/input";
import MultipleSelectBox from "./components/multiple-select";
import SelectBox from "./components/select-box";
import { SelectInputType } from "./types";
import Loader from "../../loader";

// TODO
// lazy loading / onScroll load more options

const SelectInputComp = forwardRef<HTMLSelectElement, SelectInputType>(
  (
    {
      className,
      label,
      loading,
      placeholder,
      error,
      attributes,
      inputProps,
      withInput,
      reset,
      disabled,
      option,
      multiple,
      ...rest
    },
    ref: Ref<HTMLSelectElement>
  ) => {
    const {
      state: { selected, inputValue, focused, open, multipleSelected },
      refs: { containerRef, wrapperRef },
    } = useSelectInput();

    return (
      <div
        aria-label="Select"
        className={classNames(
          "select relative w-full my-1 select-none",
          className
        )}
        ref={containerRef}
      >
        {Boolean(label) && !Boolean(label?.dynamic) && (
          <InputLabel
            {...(label ?? {})}
            focused={
              open || focused || Boolean(selected) || Boolean(inputValue)
            }
          />
        )}

        <div
          className="relative select-none outline-none focus:outline-none"
          ref={wrapperRef}
          tabIndex={0}
        >
          <div className="relative w-full h-full">
            {Boolean(label?.dynamic) && (
              <InputLabel
                {...(label ?? {})}
                focused={
                  open || focused || Boolean(selected) || Boolean(inputValue)
                }
              />
            )}
            <InputWrapper
              error={Boolean(error)}
              valid={Boolean(selected) && Boolean(!error)}
              focused={open || focused}
              className={classNames(
                multiple && multipleSelected?.length && "h-max ps-1.5 pt-1 pb-1"
              )}
            >
              {loading ? (
                <Loader variant="skeleton" count={1} height={28} />
              ) : (
                <>
                  {attributes?.prefix?.icon && (
                    <InputIcon
                      type={"select"}
                      focused={focused}
                      icon={attributes?.prefix?.icon}
                      prefix
                    />
                  )}
                  {multiple ? (
                    <MultipleSelectBox />
                  ) : withInput ? (
                    <SelectInputBox />
                  ) : (
                    <SelectBox />
                  )}

                  <Actions />

                  {attributes?.suffix?.icon && (
                    <InputIcon
                      focused={focused}
                      value={Boolean(selected)}
                      icon={attributes?.suffix?.icon}
                      suffix
                    />
                  )}
                </>
              )}
            </InputWrapper>
          </div>

          {open && <SelectDropdown />}
        </div>

        {error && <InputError title={error}>{error}</InputError>}
      </div>
    );
  }
);

const SelectInput = forwardRef<HTMLSelectElement, SelectInputType>(
  ({ ...rest }, ref) => {
    return (
      <ErrorBoundary fallback={<div>Something went wrong with Select</div>}>
        <SelectInputProvider {...rest}>
          <SelectInputComp ref={ref} {...rest} />
        </SelectInputProvider>
      </ErrorBoundary>
    );
  }
);

SelectInput.displayName = "SelectInput";

export * from "./types";
export default SelectInput;
