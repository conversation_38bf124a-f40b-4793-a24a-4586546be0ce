import classNames from "classnames";
import { X } from "react-feather";
import useTagSelect from "../../../@context";
import { OptionDataType } from "../../../types";
import OptionIcon from "../../dropdown/options/list/components/option/icon";

const AddedTag = ({
  tag,
  hideIcon,
}: {
  tag: OptionDataType;
  hideIcon?: boolean;
}) => {
  const {
    helper: { handleRemoveOption },
  } = useTagSelect();

  return (
    <div
      className={classNames(
        "w-max flex items-center justify-center gap-1.5 text-ellipsis whitespace-nowrap overflow-hidden",
        "text-sb bg-grey bg-opacity-10 border border-grey border-opacity-10 rounded-base",
        "py-1 px-1.5 my-0 max-1 leading-3"
      )}
    >
      {!hideIcon && tag?.icon && (
        <OptionIcon title={tag?.title} icon={tag?.icon} className="!max-w-5" />
      )}
      {tag?.title}
      <X
        className="cursor-pointer w-3 h-3 text-font hover:text-main"
        onClick={() => handleRemoveOption(tag)}
      />
    </div>
  );
};

export default AddedTag;
