import useSelectInput from "../../../@context";

const OptionSearch = () => {
  const {
    state: { optionSearchValue, setOptionSearchValue },
  } = useSelectInput();

  return (
    <input
      className="border outline-0 w-full p-1.5 text-sm border-b-slate-300 border-t-transparent border-x-transparent"
      type="search"
      name="search"
      placeholder="Search..."
      onChange={(e) => setOptionSearchValue(e?.target?.value)}
      value={optionSearchValue ?? ""}
      autoComplete="off"
      autoFocus
    />
  );
};

export default OptionSearch;
