import { validArray } from "../../../../../../utils/helpers/data/array";
import classNames from "classnames";
import useSelectInput from "../../../@context";
import OptionError from "./error";
import AddNew from "./list/components/add-new";
import LastOption from "./list/components/last-option";
import OptionList from "./list";

const SelectOptions = () => {
  const {
    state: { selected, inputValue, multipleSelected },
    helper: { finalData, getSearchValue },
    option: {
      data,
      props,
      errorMessage,
      lastOption,
      addNew,
      removeSelected,
    } = {},
    multiple,
    withInput,
  } = useSelectInput();

  const matchToLastOption = (): boolean => {
    try {
      if (selected) {
        return (
          lastOption?.title
            ?.toString()
            ?.toLowerCase()
            ?.includes(selected?.title?.toString()?.toLowerCase()) ?? false
        );
      } else {
        const searchValue = getSearchValue();
        return (
          lastOption?.title
            ?.toString()
            ?.toLowerCase()
            ?.includes(searchValue?.toString()?.toLowerCase()) ?? false
        );
      }
    } catch (error) {
      return false;
    }
  };
  const isAllSelected =
    validArray(data) &&
    multiple &&
    removeSelected &&
    multipleSelected?.length >= data?.length;
  const renderData = finalData();
  return (
    <div
      aria-label="options"
      className={classNames(
        "option_list h-full max-h-40 py-1.5 overflow-y-auto overflow-x-hidden"
      )}
      {...(props ?? {})}
    >
      {validArray(data) ? (
        <>
          {validArray(renderData) ? (
            <>
              <OptionList />
              {lastOption && <LastOption />}
            </>
          ) : !validArray(renderData) && matchToLastOption() ? (
            <>
              <LastOption />
            </>
          ) : !validArray(renderData) && addNew && withInput && inputValue ? (
            <>
              <AddNew />
              <OptionError message="Search not found" />
            </>
          ) : (
            <>
              <OptionError
                message={
                  isAllSelected ? "All options selected" : "Search not found"
                }
              />
            </>
          )}
        </>
      ) : (
        <OptionError message={errorMessage ?? "No data available"} />
      )}
    </div>
  );
};

export default SelectOptions;
