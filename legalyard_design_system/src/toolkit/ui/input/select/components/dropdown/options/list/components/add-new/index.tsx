import useSelectInput from "../../../../../../@context";
import OptionItem from "../option";

const AddNew = () => {
  const {
    state: { inputValue },
    helper: { handleOptionSelect },
    option: { addNewTitle = "" } = {},
  } = useSelectInput();

  const newOption = {
    title: inputValue ?? "",
    value: inputValue ?? "",
  };

  return (
    <OptionItem
      option={newOption}
      blink={false}
      isSelected={false}
      onClick={() => handleOptionSelect(newOption ?? null)}
      text={newOption?.title}
      note={Boolean(addNewTitle) ? addNewTitle : "Continue with"}
    />
  );
};

export default AddNew;
