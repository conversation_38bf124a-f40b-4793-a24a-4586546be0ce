import Placeholder from "../../../../../../../../../../assets/placeholder/image.png";
import classNames from "classnames";
import { createElement, Fragment, ReactNode } from "react";

const OptionIcon = ({
  title,
  icon,
  className,
}: {
  title?: string;
  icon: string | ReactNode;
  className?: string;
}) => {
  if (!icon) return null;

  return (
    <>
      {typeof icon === "string" ? (
        <img
          src={icon ?? Placeholder}
          alt={title ?? "option-icon"}
          className={classNames(
            "w-full max-w-8 h-auto rounded-full aspect-square bg-slate-100",
            className
          )}
          onError={(e) => {
            try {
              const imgElement = e.target as HTMLImageElement;
              imgElement.src = Placeholder;
            } catch (error) {
              console.error(error);
            }
          }}
        />
      ) : (
        <>
          {createElement(
            "span",
            {
              className,
            },
            icon
          )}
        </>
      )}
    </>
  );
};

export default OptionIcon;
