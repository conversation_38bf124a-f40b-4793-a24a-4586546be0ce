import useSelectInput from "../../../../../@context";
import StandardOptionList from "../standard";

const GroupList = () => {
  const {
    helper: { finalData },
    option: { data, dataStart = 0, dataEnd = data?.length || 0 } = {},
  } = useSelectInput();

  const renderData = finalData();

  return (
    <>
      {renderData?.slice(dataStart, dataEnd)?.map((item, index) => {
        return (
          <div key={index + String(item?.group)}>
            <div className={"flex items-center gap-[1px]"}>
              <h6 className="min-w-min text-xs font-normal text-zinc-500 my-1 ps-4 pe-2 text-ellipsis overflow-hidden whitespace-nowrap">
                {item?.group}
              </h6>
              <span
                aria-label="title border"
                className="mt-[1px] flex-1  min-w-1 h-[1px] bg-slate-100"
              />
            </div>
            <StandardOptionList optionData={item?.data} />
          </div>
        );
      })}
    </>
  );
};

export default GroupList;
