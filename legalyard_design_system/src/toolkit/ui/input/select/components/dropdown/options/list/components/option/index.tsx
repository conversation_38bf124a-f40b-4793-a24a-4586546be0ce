import CheckboxInput from "../../../../../../../../input/checkbox/single";
import { OptionDataType } from "../../../../../../../../input/select/types";
import classNames from "classnames";
import { FC } from "react";
import OptionIcon from "./icon";

interface PropsType {
  option?: OptionDataType;
  blink: boolean;
  multiple?: boolean;
  hideIcon?: boolean;
  isSelected: boolean;
  checkbox?: boolean;
  onClick: () => void;
  text?: string;
  note?: string;
  className?: string;
}

const OptionItem: FC<PropsType> = ({
  option,
  blink,
  multiple,
  hideIcon,
  isSelected,
  onClick,
  text,
  className,
  note,
  checkbox,
}) => {
  const handleCheck = () => {
    onClick();
  };

  return (
    <div
      role="button"
      title={option?.title}
      className={classNames(
        "text-sb cursor-pointer py-2 px-5 my-0.5 bg-white text-font capitalize w-full whitespace-nowrap text-ellipsis overflow-hidden hover:bg-main hover:bg-opacity-15 transition-all",
        "flex items-start gap-x-2",
        blink && "!bg-sky-100",
        isSelected && "!bg-main text-white",
        multiple && "!bg-opacity-20 !text-font  hover:!bg-opacity-15",
        !hideIcon && option?.icon && "!items-center",
        className
      )}
      onKeyDown={(e) => e?.stopPropagation()}
      onClick={() => handleCheck()}
    >
      {checkbox && (
        <CheckboxInput
          checked={isSelected}
          className="!my-0"
          inputClassName="!w-4 !h-4"
        />
      )}

      {!hideIcon && option?.icon && (
        <OptionIcon title={text} icon={option?.icon} />
      )}

      <div className="flex flex-col items-start gap-y-0.5">
        {note && (
          <span className="text-xs block w-full mb-[0.1rem]">
            {note}
          </span>
        )}

        <span className={classNames(checkbox && !option?.subTitle && "mt-0.5")}>
          {text ?? ""}
        </span>

        {option?.subTitle && (
          <span className="mt-[0.15rem] text-[0.65rem] block w-full mb-[0.1rem]">
            {option?.subTitle}
          </span>
        )}
      </div>
    </div>
  );
};

export default OptionItem;
