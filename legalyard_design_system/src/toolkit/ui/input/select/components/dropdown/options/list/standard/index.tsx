import { validArray } from "../../../../../../../../utils/helpers/data/array";
import { hasCamelCase } from "../../../../../../../../utils/helpers/data/string";
import useSelectInput from "../../../../../@context";
import { OptionDataType } from "../../../../../types";
import OptionItem from "../components/option";

const StandardOptionList = ({
  optionData,
}: {
  optionData?: OptionDataType[];
}) => {
  const {
    state: { selected, valueForBlink, multipleSelected },
    helper: { handleOptionSelect, finalData },
    option: {
      data,
      divideCamelCase,
      dataStart = 0,
      dataEnd = data?.length || 0,
      checkbox,
      hideIcon,
    } = {},
    multiple,
  } = useSelectInput();

  const renderData = optionData ?? finalData() ?? [];

  return (
    <>
      {(renderData as OptionDataType[])
        ?.slice(dataStart, dataEnd)
        ?.map((item, index) => {
          const camelCase = hasCamelCase(item?.title);
          const finalText: string =
            divideCamelCase && camelCase
              ? item?.title?.replace(/([A-Z])/g, " $1")
              : item?.title;

          const blink =
            valueForBlink?.toString()?.toLowerCase() ===
            finalText?.toString()?.toLowerCase();

          const isSelected = (): boolean => {
            try {
              if (multiple) {
                return Boolean(
                  validArray(multipleSelected) &&
                    multipleSelected?.find((sl) => sl?.value === item?.value)
                );
              } else {
                return (
                  Boolean(selected?.value) && selected?.value === item?.value
                );
              }
            } catch (error) {
              return false;
            }
          };

          return (
            <OptionItem
              key={index + String(item?.value)}
              option={item}
              blink={blink}
              isSelected={isSelected()}
              onClick={() => handleOptionSelect(item)}
              text={finalText?.toString()}
              multiple={multiple}
              checkbox={checkbox}
              hideIcon={hideIcon}
            />
          );
        })}
    </>
  );
};

export default StandardOptionList;
