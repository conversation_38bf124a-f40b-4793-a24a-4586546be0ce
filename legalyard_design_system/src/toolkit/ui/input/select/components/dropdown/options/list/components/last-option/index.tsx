import useSelectInput from "../../../../../../@context";
import OptionItem from "../option";

const LastOption = () => {
  const {
    state: { selected },
    helper: { handleOptionSelect },
    option: { lastOption } = {},
  } = useSelectInput();

  return (
    <OptionItem
      option={lastOption}
      blink={false}
      isSelected={Boolean(selected && selected?.value === lastOption?.value)}
      onClick={() => handleOptionSelect(lastOption ?? null)}
      text={lastOption?.title}
    />
  );
};

export default LastOption;
