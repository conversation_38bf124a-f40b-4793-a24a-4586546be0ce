import usePosition from "../../../../../utils/hooks/usePosition";
import classNames from "classnames";
import useSelectInput from "../../@context";
import SelectOptions from "./options";
import OptionSearch from "./search";

const SelectDropdown = () => {
  const {
    withInput,
    option: { search } = {},
    refs: { containerRef, dropdownRef },
  } = useSelectInput();

  const { bottom, toTop } = usePosition(containerRef);
  const dropHeight = Number(dropdownRef?.current?.offsetHeight) + 50 || 210;

  return (
    <div
      ref={dropdownRef}
      aria-label="options dropdown"
      className={classNames(
        "dropdown absolute left-0 right-0 w-full bg-white border border-solid border-slate-400 shadow-md rounded-base overflow-hidden z-20",
        bottom && bottom < dropHeight && toTop
          ? "bottom-full !border-b-0"
          : "top-full !border-t-0"
      )}
    >
      {search && !withInput && <OptionSearch />}

      <SelectOptions />
    </div>
  );
};

export default SelectDropdown;
