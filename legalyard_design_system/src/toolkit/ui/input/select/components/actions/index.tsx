import { ChevronDown, ChevronUp, X } from "react-feather";
import useSelectInput from "../../@context";
import { validArray } from "../../../../../utils/helpers/data/array";

const Actions = () => {
  const {
    state: { selected, open, setOpen, multipleSelected },
    helper: { handleReset },
    reset,
  } = useSelectInput();

  return (
    <div className="flex items-center gap-x-[1px] px-2">
      {reset && (Boolean(selected) || validArray(multipleSelected)) && (
        <button
          title="Clear all"
          className="text-font hover:text-main bg-transparent hover:bg-sub hover:bg-opacity-10 p-0.5 rounded-sm transition-all"
          onClick={() => handleReset()}
        >
          <X />
        </button>
      )}

      <button
        className="text-font hover:text-main bg-transparent hover:bg-sub hover:bg-opacity-10 p-0.5 rounded-sm transition-all"
        onClick={() => setOpen((prev) => !prev)}
      >
        {open ? <ChevronUp /> : <ChevronDown />}
      </button>
    </div>
  );
};

export default Actions;
