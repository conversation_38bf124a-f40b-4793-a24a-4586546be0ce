import { hasCamelCase } from "../../../../../utils/helpers/data/string";
import classNames from "classnames";
import useSelectInput from "../../@context";
import OptionIcon from "../dropdown/options/list/components/option/icon";

const SelectBox = () => {
  const {
    state: { selected, open },
    helper: { dropdownToggle },
    option: { divideCamelCase, hideIcon } = {},
    disabled,
    placeholder,
  } = useSelectInput();

  type forValue = {
    title: string;
    noValue: boolean;
  };
  const currentValue = (): forValue => {
    try {
      if (
        selected &&
        typeof selected === "object" &&
        Object.keys(selected)?.length > 0
      ) {
        const current: string =
          divideCamelCase && hasCamelCase(selected?.title)
            ? selected?.title?.replace(/([A-Z])/g, " $1")
            : selected?.title;

        return { title: current ?? "", noValue: !<PERSON><PERSON><PERSON>(current) };
      } else if (placeholder) {
        return { title: placeholder, noValue: true };
      } else {
        return { title: "Select", noValue: true };
      }
    } catch (error) {
      return { title: "Select", noValue: true };
    }
  };

  return (
    <button
      aria-label="select box"
      className={classNames(
        "cursor-pointer p-1.5 w-full flex-1 text-start text-sm capitalize",
        !hideIcon && selected?.icon && "flex items-center gap-1",
        currentValue()?.noValue && "!text-slate-400",
        disabled && "pointer-events-none bg-grey bg-opacity-10 shadow-inner"
      )}
      onClick={() => dropdownToggle(!open)}
    >
      {!hideIcon && selected?.icon && (
        <OptionIcon
          title={currentValue()?.title}
          icon={selected?.icon}
          className="!max-w-5"
        />
      )}
      {currentValue()?.title}
    </button>
  );
};

export default SelectBox;
