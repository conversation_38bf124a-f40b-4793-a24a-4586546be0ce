import styled from "styled-components";
import { StyledType } from "../types";
import * as Styles from "../styles";

export const Container = styled(Styles.Container)`
  font-size: ${({ theme: { font } }) => font.sub};
`;

export const Wrapper = styled(Styles.Wrapper)`
  position: relative;
`;

export const LabelHolder = styled(Styles.LabelHolder)<StyledType>``;

export const Input = styled(Styles.CustomInput)<StyledType>`
  &[type="date"]::-webkit-inner-spin-button,
  &[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
  }
`;
