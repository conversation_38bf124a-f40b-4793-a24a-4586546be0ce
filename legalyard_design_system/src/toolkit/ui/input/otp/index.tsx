import {
  useState,
  useEffect,
  useRef,
  FC,
  ChangeEvent,
  ClipboardEvent,
  KeyboardEvent,
} from "react";
import * as Styles from "./styles";
import { OTPInputTypes } from "./types";

const OTPInput: FC<OTPInputTypes> = ({
  id,
  onChange,
  name,
  label,
  reset,
  error,
  response,
  disabled,
}) => {
  const inputRef = useRef<any[]>([]);
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);

  const OTPCount = otp.length;

  const handleChange = (index: number, e: ChangeEvent<HTMLInputElement>) => {
    try {
      const value = e?.target?.value;
      if (isNaN(Number(value))) {
        return;
      }

      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);
      if (onChange) {
        onChange({ target: { value: newOtp?.join(""), name } });
      }

      if (value !== "" && index < inputRef?.current?.length - 1) {
        inputRef.current[index + 1].focus();
      }
    } catch (err) {
      console.error(err);
    }
  };

  useEffect(() => {
    try {
      const valid = otp?.every((item) => item !== "");

      if (valid && reset) {
        setOtp(["", "", "", "", "", ""]);
        inputRef?.current[0]?.focus();
      }
    } catch (err) {
      console.error(err);
    }
  }, [otp, onChange, reset, name]);

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    try {
      if (e.key === "Backspace" && otp[index] === "" && index > 0) {
        inputRef.current[index - 1].focus();
      }
      if (e.key === " ") {
        e.preventDefault();
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      const data = e?.clipboardData?.getData("Text");
      let pasteOTP: string[] = data.split("");
      if (pasteOTP.length < OTPCount) {
        const add = Array(OTPCount - data.length).fill("");
        pasteOTP.push(String(add));
      }
      setOtp(pasteOTP.flat());
      inputRef.current[Math.min(5, data.length)].focus();
    } catch (error) {
      console.error({ error });
    }
  };

  return (
    <div className="bg-transparent w-full my-2">
      {label && <Styles.LabelHolder>{label}</Styles.LabelHolder>}
      <div className="flex items-center gap-3">
        {otp?.map((_, index) => {
          return (
            <Styles.InputHolder
              key={index}
              id={id ?? "otp"}
              ref={(ref) => (inputRef.current[index] = ref)}
              type="text"
              maxLength={1}
              value={otp[index] || ""}
              onChange={(e) => handleChange(index, e)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={(e) => handlePaste(e)}
              autoComplete="none"
              autoFocus={Boolean(index === 0)}
              disabled={Boolean(disabled)}
              $valid={Boolean(otp[index])}
              $error={Boolean(error)}
            />
          );
        })}
      </div>
      {error && (
        <p id="input-error" className="text-red my-3">
          {error}
        </p>
      )}
      {response && (
        <p id="input-response" className="text-green my-3">
          {response}
        </p>
      )}
    </div>
  );
};

export default OTPInput;
