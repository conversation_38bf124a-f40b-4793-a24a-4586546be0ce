import { FC, useCallback, useEffect, useState } from "react";

import * as Styles from "./styles";
import { PropsType } from "./types";

const RadioInput: FC<PropsType> = ({
  radios,
  name,
  defaultValue,
  value,
  error,
  onChange,
  boolean,
  inline,
  disabled,
  ...rest
}) => {
  const [isChecked, setIsChecked] = useState<string | number | null>(
    defaultValue || null
  );

  // TODO
  // This is to set defaultValue & get it back to parent component

  const handleDefaultValue = useCallback(() => {
    if (defaultValue && !Boolean(isChecked) && !Boolean(value)) {
      const defaultItem = radios?.find((item) => item?.value === defaultValue);
      if (defaultItem) {
        setIsChecked(defaultItem?.value);
      }
    }
  }, [defaultValue, isChecked, radios, value]);

  useEffect(() => {
    handleDefaultValue();
  }, [handleDefaultValue]);

  useEffect(() => {
    if (<PERSON><PERSON><PERSON>(value)) {
      setIsChecked(value);
    }
  }, [value]);

  const handleChange = (value: string | number) => {
    setIsChecked(value);

    if (onChange) {
      onChange({ target: { name, value } });
    }
  };

  return (
    <Styles.Container
      className="radio"
      aria-label="Radio Input"
      $inline={inline}
      $disabled={disabled}
    >
      {Array.isArray(radios) &&
        radios?.length > 0 &&
        radios?.map((item, index) => {
          return (
            <Styles.RadioHolder
              key={item?.label + index}
              onClick={() => handleChange(item?.value)}
              className="item"
              $hasSubtext={Boolean(item?.subtext)}
              $disabled={item?.disabled}
            >
              <Styles.RadioInput
                id={String(item?.value)}
                className="bullet"
                $selected={isChecked === item?.value}
                {...rest}
              />
              <Styles.LabelHolder className="label">
                <h5>{item?.label}</h5>
                {item?.subtext && <p>{item?.subtext}</p>}
              </Styles.LabelHolder>
            </Styles.RadioHolder>
          );
        })}
      {error ? (
        <Styles.ErrorHolder>
          <Styles.TextHolder $error>{error}</Styles.TextHolder>
        </Styles.ErrorHolder>
      ) : null}
    </Styles.Container>
  );
};

export default RadioInput;
