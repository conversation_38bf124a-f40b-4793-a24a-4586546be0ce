import styled, { css } from "styled-components";
import { StyledType } from "./types";

const disabledMode = css`
  pointer-events: none;
  opacity: 0.5;
`;

export const Container = styled.div<StyledType>`
  ${({ theme: { placeholders } }) => placeholders.flex};
  align-items: ${({ $inline }) => ($inline ? "center" : "flex-start")};
  flex-direction: ${({ $inline }) => ($inline ? "row" : "column")};
  flex-wrap: wrap;
  gap: 0.75rem;

  ${({ $disabled }) => $disabled && disabledMode}
`;

export const RadioHolder = styled.div<StyledType>`
  ${({ theme: { placeholders } }) => placeholders.flex};
  align-items: ${({ $hasSubtext }) => ($hasSubtext ? "flex-start" : "center")};
  gap: 0.5rem;

  user-select: none;
  cursor: pointer;

  &:first-of-type {
    span {
      margin-left: 0;
    }
  }

  &:last-of-type {
    margin-right: 0;
  }

  ${({ $disabled }) => $disabled && disabledMode}
`;

export const LabelHolder = styled.label<StyledType>`
  cursor: pointer;
  line-height: normal;

  h5 {
    font-size: ${({ theme: { font } }) => font.main};
    text-transform: capitalize;
  }
  p {
    font-size: ${({ theme: { font } }) => font.sub};
    color: ${({ theme: { color } }) => `${color.grey}90`};
  }
`;

export const RadioInput = styled.span<StyledType>`
  cursor: pointer;
  background-color: ${({ theme: { color } }) => color.white};
  border: 2px solid
    ${({ $error, theme: { color } }) => ($error ? color.red : color.main)};
  color: ${({ theme: { color } }) => color.main};
  width: 22px;
  height: 22px;
  outline: none;
  appearance: none;
  margin-right: 0.1rem;
  border-radius: 100px;
  position: relative;
  display: block;

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    background-color: ${({ $selected, theme: { color } }) =>
      $selected ? color.main : "transparent"};
    border-radius: 100px;
    transform: translate(-50%, -50%);
  }
`;

export const TextHolder = styled.span<StyledType>`
  font-size: ${({ $error, theme: { font } }) =>
    $error ? font.sub : font.main};
`;

export const ErrorHolder = styled.div`
  margin: 4px 0;
  color: ${({ theme: { color } }) => color.red};
`;
