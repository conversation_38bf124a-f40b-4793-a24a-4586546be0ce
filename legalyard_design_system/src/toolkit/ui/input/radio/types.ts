export interface PropsType {
  radios: {
    label: string;
    value: string | number;
    subtext?: string;
    [key: string]: any;
  }[];
  name: string;
  defaultValue?: string | number;
  value: string | number;
  error?: string;
  onChange?: (d?: any) => void;
  boolean?: boolean;
  inline?: boolean;
  disabled?: boolean;
}

export interface styles extends React.InputHTMLAttributes<HTMLInputElement> {
  $inline?: boolean;
  $hasSubtext?: boolean;
  $selected?: boolean;
  $error?: boolean;
  $disabled?: boolean;
}

export type StyledType = styles;
