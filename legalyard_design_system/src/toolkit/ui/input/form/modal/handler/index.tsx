import withUnsavedChanges from "../../../../HOC/unsaved-changes";
import Modal from "../../../../modal";
import { propsType as modalTypes } from "../../../../modal/types";
import { FC } from "react";

const ModalHandler: FC<modalTypes> = ({ children, open, onClose, ...rest }) => {
  return (
    <Modal open={open} onClose={onClose} {...rest}>
      {children}
    </Modal>
  );
};

const ModalWrapped = withUnsavedChanges(ModalHandler);

const FormHandler: FC<modalTypes> = ({ children, open, onClose, ...rest }) => {
  return (
    <div aria-label="Form Handler" className="relative">
      <ModalWrapped open={open} onClose={onClose} {...rest}>
        {children}
      </ModalWrapped>
    </div>
  );
};

export default FormHandler;
