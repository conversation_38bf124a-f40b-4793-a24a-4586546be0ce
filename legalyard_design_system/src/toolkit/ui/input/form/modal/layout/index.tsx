import classNames from "classnames";
import { FormEvent, forwardRef, Ref } from "react";
import Button from "../../../../button";
import { ModalFormLayoutType } from "../../types";

const ModalFormLayout = forwardRef<HTMLDivElement, ModalFormLayoutType>(
  (
    {
      children,
      title,
      hideStyle,
      button,
      onSubmit,
      onClose,
      className,
      buttonClassName,
    },
    ref: Ref<HTMLDivElement>
  ) => {
    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
      if (onSubmit) {
        onSubmit(e);
      }
    };

    const handleClose = () => {
      if (onClose) {
        onClose(false);
      }
    };

    return (
      <div
        ref={ref}
        className={classNames(
          "modal-form-layout relative",
          !hideStyle &&
            "max-h-[85vh] w-[calc(100vw-10px)] sm:w-[60vw] max-w-[800px]",
          className
        )}
      >
        {title && (
          <h3 className="text-base font-semibold capitalize border-b border-gray py-2 px-6 mb-5 sticky top-0 z-50 bg-white rounded-base shadow-md">
            {title}
          </h3>
        )}
        <form
          onSubmit={(e) => handleSubmit(e)}
          className="modal-form-layout-form w-full flex flex-col items-start gap-y-4"
        >
          <div className="modal-form-layout-content w-full flex flex-col items-start gap-4 [&>*]:w-full px-6">
            {children}
          </div>

          {!button?.hidden && (
            <div className="sticky bottom-0 px-6 py-2 bg-white w-full border-t border-slate-100 shadow-inner">
              <div className="flex items-center gap-x-4 ml-auto w-full sm:w-3/4 md:w-1/2 lg:w-1/3">
                <Button
                  variant="action"
                  type="button"
                  effect
                  onClick={() => handleClose()}
                >
                  cancel
                </Button>

                <Button
                  className={classNames(buttonClassName)}
                  type="submit"
                  disabled={button?.disabled}
                  size="full"
                >
                  {button?.content ?? "Save"}
                </Button>
              </div>
            </div>
          )}
        </form>
      </div>
    );
  }
);

export default ModalFormLayout;
