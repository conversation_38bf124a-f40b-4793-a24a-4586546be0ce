import { useState, useEffect, FC } from "react";
import { Check, X } from "react-feather";
import { CheckboxTagsTypes } from "./types";
import * as Styles from "./styles";

const mockData = [
  { titleId: 1, title: "Flat" },
  { titleId: 2, title: "House/Villa" },
  { titleId: 3, title: "Plot" },
];

const CheckboxTags: FC<CheckboxTagsTypes> = ({
  data,
  currentSingleSelected,
  currentMultipleSelected,
  getMultipleSelected,
  getCurrentSelected,
  rounded,
  selectMultiple,
  wrapperStyle,
  checkStyle,
  inGrid,
  gridColumn,
}) => {
  const [multipleSelected, setMultipleSelected] = useState<any[]>([]);
  const [currentSelected, setCurrentSelected] = useState(currentSingleSelected);

  const getSelected = (val: any) => {
    if (selectMultiple) {
      if (multipleSelected.length > 0 && val) {
        return (
          multipleSelected.includes(val?.title) ||
          multipleSelected.includes(val?.value) ||
          multipleSelected.includes(val?.id)
        );
      }
    } else {
      return val?.title === currentSelected?.title;
    }
  };

  const handleSelect = (val: any) => {
    try {
      if (selectMultiple) {
        const isItemSelected = getSelected(val);

        if (isItemSelected) {
          const removeItem = multipleSelected.filter((item) => item !== val);
          if (getMultipleSelected) {
            getMultipleSelected(removeItem);
          }
          setMultipleSelected(removeItem);
        } else {
          if (getMultipleSelected) {
            getMultipleSelected([...multipleSelected, val]);
          }
          setMultipleSelected([...multipleSelected, val]);
        }
      } else {
        const isItemSelected = getSelected(val);
        if (isItemSelected) {
          setCurrentSelected(null);
          if (getCurrentSelected) {
            getCurrentSelected(null);
          }
        } else {
          setCurrentSelected(val);
          if (getCurrentSelected) {
            getCurrentSelected(val);
          }
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  useEffect(() => {
    if (selectMultiple) {
      setMultipleSelected(currentMultipleSelected);
    } else {
      setCurrentSelected(currentSingleSelected);
    }
  }, [selectMultiple, currentMultipleSelected, currentSingleSelected]);

  const listData = data || mockData;

  return (
    <Styles.Container aria-label="Radio Tags">
      <Styles.Wrapper
        style={wrapperStyle}
        $inGrid={inGrid}
        $gridColumn={gridColumn}
      >
        {listData && Array.isArray(listData) ? (
          <>
            {listData.map((item, index) => {
              const isItemSelected = getSelected(item);
              return (
                <Styles.TagWrapper
                  key={index}
                  onClick={() => handleSelect(item)}
                  $selected={isItemSelected}
                  $rounded={rounded}
                  style={checkStyle}
                >
                  <Styles.CheckHolder
                    $selected={isItemSelected}
                    className="checkbox"
                  >
                    {isItemSelected ? <Check size={10} /> : <X size={10} />}
                  </Styles.CheckHolder>
                  {item?.icon ? (
                    <Styles.IconWrapper>
                      {item?.icon && typeof item?.icon === "string" ? (
                        <Styles.IconHolder src={item?.icon ? item?.icon : ""} />
                      ) : (
                        item?.icon
                      )}
                    </Styles.IconWrapper>
                  ) : null}
                  <Styles.TextHolder>{item?.title}</Styles.TextHolder>
                </Styles.TagWrapper>
              );
            })}
          </>
        ) : null}
      </Styles.Wrapper>
    </Styles.Container>
  );
};

export default CheckboxTags;
