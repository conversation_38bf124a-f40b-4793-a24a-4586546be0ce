import styled from "styled-components";
import { StyledType } from "../../types";

export const Container = styled.div``;

export const Wrapper = styled.div<StyledType>`
  display: ${({ $inGrid }) => ($inGrid ? "grid" : "flex")};
  grid-template-columns: ${({ $gridColumn }) => `repeat(${$gridColumn}, 1fr)`};
  grid-gap: 10px 15px;
  align-items: center;
  flex-wrap: wrap;
`;

export const TagWrapper = styled.div<StyledType>`
  margin-right: 10px;
  margin-bottom: 10px;
  font-weight: ${({ $selected }) => ($selected ? 600 : 400)};

  letter-spacing: 0.5px;

  border: 1px solid
    ${({ $selected, theme: { allColors } }) =>
      $selected ? allColors.main : `${allColors.dark}20`};
  border-radius: ${({ $rounded, theme: { elements } }) =>
    $rounded ? "100px" : elements.radius};

  padding: ${({ $rounded }) => ($rounded ? "10px 15px" : "10px 12px")};

  background-color: ${({ $selected, theme: { allColors } }) =>
    $selected ? `${allColors.sub}20` : allColors.white};

  box-shadow: 0 0 5px ${({ theme: { allColors } }) => `${allColors.dark}10`};
  &:last-of-type {
    margin-right: 0;
  }

  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: ${({ $selected, theme: { allColors } }) =>
      !$selected && `${allColors.sub}10`};

    .checkbox {
      border-color: ${({ theme: { allColors } }) => allColors.main};
    }
  }

  display: flex;
  align-items: center;
`;

export const TextHolder = styled.span<StyledType>`
  width: 100%;
  font-size: ${({ theme: { fontSize } }) => fontSize.sub};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const CheckHolder = styled.div<StyledType>`
  width: 20px;
  height: 100%;
  background-color: ${({ $selected, theme: { allColors } }) =>
    $selected ? allColors.main : allColors.white};
  border-width: 1px;
  border-style: solid;
  border-color: ${({ $selected, theme: { allColors } }) =>
    $selected ? allColors.main : `${allColors.dark}20`};
  border-radius: ${({ theme: { elements } }) => elements.radius};
  padding: 4px;
  position: relative;

  color: #fff;

  margin-right: 12px;

  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    stroke-width: 4px;
  }
`;

export const IconWrapper = styled.div<StyledType>`
  margin-right: 8px;
  display: block;
  svg {
    width: 15px;
    height: 150%;
  }
`;

export const IconHolder = styled.img``;
