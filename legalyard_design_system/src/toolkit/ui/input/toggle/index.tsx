import { useEffect, useState } from "react";

import * as Styles from "./styles";

// TODO
// Convert to typescript + tailwind

const ToggleInput = ({
  label,
  subtext,
  name,
  value,
  defaultValue,
  onChange,
}: any) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (defaultValue) {
      setIsOpen(defaultValue);
    }
  }, [defaultValue]);

  useEffect(() => {
    if (value) {
      setIsOpen(value);
    }
  }, [value]);

  const handleClick = (val: any) => {
    if (onChange) {
      onChange({
        target: {
          name,
          value: val,
        },
      });
    }
    setIsOpen((prevState) => !prevState);
  };

  return (
    <Styles.Container
      hasSubtext={Boolean(subtext)}
      onClick={() => handleClick(!isOpen)}
      title={name || ""}
    >
      <Styles.ToggleContainer
        className="toggle"
        aria-label="Toggle Input"
        $active={isOpen}
      >
        <Styles.ToggleHolder $active={isOpen} />
      </Styles.ToggleContainer>
      <Styles.LabelHolder className="label">
        <h5>{label}</h5>
        {subtext && <p>{subtext}</p>}
      </Styles.LabelHolder>
    </Styles.Container>
  );
};

export default ToggleInput;
