import styled, { css } from "styled-components";

export const Container = styled.div<{ hasSubtext: any }>`
  ${({ theme: { placeholders } }) => placeholders.flex};
  align-items: ${({ hasSubtext }) => (hasSubtext ? "flex-start" : "center")};
  gap: 0.5rem;
  flex-wrap: wrap;
  user-select: none;
`;

export const ToggleContainer = styled.div<{ $active: any }>`
  background: ${({ theme: { color } }) => color.white};
  border: 2px solid
    ${({ $active, theme: { color } }) => ($active ? color.main : color.grey)};
  height: 1.2em;
  width: 2.1em;
  border-radius: 1em;
  position: relative;
  padding: 0 0.25rem;
  cursor: pointer;
  transition: border 150ms ease-in-out;
`;

const makeLeft = css`
  left: 1px;
`;

const makeRight = css`
  left: 98%;
  transform: translate(-100%, -50%);
`;

export const ToggleHolder = styled.div<{ $active: any }>`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 0.9em;
  height: 0.9em;
  background: ${({ $active, theme: { color } }) =>
    $active ? color.main : color.grey};
  border: 1px solid ${({ theme: { color } }) => color.white};
  border-radius: 50%;
  transition: all 150ms ease-in-out;
  ${({ $active }) => ($active ? makeRight : makeLeft)};
`;

export const LabelHolder = styled.label`
  cursor: pointer;
  line-height: normal;

  h5 {
    font-size: ${({ theme: { font } }) => font.main};
  }
  p {
    font-size: ${({ theme: { font } }) => font.sub};
    color: ${({ theme: { color } }) => `${color.grey}90`};
  }
`;
