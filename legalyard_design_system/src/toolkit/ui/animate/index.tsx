import { FC, useEffect, useState } from "react";
import { propsType } from "./types";
import * as Styles from "./styles";
import classNames from "classnames";

const Animate: FC<propsType> = ({
  children,
  duration,
  toLeft,
  toRight,
  toBottom,
  toTop,
  className,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const propGroup = {
    $isVisible: isVisible,
    $duration: duration,
    $toLeft: toLeft,
    $toRight: toRight,
    $toBottom: toBottom,
    $toTop: toTop,
  };

  return (
    <Styles.Container
      aria-label="Animate"
      className={classNames("animate", className)}
      {...propGroup}
    >
      {children}
    </Styles.Container>
  );
};

export default Animate;
