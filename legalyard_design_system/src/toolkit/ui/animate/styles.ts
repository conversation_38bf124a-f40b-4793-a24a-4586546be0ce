import styled, { css } from "styled-components";
import { StyledType } from "./types";

const towardsLeft = css<StyledType>`
  transform: ${({ $isVisible }) =>
    $isVisible ? "translateX(0)" : "translateX(100px)"};
`;

const towardsRight = css<StyledType>`
  transform: ${({ $isVisible }) =>
    $isVisible ? "translateX(0)" : "translateX(-100px)"};
`;

const towardsBottom = css<StyledType>`
  transform: ${({ $isVisible }) =>
    $isVisible ? "translateY(0)" : "translateY(-100px)"};
`;

const towardsTop = css<StyledType>`
  transform: ${({ $isVisible }) =>
    $isVisible ? "translateY(0)" : "translateY(100px)"};
`;

export const Container = styled.div<StyledType>`
  width: 100%;
  height: 100%;

  opacity: ${({ $isVisible }) => ($isVisible ? 1 : 0)};
  ${({ $toLeft, $toBottom, $toTop }) =>
    $toLeft
      ? towardsLeft
      : $toBottom
      ? towardsBottom
      : $toTop
      ? towardsTop
      : towardsRight}
  transition-property: transform, opacity;
  transition-timing-function: ease-in-out;
  transition-duration: ${({ $duration }) =>
    $duration ? `${$duration}ms` : "500ms"};
`;
