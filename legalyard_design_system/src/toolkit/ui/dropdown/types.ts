import { ReactNode } from "react";

export interface DropdownReferenceType {
  toggle: (v: boolean) => void;
}

export interface DropdownType {
  children?: ReactNode;
  content?: ReactNode;
  dropdownStyle?: any;
  inlineAlign?: "left";
  topAlign?: boolean;
  hover?: boolean;
  className?: string;
  contentClassName?: string;
  actionClassName?: string;
  onClose?: (data?: any) => void;
  onToggle?: (data?: any) => void;
}

export * from "./menu/types";
