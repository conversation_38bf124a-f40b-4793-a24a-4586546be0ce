import classNames from "classnames";
import React, { Dispatch, SetStateAction, useState } from "react";

const MenuSearch = ({
  searchValue,
  setSearchValue,
}: {
  searchValue: string;
  setSearchValue: Dispatch<SetStateAction<string>>;
}) => {
  const [focused, setFocused] = useState(false);

  return (
    <div className="dropdown-search p-2 bg-slate-200 rounded-base sticky top-0 z-[5]">
      <input
        type="search"
        className={classNames(
          "border outline-0 w-full p-2 text-sm rounded-base",
          focused ? "border-skyBlue" : "border-slate-300"
        )}
        name="search"
        placeholder="Search..."
        onChange={(e) => setSearchValue(e?.target?.value)}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        value={searchValue ?? ""}
        autoComplete="off"
        autoFocus
      />
    </div>
  );
};

export default MenuSearch;
