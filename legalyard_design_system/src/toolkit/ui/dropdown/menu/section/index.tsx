import { validArray } from "../../../../utils/helpers/data/array";
import usePosition from "../../../../utils/hooks/usePosition";
import classNames from "classnames";
import { FC, lazy, Suspense, useRef, useState } from "react";
import { ChevronDown, ChevronRight } from "react-feather";
import { MenuDataItemType, MenuSectionType } from "../types";
import MenuItem from "./item";
import Loader from "../../../loader";

const DropdownMenu = lazy(() => import(".."));

const MenuSection: FC<MenuSectionType> = ({ collapsible, section, index }) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const actionRef = useRef<HTMLDivElement>(null);

  const [open, setOpen] = useState<boolean>(collapsible ?? true);

  const { toLeft, toTop } = usePosition(actionRef);

  const collapseProps = {
    onClick: () => setOpen((prev) => !prev),
  };

  return (
    <div
      ref={elementRef}
      aria-label="section"
      className="first-of-type:pt-2 last-of-type:pb-2"
    >
      <div
        role={collapsible ? "button" : "tree"}
        className={classNames(
          "relative  w-full py-2 px-3 h-3 flex items-center gap-x-1",
          index === 0 && !Boolean(section.title) && "hidden",
          collapsible && "hover:bg-sky-50 py-3 cursor-pointer select-none"
        )}
        {...(collapsible ? collapseProps : {})}
      >
        <div
          className={classNames(
            "flex items-center gap-[1px] flex-1",
            collapsible && "-translate-x-2.5"
          )}
        >
          {collapsible && (
            <>
              {open ? (
                <ChevronDown className="stroke-[3] w-4 h-4 text-gray" />
              ) : (
                <ChevronRight className="stroke-[3] w-4 h-4 text-gray" />
              )}
            </>
          )}
          {section.title && (
            <span
              className={classNames(
                "capitalize inline-block text-gray font-normal leading-4 whitespace-nowrap",
                collapsible ? "text-sm" : "text-xs"
              )}
            >
              {section.title}
            </span>
          )}
        </div>

        <span
          aria-label="title border"
          className="mt-[1px] w-full h-[1px] bg-slate-100"
        />
      </div>

      {open && section.menu && (
        <div className="flex flex-col items-start gap-y-0.5">
          {validArray(section.menu) &&
            section.menu.map((menu: MenuDataItemType, idx: number) => {
              return (
                <div
                  ref={actionRef}
                  key={idx}
                  className={classNames(
                    "relative w-full cursor-pointer text-sb text-font hover:bg-slate-100 hover:text-main block [&:hover>.child-dropdown]:block",
                    menu?.active && "bg-slate-100 text-main"
                  )}
                >
                  <MenuItem menu={menu} />
                  {menu.child && (
                    <Suspense fallback={<Loader />}>
                      <DropdownMenu
                        data={menu?.child}
                        className={classNames(
                          "hidden absolute -top-2 child-dropdown h-fit",
                          toLeft ? "right-full" : "left-full",
                          toTop ? "-bottom-4" : "-top-4"
                        )}
                      />
                    </Suspense>
                  )}
                </div>
              );
            })}
        </div>
      )}
    </div>
  );
};

export default MenuSection;
