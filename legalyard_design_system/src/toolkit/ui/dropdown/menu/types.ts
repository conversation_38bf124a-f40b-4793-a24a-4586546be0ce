import { ReactNode } from "react";

export interface MenuDataItemType {
  id?: string | number;
  title: string;
  text?: string;
  rightText?: string;
  icon?: string | ReactNode;
  active?: boolean;
  event?: () => void;
  child?: MenuDataType[];
}

export interface MenuDataType {
  id?: string;
  title?: string;
  menu: MenuDataItemType[];
}

export interface DropdownMenuType {
  className?: string;
  search?: boolean;
  collapsible?: boolean;
  data: MenuDataType[];
  onClose?: (data?: any) => void;
  fixContent?: ReactNode;
}

export interface MenuSectionType {
  collapsible?: boolean;
  section: MenuDataType;
  index: number;
}
