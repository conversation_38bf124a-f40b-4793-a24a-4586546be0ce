import { validArray } from "../../../utils/helpers/data/array";
import useOutsideAlert from "../../../utils/hooks/useOutsideAlert";
import useScroll from "../../../utils/hooks/useScroll";
import classNames from "classnames";
import { FC, Fragment, lazy, Suspense, useRef, useState } from "react";
import { AlertOctagon } from "react-feather";
import MenuSearch from "./search";
import { DropdownMenuType, MenuDataType } from "./types";
import Loader from "../../loader";

const MenuSection = lazy(() => import("./section"));

const DropdownMenu: FC<DropdownMenuType> = ({
  data,
  className,
  search,
  onClose,
  collapsible,
  fixContent,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { translateY, wheelScroll, touchMove } = useScroll(dropdownRef);
  const [searchValue, setSearchValue] = useState("");

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  useOutsideAlert(dropdownRef, handleClose);

  const translateStyle = {
    transform: `translateY(${translateY})px`,
  };

  const filteredData = (): any[] => {
    try {
      if (validArray(data)) {
        const lowercasedTerm = searchValue?.toLowerCase();
        return data?.reduce<MenuDataType[]>((filteredSection, section) => {
          const filteredFields = section?.menu?.filter((field) =>
            field?.title?.toLowerCase()?.includes(lowercasedTerm)
          );

          if (filteredFields && filteredFields?.length > 0) {
            filteredSection.push({ ...section, menu: filteredFields });
          }

          return filteredSection;
        }, []);
      }
      return [];
    } catch (error) {
      console.error(error);
      return [];
    }
  };
  const finalData = searchValue ? filteredData() : data;

  return (
    <div
      ref={dropdownRef}
      className={classNames(
        "dropdown-menu bg-white text-font shadow-lg rounded-base w-full h-[350px] focus:outline-0",
        className
      )}
      style={translateStyle}
      role="menu"
      aria-orientation="vertical"
      aria-labelledby="menu-button"
      tabIndex={-1}
      onWheel={wheelScroll}
      onTouchMove={touchMove}
      onClick={(e) => e?.stopPropagation()}
    >
      {search && (
        <MenuSearch searchValue={searchValue} setSearchValue={setSearchValue} />
      )}
      <div className="bg-white text-font w-full h-full rounded-base">
        {validArray(finalData) ? (
          finalData.map((section: any, index: number) => (
            <Fragment key={index}>
              <Suspense
                key={index}
                fallback={<Loader variant="skeleton" key={index} />}
              >
                <MenuSection
                  key={index}
                  section={section}
                  index={index}
                  collapsible={collapsible}
                />
              </Suspense>
            </Fragment>
          ))
        ) : (
          <div className="text-sm text-red flex items-center gap-1 whitespace-nowrap p-3 rounded-base">
            <AlertOctagon className="w-3 h-3" />
            <span>No results found</span>
          </div>
        )}
        {fixContent}
      </div>
    </div>
  );
};

export default DropdownMenu;
