import classNames from "classnames";
import { FC, useEffect, useRef } from "react";
import { X } from "react-feather";
import { validArray } from "../../../../utils/helpers/data/array";
import Button from "../../../button";
import { monthData } from "../../../input/date/data";
import { yearBoxType } from "../../types";

const YearBox: FC<yearBoxType> = ({
  startYear,
  endYear,
  date,
  handleSelectYear,
  setShowYears,
  setShowMonths,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const itsMonth = date?.getMonth() as number;
  const currentYear = new Date()?.getFullYear() as number;

  const startFrom = startYear ?? 1971;
  const endTo = endYear ?? 2099;

  const yearsArray = Array.from(
    { length: endTo - startFrom + 1 },
    (_, index) => startFrom + index
  );

  const handleMonthShow = () => {
    setShowMonths(true);
    setShowYears(false);
  };

  useEffect(() => {
    try {
      if (containerRef.current) {
        const container = containerRef.current;
        if (container) {
          const yearElements = container?.querySelectorAll(".year");

          yearElements?.forEach((yearElement: Element) => {
            const divElement = yearElement as HTMLDivElement;
            if (parseInt(divElement.innerText) === currentYear) {
              container!.scrollTop =
                divElement.offsetTop - container!.clientHeight / 2;
            }
          });
        }
      }
    } catch (error) {
      console.error(error);
    }
  }, [currentYear]);

  return (
    <div className="bg-white absolute top-0 left-0 w-full h-full border border-slate-200">
      <div
        ref={containerRef}
        className="flex flex-col w-full h-full overflow-y-auto"
      >
        <div className="sticky top-0 bg-white flex items-center justify-between text-sm border-b border-slate-200 px-1">
          <Button
            effect
            variant="action"
            className="h-8 flex-1 flex items-center justify-center font-semibold"
            onClick={() => handleMonthShow()}
          >
            {monthData?.[itsMonth]?.name}
          </Button>
          <span className="border-r border-slate-200 h-5 mx-1" />
          <Button
            effect
            variant="action"
            className="h-8 flex-1 flex items-center justify-center gap-x-0"
            onClick={() => setShowYears(false)}
          >
            <X className="w-3 h-3" />
            Close
          </Button>
        </div>
        <div className="flex-1 grid grid-cols-3 w-full h-full">
          {validArray(yearsArray) &&
            yearsArray?.map((year) => {
              return (
                <div
                  key={year}
                  className={classNames(
                    "year h-8 bg-white text-sm hover:bg-slate-100 flex items-center justify-center p-3 shadow cursor-pointer",
                    currentYear === year && "!bg-slate-100 font-semibold"
                  )}
                  onClick={() => handleSelectYear(year)}
                >
                  {year}
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default YearBox;
