import { FC } from "react";
import { ChevronDown, ChevronLeft, ChevronRight } from "react-feather";
import { monthData } from "../../../input/date/data";
import { headerType } from "../../types";
import Button from "../../../button";
import * as Styles from "./styles";

const Header: FC<headerType> = ({
  date,
  setDate,
  hideYear,
  navigation,
  setShowMonths,
  setShowYears,
}) => {
  const itsMonth = date?.getMonth() as number;
  const itsYear = date?.getFullYear() as number;

  const handlePrevMonth = () => {
    if (navigation) {
      setDate(new Date(date.getFullYear(), date.getMonth() - 1, 1));
    }
  };

  const handleNextMonth = () => {
    if (navigation) {
      setDate(new Date(date.getFullYear(), date.getMonth() + 1, 1));
    }
  };

  return (
    <Styles.Container>
      <div className="flex items-center gap-x-0">
        <Button
          effect
          variant="action"
          className="font-semibold flex items-center gap-x-0"
          onClick={() => setShowMonths(true)}
        >
          {monthData?.[itsMonth]?.short}
          <ChevronDown />
        </Button>
        <span className="border-r border-slate-200 h-4 mx-1" />
        {!hideYear && (
          <Button
            effect
            variant="action"
            className="font-semibold flex items-center gap-x-0"
            onClick={() => setShowYears(true)}
          >
            {itsYear}
            <ChevronDown />
          </Button>
        )}
      </div>
      {navigation && (
        <Styles.ButtonWrapper>
          <Styles.ButtonHolder onClick={() => handlePrevMonth()}>
            <ChevronLeft />
          </Styles.ButtonHolder>
          <Styles.ButtonHolder onClick={() => handleNextMonth()}>
            <ChevronRight />
          </Styles.ButtonHolder>
        </Styles.ButtonWrapper>
      )}
    </Styles.Container>
  );
};

export default Header;
