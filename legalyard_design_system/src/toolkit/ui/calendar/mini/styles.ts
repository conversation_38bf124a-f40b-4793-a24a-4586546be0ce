import styled, { css } from "styled-components";
import { StyledType } from "../types";

const Height = css`
  height: 24px;
`;

export const ItemHolder = css`
  width: 24px;
  height: 24px;
  line-height: 24px;
  margin: auto;
  position: relative;
  background-color: #fff;
  -webkit-transition: background-color 0.1s linear;
  transition: background-color 0.1s linear;
`;

export const Container = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
`;

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: 0.5rem;

  & > * {
    width: 100%;
  }
`;

export const Table = styled.div`
  flex: 1;
  display: table;
  table-layout: fixed;
  width: 100%;
  text-align: center;

  user-select: none;
  -webkit-box-direction: normal;
`;

export const DaysWrapper = styled.div`
  ${Height};
  display: table-row;
`;

export const DayWrapper = styled.span`
  display: table-cell;
  font-size: 10px;
  font-weight: 500;
  vertical-align: middle;
  outline: none;
  font-weight: 500;
  font-size: ${({ theme: { font } }) => font.small};
`;

export const ShortDayHolder = styled.span`
  font-weight: 500;
  ${ItemHolder}
`;

export const FulDayHolder = styled.span`
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  overflow: hidden;
  position: absolute;
  -webkit-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 1px;
  ${ItemHolder}
`;

export const DateContainer = styled.div`
  display: table-row-group;
`;

export const WeekWrapper = styled.div`
  ${Height};
  display: table-row;
`;

export const DateHolder = styled.span<StyledType>`
  cursor: pointer;
  outline: none;
  position: relative;
  display: table-cell;
  font-size: 10px;
  font-weight: 500;
  vertical-align: middle;
  ${ItemHolder};

  font-weight: ${({ $active, $selected }) =>
    $active || $selected ? 600 : 500};

  border-radius: ${({ theme: { element } }) => element.radius};

  background-color: ${({ $selected, theme: { color } }) =>
    $selected ? color.blue : "inherit"};

  color: ${({ $selected, theme: { color } }) =>
    $selected ? color.white : "inherit"};

  border: 1px solid
    ${({ $active, theme: { color } }) => ($active ? color.main : "transparent")};

  &:hover {
    background-color: ${({ $selected, theme: { color } }) =>
      $selected ? color.blue : `${color.main}10`};

    color: ${({ $selected, theme: { color } }) =>
      $selected ? color.white : color?.font};
  }
`;
