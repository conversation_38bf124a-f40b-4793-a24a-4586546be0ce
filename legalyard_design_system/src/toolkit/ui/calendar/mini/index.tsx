import { useEffect, useState, FC, MouseEvent } from "react";
import { daysData } from "../../input/date/data";
import Header from "./header";
import {
  getDaysInMonth,
  getStartingDay,
} from "../../../utils/helpers/data/date";
import { propsType } from "../types";
import classNames from "classnames";

import Actions from "./actions";
import YearBox from "./years";
import MonthBox from "./month";
import * as Styles from "./styles";

const Calendar: FC<propsType> = ({
  currentDate,
  getSelected,
  onClear,
  className,
  ...otherProps
}) => {
  // props
  // hideYear

  const [date, setDate] = useState<Date>(currentDate ?? new Date());
  const [selected, setSelected] = useState<Date | null>(null);
  const [weeks, setWeeks] = useState<any[] | null>(null);
  const [showYears, setShowYears] = useState<boolean>(false);
  const [showMonths, setShowMonths] = useState<boolean>(false);

  useEffect(() => {
    if (currentDate) {
      setDate(currentDate || new Date());
      setSelected(currentDate);
    }
  }, [currentDate]);

  const handleClick = (e: MouseEvent<HTMLSpanElement>, date: Date) => {
    try {
      e?.preventDefault();
      e?.stopPropagation();

      setSelected(date);
      if (getSelected) {
        getSelected(date);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleSelectYear = (year: number) => {
    try {
      setDate(new Date(year, date.getMonth(), 1));
      setShowYears(false);
      setShowMonths(true);
    } catch (error) {
      console.error(error);
    }
  };

  const handleSelectMonth = (month: number) => {
    try {
      setDate(new Date(date.getFullYear(), month, 1));
      setShowYears(false);
      setShowMonths(false);
    } catch (error) {
      console.error(error);
    }
  };

  const short = true;

  useEffect(() => {
    const getWeeks = () => {
      try {
        const year = date.getFullYear();
        const month = date.getMonth();
        const daysInMonth: any = getDaysInMonth(year, month);
        const startingDay: any = getStartingDay(year, month);
        const days = [];

        for (let i = 0; i < startingDay; i++) {
          days.push(0);
        }

        for (let i = 1; i <= daysInMonth; i++) {
          days.push(i);
        }

        const chunkSize = 7;
        const groupedArray = [];

        for (let i = 0; i < days.length; i += chunkSize) {
          const chunk = days.slice(i, i + chunkSize);
          groupedArray.push({ week: chunk });
        }

        return groupedArray;
      } catch (err) {
        console.error(err);
      }
    };

    const weeksData: any = getWeeks();
    setWeeks(weeksData);
  }, [date]);

  const handleSetToday = () => {
    try {
      const today = new Date();
      setSelected(today);
      setDate(today);
      if (getSelected) {
        getSelected(today);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleClearData = () => {
    try {
      setSelected(null);
      if (getSelected) {
        getSelected(null);
      }

      if (onClear) {
        onClear();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const config = {
    ...otherProps,
    date,
    setDate,
    setShowMonths,
    setShowYears,
    handleSelectYear,
    handleSelectMonth,
    handleSetToday,
    handleClearData,
  };

  return (
    <Styles.Container
      aria-label="Mini-calendar"
      className={classNames("calendar", className)}
    >
      <Styles.Wrapper>
        <Header {...config} />

        <Styles.Table>
          <Styles.DaysWrapper>
            {daysData?.map((day, index) => {
              return (
                <Styles.DayWrapper key={index + day?.name}>
                  {short ? (
                    <Styles.ShortDayHolder>{day?.short}</Styles.ShortDayHolder>
                  ) : (
                    <Styles.FulDayHolder>{day?.name}</Styles.FulDayHolder>
                  )}
                </Styles.DayWrapper>
              );
            })}
          </Styles.DaysWrapper>
          <Styles.DateContainer>
            {Array.isArray(weeks) && weeks?.length > 0 && (
              <>
                {weeks?.map((weekSet, index) => {
                  return (
                    <Styles.WeekWrapper key={index * weekSet?.week?.length}>
                      {weekSet?.week?.map((day: any, dayIndex: number) => {
                        const today = new Date();
                        const yearMatch =
                          today?.getFullYear() === date?.getFullYear();
                        const monthMatch =
                          today?.getMonth() === date?.getMonth();
                        const dayMatch = day === today?.getDate();

                        const dayToRender = Boolean(day) ? day : "";
                        const currentDate = new Date(
                          date?.getFullYear(),
                          date?.getMonth(),
                          day
                        );

                        const isSelected = Boolean(selected)
                          ? selected?.getFullYear() === date?.getFullYear() &&
                            selected?.getMonth() === date?.getMonth() &&
                            selected?.getDate() === day
                          : false;

                        return (
                          <Styles.DateHolder
                            key={
                              !Boolean(day)
                                ? `day_${dayIndex}`
                                : dayIndex * dayToRender
                            }
                            $active={yearMatch && monthMatch && dayMatch}
                            $selected={isSelected}
                            onClick={(e) => handleClick(e, currentDate)}
                          >
                            {dayToRender}
                          </Styles.DateHolder>
                        );
                      })}
                    </Styles.WeekWrapper>
                  );
                })}
              </>
            )}
          </Styles.DateContainer>
        </Styles.Table>
        <Actions {...config} />
      </Styles.Wrapper>

      {showMonths && <MonthBox {...config} />}
      {showYears && <YearBox {...config} />}
    </Styles.Container>
  );
};

export default Calendar;
