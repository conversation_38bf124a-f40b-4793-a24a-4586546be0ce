import { useEffect, useRef } from "react";

const useDragger = (id: string): void => {
  const isClicked = useRef<boolean>(false);

  const coords = useRef<{
    startX: number;
    startY: number;
    lastX: number;
    lastY: number;
  }>({
    startX: 0,
    startY: 0,
    lastX: 0,
    lastY: 0,
  });

  useEffect(() => {
    try {
      const target = document.getElementById(id);
      if (target) {
        const container = target?.parentElement;
        if (container) {
          const onMouseDown = (e: MouseEvent) => {
            isClicked.current = true;
            coords.current.startX = e.clientX;
            coords.current.startY = e.clientY;
          };

          const onMouseUp = (e: MouseEvent) => {
            isClicked.current = false;
            coords.current.lastX = target.offsetLeft;
            coords.current.lastY = target.offsetTop;
          };

          const onMouseMove = (e: MouseEvent) => {
            if (!isClicked.current) return;

            const nextX =
              e.clientX - coords.current.startX + coords.current.lastX;
            const nextY =
              e.clientY - coords.current.startY + coords.current.lastY;

            target.style.top = `${nextY}px`;
            target.style.left = `${nextX}px`;
          };

          target.addEventListener("mousedown", onMouseDown);
          target.addEventListener("mouseup", onMouseUp);
          container.addEventListener("mousemove", onMouseMove);
          container.addEventListener("mouseleave", onMouseUp);

          const cleanup = () => {
            target.removeEventListener("mousedown", onMouseDown);
            target.removeEventListener("mouseup", onMouseUp);
            container.removeEventListener("mousemove", onMouseMove);
            container.removeEventListener("mouseleave", onMouseUp);
          };

          return cleanup;
        }
      }
    } catch (error) {
      console.error(error);
    }
  }, [id]);
};

export default useDragger;
