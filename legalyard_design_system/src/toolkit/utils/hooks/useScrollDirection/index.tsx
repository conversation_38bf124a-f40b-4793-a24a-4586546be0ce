import { RefObject, useCallback, useEffect, useRef, useState } from "react";

type ScrollDirection = "up" | "down" | null;

interface UseScrollDirectionOptions {
  ref?: RefObject<HTMLElement>;
}

const useScrollDirection = ({
  ref,
}: UseScrollDirectionOptions = {}): ScrollDirection => {
  const [scrollDirection, setScrollDirection] = useState<ScrollDirection>(null);
  const [error, setError] = useState<string | null>(null);

  const lastScrollY = useRef<number>(0);
  const ticking = useRef<boolean>(false);

  const updateScrollDirection = useCallback(
    (scrollY: number) => {
      try {
        const direction: ScrollDirection =
          scrollY > lastScrollY.current ? "down" : "up";

        if (
          direction !== scrollDirection &&
          Math.abs(scrollY - lastScrollY.current) > 5
        ) {
          setScrollDirection(direction);
        }

        lastScrollY.current = scrollY > 0 ? scrollY : 0;
        ticking.current = false;
      } catch (err) {
        setError("An error occurred while detecting scroll direction.");
        console.error(err);
      }
    },
    [scrollDirection]
  );

  const onScroll = useCallback(() => {
    const currentElement =
      ref?.current || document.documentElement || document.body;

    if (!ticking.current) {
      const scrollY = currentElement.scrollTop || window.pageYOffset;
      window.requestAnimationFrame(() => updateScrollDirection(scrollY));
      ticking.current = true;
    }
  }, [updateScrollDirection, ref]);

  const onTouchStart = useCallback((e: TouchEvent) => {
    lastScrollY.current = e.touches[0].clientY;
  }, []);

  const onTouchMove = useCallback(
    (e: TouchEvent) => {
      const scrollY = e.touches[0].clientY;
      if (!ticking.current) {
        window.requestAnimationFrame(() => updateScrollDirection(scrollY));
        ticking.current = true;
      }
    },
    [updateScrollDirection]
  );

  useEffect(() => {
    const currentElement = ref?.current || window;

    try {
      currentElement.addEventListener("scroll", onScroll as EventListener);
      currentElement.addEventListener("wheel", onScroll as EventListener);

      currentElement.addEventListener(
        "touchstart",
        onTouchStart as EventListener
      );
      currentElement.addEventListener(
        "touchmove",
        onTouchMove as EventListener
      );
    } catch (err) {
      setError("Failed to add scroll/wheel/touch event listeners.");
      console.error(err);
    }

    return () => {
      try {
        currentElement.removeEventListener("scroll", onScroll as EventListener);
        currentElement.removeEventListener("wheel", onScroll as EventListener);

        currentElement.removeEventListener(
          "touchstart",
          onTouchStart as EventListener
        );
        currentElement.removeEventListener(
          "touchmove",
          onTouchMove as EventListener
        );
      } catch (err) {
        setError("Failed to remove scroll/wheel/touch event listeners.");
        console.error(err);
      }
    };
  }, [onScroll, onTouchStart, onTouchMove, ref]);

  if (error) {
    console.error(error);
  }

  return scrollDirection;
};

export default useScrollDirection;
