import { useState, useEffect } from "react";

type FaviconUrl = string | null;

const useFavicon = (url?: string): FaviconUrl => {
  const [favicon, setFavicon] = useState<FaviconUrl>(null);

  useEffect(() => {
    const fetchFavicon = async () => {
      try {
        if (!url) return;

        const finalURL =
          url.startsWith("http://") || url.startsWith("https://")
            ? `http://www.google.com/s2/favicons?sz=180&domain=${url}`
            : `http://www.google.com/s2/favicons?sz=180&domain=http://${url}`;

        setFavicon(finalURL);
      } catch (error) {
        console.error("Error fetching favicon:", error);
      }
    };

    fetchFavicon();
  }, [url]);

  return favicon;
};

export default useFavicon;
