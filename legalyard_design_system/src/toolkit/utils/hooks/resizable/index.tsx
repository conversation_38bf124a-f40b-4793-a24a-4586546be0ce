import React, { FC, ReactNode, useCallback, useEffect, useState } from "react";

export interface ResizableProps {
  children: (props: {
    ref: React.RefCallback<HTMLElement>;
    width: number | null;
  }) => ReactNode;
  minWidth?: number;
  onWidthChange?: (d?: any) => void;
}

const Resizable: FC<ResizableProps> = ({
  children,
  minWidth,
  onWidthChange,
}) => {
  const [node, setNode] = useState<HTMLElement | null>(null);
  const [width, setWidth] = useState<number | null>(null);

  const ref = useCallback((nodeEle: HTMLElement | null) => {
    setNode(nodeEle);
  }, []);

  const getWidth = useCallback(
    (w: number, dx: number): number => {
      try {
        return w + dx;
      } catch (error) {
        console.error(error);
        return minWidth ?? 1;
      }
    },
    [minWidth]
  );

  const updateWidth = useCallback(
    (wdt: number) => {
      if (onWidthChange) {
        onWidthChange(wdt);
      }
      setWidth(wdt);
    },
    [onWidthChange]
  );

  const handleMouseDown = useCallback(
    (e: MouseEvent) => {
      try {
        if (!node) {
          return;
        }

        const parent = node.parentElement;
        if (!parent) {
          return;
        }

        const startPos = {
          x: e.clientX,
          y: e.clientY,
        };
        const styles = window.getComputedStyle(parent);
        const w = parseInt(styles.width, 10);

        const handleMouseMove = (e: MouseEvent) => {
          const dx = e.clientX - startPos.x;
          if (minWidth && Boolean(parent.style.width)) {
            if (getWidth(w, dx) >= minWidth) {
              parent.style.width = `${w + dx}px`;
            }
          } else {
            parent.style.width = `${w + dx}px`;
          }
          updateWidth(parseInt(String(parent.style.width)));
          updateCursor();
        };

        const handleMouseUp = () => {
          document.removeEventListener("mousemove", handleMouseMove);
          document.removeEventListener("mouseup", handleMouseUp);
          resetCursor();
        };

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
      } catch (error) {
        console.error(error);
      }
    },
    [getWidth, minWidth, node, updateWidth]
  );

  const handleTouchStart = useCallback(
    (e: TouchEvent) => {
      try {
        if (!node) {
          return;
        }
        const parent = node.parentElement;
        if (!parent) {
          return;
        }

        const touch = e.touches[0];
        const startPos = {
          x: touch.clientX,
          y: touch.clientY,
        };
        const styles = window.getComputedStyle(parent);
        const w = parseInt(styles.width, 10);

        const handleTouchMove = (e: TouchEvent) => {
          const touch = e.touches[0];
          const dx = touch.clientX - startPos.x;
          if (minWidth && Boolean(parent.style.width)) {
            if (getWidth(w, dx) >= minWidth) {
              parent.style.width = `${w + dx}px`;
            }
          } else {
            parent.style.width = `${w + dx}px`;
          }
          updateWidth(parseInt(String(parent.style.width)));
          updateCursor();
        };

        const handleTouchEnd = () => {
          document.removeEventListener("touchmove", handleTouchMove);
          document.removeEventListener("touchend", handleTouchEnd);
          resetCursor();
        };

        document.addEventListener("touchmove", handleTouchMove);
        document.addEventListener("touchend", handleTouchEnd);
      } catch (error) {
        console.error(error);
      }
    },
    [getWidth, minWidth, node, updateWidth]
  );

  const updateCursor = () => {
    document.body.style.cursor = "col-resize";
    document.body.style.userSelect = "none";
  };

  const resetCursor = () => {
    document.body.style.removeProperty("cursor");
    document.body.style.removeProperty("user-select");
  };

  useEffect(() => {
    if (!node) {
      return;
    }
    node.addEventListener("mousedown", handleMouseDown);
    node.addEventListener("touchstart", handleTouchStart);

    return () => {
      node.removeEventListener("mousedown", handleMouseDown);
      node.removeEventListener("touchstart", handleTouchStart);
    };
  }, [node, handleMouseDown, handleTouchStart]);

  return <>{children({ ref, width })}</>;
};

export default Resizable;
