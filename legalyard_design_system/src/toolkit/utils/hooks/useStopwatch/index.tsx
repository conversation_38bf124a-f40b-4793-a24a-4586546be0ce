import { useCallback, useEffect, useState } from "react";

interface StopwatchConfig {
  start?: boolean;
  stopTimer?: boolean;
  restart?: boolean;
  onFinish?: () => void;
}

interface UseStopwatchReturn {
  time: string | number;
  done: boolean;
}

const useStopwatch = (
  initialSeconds: number = 60,
  config?: StopwatchConfig
): UseStopwatchReturn => {
  const [timeLeft, setTimeLeft] = useState<number>(initialSeconds);
  const [isDone, setIsDone] = useState<boolean>(true);

  useEffect(() => {
    if (config?.start) {
      setTimeLeft(initialSeconds);
      setIsDone(false);
    }
  }, [config?.start, initialSeconds, setIsDone, setTimeLeft]);

  useEffect(() => {
    let timer: NodeJS.Timeout | undefined;

    if (config?.start && !isDone && timeLeft > 0) {
      timer = setTimeout(() => setTimeLeft((prev) => prev - 1), 1000);
    } else if (timeLeft <= 0) {
      setIsDone(true);
      config?.onFinish?.();
    }

    return () => clearTimeout(timer);
  }, [config?.start, timeLeft, isDone, config]);

  const handleStop = useCallback(() => {
    setTimeLeft(0);
    setIsDone(true);
  }, []);

  useEffect(() => {
    if (config?.stopTimer) {
      handleStop();
    }
  }, [config?.stopTimer, handleStop]);

  const handleRestart = useCallback(() => {
    setTimeLeft(initialSeconds);
    setIsDone(false);
  }, [initialSeconds]);

  useEffect(() => {
    if (config?.restart) {
      handleRestart();
    }
  }, [config?.restart, handleRestart]);

  const mins = Math.floor(timeLeft / 60);
  const secs = timeLeft % 60;
  const formattedTime = `${mins.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;

  return { time: formattedTime === "00:00" ? 0 : formattedTime, done: isDone };
};

export default useStopwatch;
