import { useEffect, useRef } from "react";

interface Location {
  pathname: string;
}

const useLastLocation = (
  location: Location
): { lastPath: string | null; currentPath: string } => {
  const lastPathRef = useRef<string | null>(null);

  useEffect(() => {
    lastPathRef.current = location.pathname;
  }, [location.pathname]);

  return {
    lastPath: lastPathRef.current,
    currentPath: location.pathname,
  };
};

export default useLastLocation;
