import {
  RefObject,
  TouchEventHandler,
  useState,
  WheelEventHandler,
} from "react";

const useScroll = (ref: RefObject<HTMLElement>) => {
  const [translateY, setTranslateY] = useState<number>(0);

  const handleWheelScroll: WheelEventHandler<HTMLDivElement> = (e) => {
    try {
      const container = ref?.current;
      if (!container) return;

      const maxScrollTop = container.scrollHeight - container.clientHeight;

      const delta = e.deltaY || -e.detail || 0;

      let newTranslateY = translateY - delta * 30;
      newTranslateY = Math.max(-maxScrollTop, Math.min(0, newTranslateY));

      setTranslateY(newTranslateY);
      e.stopPropagation();
    } catch (err) {
      console.error(err);
    }
  };

  const handleTouchMove: TouchEventHandler<HTMLDivElement> = (e) => {
    try {
      const container = ref?.current;
      if (!container) return;

      const maxScrollTop = container.scrollHeight - container.clientHeight;

      const delta = e.touches[0].clientY || 0;

      let newTranslateY = translateY - delta * 30;
      newTranslateY = Math.max(-maxScrollTop, Math.min(0, newTranslateY));

      setTranslateY(newTranslateY);
      e.stopPropagation();
    } catch (err) {
      console.error(err);
    }
  };

  return {
    translateY,
    wheelScroll: handleWheelScroll,
    touchMove: handleTouchMove,
  };
};

export default useScroll;
