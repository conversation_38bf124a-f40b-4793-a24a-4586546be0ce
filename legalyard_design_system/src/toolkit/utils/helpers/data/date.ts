import { monthData } from "../../../ui/input/date/data";

export const toDate = (date: string) => {
  try {
    if (!date) {
      return null;
    }

    const newDate = new Date(date);
    return newDate;
  } catch (err) {
    console.error(err);
  }
};

export const toRenderDate = (date: string, withMonthName: boolean = false) => {
  try {
    const customDate = new Date(date);

    const day: string = customDate.getUTCDate().toString().padStart(2, "0");
    const month: string = (customDate.getUTCMonth() + 1)
      .toString()
      .padStart(2, "0");
    const finalMonth: string = withMonthName
      ? monthData[Number(month)]?.short
      : month;
    const year: string = customDate.getUTCFullYear().toString();

    const standardFormate = `${day}/${finalMonth}/${year}`;
    const nameFormate = `${finalMonth} ${day}, ${year}`;

    return withMonthName ? nameFormate : standardFormate;
  } catch (err) {
    console.error(err);
  }
};

export const toRenderTime = (date: string) => {
  try {
    const customDate = new Date(date);

    const hours = customDate.getHours() % 12 || 12;
    const minutes = customDate.getMinutes().toString().padStart(2, "0");
    const ampm = customDate.getHours() >= 12 ? "PM" : "AM";

    return `${hours}:${minutes} ${ampm}`;
  } catch (err) {
    console.error(err);
  }
};

export const getReadableDate = (date: {
  day: string | number;
  month: string | number;
  year: string | number;
}) => {
  try {
    const newDate = new Date(`${date?.month} ${date?.day}, ${date?.year}`);

    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };

    return newDate.toLocaleDateString(undefined, options);
  } catch (err) {
    console.error(err);
  }
};

export const getDaysInMonth = (year: number, month: number) => {
  try {
    return new Date(year, month + 1, 0).getDate();
  } catch (err) {
    console.error(err);
  }
};

export const getStartingDay = (year: number, month: number) => {
  try {
    return new Date(year, month, 1).getDay();
  } catch (err) {
    console.error(err);
  }
};

export const time24HrFormate = () => {
  try {
    const times = [];
    for (let hours = 0; hours < 24; hours++) {
      for (let minutes = 0; minutes < 60; minutes += 15) {
        const formattedHours = hours < 10 ? "0" + hours : hours;
        const formattedMinutes = minutes === 0 ? "00" : minutes;
        const time = formattedHours + ":" + formattedMinutes;
        times.push(time);
      }
    }
    return times;
  } catch (err) {
    console.error(err);
  }
};

export const time12HrFormate = (difference: number) => {
  try {
    const times = [];
    const minuteDiff = difference ?? 15;
    let period;

    for (let hours = 0; hours < 24; hours++) {
      for (let minutes = 0; minutes < 60; minutes += minuteDiff) {
        period = hours < 12 ? "AM" : "PM";
        const formattedHours = hours % 12 === 0 ? 12 : hours % 12;
        const formattedMinutes = minutes === 0 ? "00" : minutes;
        const time = formattedHours + ":" + formattedMinutes + " " + period;
        times.push(time);
      }
    }
    return times;
  } catch (err) {
    console.error(err);
  }
};

export const get24Hr = () => {
  try {
    const times = [];
    let period;

    for (let hours = 0; hours < 24; hours++) {
      period = hours < 12 ? "AM" : "PM";
      const formattedHours = hours % 12 === 0 ? 12 : hours % 12;
      const time = formattedHours + " " + period;
      times.push(time);
    }
    return times;
  } catch (err) {
    console.error(err);
  }
};

export const add30Minutes = (time: string) => {
  try {
    const [hours, period] = time.split(" ");
    const numericHours = parseInt(hours);
    const newHours = numericHours % 12 || 12;
    const newTime = `${newHours}:30 ${period}`;

    return newTime;
  } catch (err) {
    console.error(err);
  }
};

export const calculateDuration = (startingTime: string, endingTime: string) => {
  try {
    const convertTo24Hour = (time: string) => {
      const regexFlexible = /^(\d{1,2})[:.]?(\d{0,2})\s?(am|pm)?$/i;
      const match = time.match(regexFlexible);

      if (match) {
        let hours = parseInt(match[1]);
        const minutes = parseInt(match[2]) || 0;
        const period = (match[3] || "").toLowerCase();

        if (period === "pm" && hours !== 12) {
          hours += 12;
        } else if (period === "am" && hours === 12) {
          hours = 0;
        }

        return `${hours < 10 ? "0" + hours : hours}:${
          minutes < 10 ? "0" + minutes : minutes
        }`;
      }
      return time;
    };

    const startTime: any = new Date(
      `2023-01-01 ${convertTo24Hour(startingTime)}`
    );
    const endTime: any = new Date(`2023-01-01 ${convertTo24Hour(endingTime)}`);
    const timeDifference = endTime - startTime;
    const hours = Math.floor(timeDifference / (1000 * 60 * 60));
    const minutes = Math.floor(
      (timeDifference % (1000 * 60 * 60)) / (1000 * 60)
    );
    const formattedResult = `${hours}.${minutes}`;

    return formattedResult;
  } catch (err) {
    console.error(err);
  }
};

export const convertTo24HourFormat = (time: string) => {
  try {
    // time formate: 11:15 pm
    const regexWithMinutes = /^(\d{1,2}):(\d{2}) (am|pm)$/i;
    const regexWithoutMinutes = /^(\d{1,2})[:.]?(\d{0,2}) (am|pm)$/i;
    const matchWithMinutes = time.match(regexWithMinutes);
    const matchWithoutMinutes = time.match(regexWithoutMinutes);

    let hours, minutes, period;

    if (matchWithMinutes) {
      hours = parseInt(matchWithMinutes[1]);
      minutes = parseInt(matchWithMinutes[2]);
      period = matchWithMinutes[3].toUpperCase();
    } else if (matchWithoutMinutes) {
      hours = parseInt(matchWithoutMinutes[1]);
      minutes = parseInt(matchWithoutMinutes[2]) || 0;
      period = matchWithoutMinutes[3].toUpperCase();
    } else {
      return "Invalid time format";
    }

    if (period === "PM" && hours !== 12) {
      hours += 12;
    } else if (period === "AM" && hours === 12) {
      hours = 0;
    }

    const formattedResult = `${hours < 10 ? "0" + hours : hours}:${
      minutes < 10 ? "0" + minutes : minutes
    }`;

    return formattedResult;
  } catch (err) {
    console.error(err);
  }
};

export const convertTo12HourFormat = (time: string) => {
  try {
    // time formate: 22:10
    const regexWithMinutes = /^(\d{1,2}):(\d{2})$/;
    const regexWithoutMinutes = /^(\d{1,2})[:.]?(\d{0,2})$/;
    const matchWithMinutes = time.match(regexWithMinutes);
    const matchWithoutMinutes = time.match(regexWithoutMinutes);

    let hours, minutes, period;

    if (matchWithMinutes) {
      hours = parseInt(matchWithMinutes[1]);
      minutes = parseInt(matchWithMinutes[2]);
    } else if (matchWithoutMinutes) {
      hours = parseInt(matchWithoutMinutes[1]);
      minutes = parseInt(matchWithoutMinutes[2]) || 0;
    } else {
      return "Invalid time format";
    }

    if (hours >= 12) {
      period = "PM";
    } else {
      period = "AM";
    }

    if (hours > 12) {
      hours -= 12;
    } else if (hours === 0) {
      hours = 12;
    }

    const formattedResult = `${hours < 10 ? "0" + hours : hours}:${
      minutes < 10 ? "0" + minutes : minutes
    } ${period}`;

    return formattedResult;
  } catch (err) {
    console.error(err);
  }
};

export const calculateEndTime = (
  startingTime: string,
  additionalMinutes: number
) => {
  try {
    const regexFlexible = /^(\d{1,2})[:.]?(\d{0,2})\s?(am|pm)?$/i;
    const match = startingTime.match(regexFlexible);

    let hours, minutes, period;

    if (match) {
      hours = parseInt(match[1]);
      minutes = parseInt(match[2]) || 0;
      period = (match[3] || "").toLowerCase();

      if (period === "pm" && hours !== 12) {
        hours += 12;
      } else if (period === "am" && hours === 12) {
        hours = 0;
      }
    } else {
      return "Invalid time format";
    }

    const totalMinutes = hours * 60 + minutes + additionalMinutes;
    const endHours = Math.floor(totalMinutes / 60) % 24;
    const endMinutes = totalMinutes % 60;

    const formattedResult = {
      startingTime: `${hours < 10 ? "0" + hours : hours}:${
        minutes < 10 ? "0" + minutes : minutes
      }`,
      endingTime: `${endHours < 10 ? "0" + endHours : endHours}:${
        endMinutes < 10 ? "0" + endMinutes : endMinutes
      }`,
    };

    return formattedResult;
  } catch (err) {
    console.error(err);
  }
};

export const convertTimeToDecimal = (time: string) => {
  try {
    const timeRegex = /^(\d{1,2})([:.](\d{2}))?\s?(am|pm)?$/i;
    const match = time.match(timeRegex);

    if (match) {
      let hours = parseInt(match[1]);
      const minutes = match[3] ? parseInt(match[3]) : 0;
      const period = match[4] ? match[4].toLowerCase() : "am";

      if (period === "pm" && hours !== 12) {
        hours += 12;
      } else if (period === "am" && hours === 12) {
        hours = 0;
      }

      const decimalTime = hours + minutes / 60;
      return decimalTime.toFixed(1);
    }

    return null;
  } catch (err) {
    console.error(err);
  }
};

export const getTimeAddition = (time: string, minutesToAdd: number) => {
  try {
    const timeRegex = /^(\d{1,2})(?::(\d{2}))?\s?(am|pm)?$/i;
    const match = time.match(timeRegex);

    if (match) {
      let hours = parseInt(match[1]);
      let minutes = match[2] ? parseInt(match[2]) : 0;
      const period = match[3] ? match[3].toLowerCase() : "am";

      if (period === "pm" && hours !== 12) {
        hours += 12;
      } else if (period === "am" && hours === 12) {
        hours = 0;
      }

      hours += Math.floor(minutesToAdd / 60);
      minutes += minutesToAdd % 60;

      if (hours === 24) {
        hours = 0;
      }

      if (hours < 0) hours = 0;
      if (hours > 23) hours = 23;
      if (minutes < 0) minutes = 0;
      if (minutes > 59) minutes = 59;

      return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
        2,
        "0"
      )}`;
    }

    return null;
  } catch (err) {
    console.error(err);
  }
};

export const getTimeSubtraction = (time: string, minutesToSubtract: number) => {
  try {
    const timeRegex = /^(\d{1,2})(?::(\d{2}))?\s?(am|pm)?$/i;
    const match = time.match(timeRegex);

    if (match) {
      let hours = parseInt(match[1]);
      let minutes = match[2] ? parseInt(match[2]) : 0;
      const period = match[3] ? match[3].toLowerCase() : "am";

      if (period === "pm" && hours !== 12) {
        hours += 12;
      } else if (period === "am" && hours === 12) {
        hours = 0;
      }

      let totalMinutes = hours * 60 + minutes;
      totalMinutes -= minutesToSubtract;

      if (totalMinutes < 0) {
        totalMinutes = 0;
      }

      hours = Math.floor(totalMinutes / 60);
      minutes = totalMinutes % 60;

      return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
        2,
        "0"
      )}`;
    }

    return null;
  } catch (err) {
    console.error(err);
  }
};

export const formatTime = (time: string) => {
  try {
    const timeRegex = /^(\d{1,2})[:.](\d{1,2})\s?(am|pm)?$/i;
    const match = time.match(timeRegex);

    if (match) {
      let hours, minutes, period;

      hours = parseInt(match[1]);
      minutes = parseInt(match[2]);
      period = match[3] ? match[3].toUpperCase() : null;

      const formattedMinutes =
        String(minutes).length === 1 ? `${minutes}0` : String(minutes);
      const formattedTime = `${String(hours).padStart(
        2,
        "0"
      )}:${formattedMinutes}`;
      if (period) {
        return `${formattedTime} ${period}`;
      }

      return formattedTime;
    }

    return null;
  } catch (err) {
    console.error(err);
  }
};

export const shortenTime = (time: string) => {
  try {
    const timeRegex = /^(\d{2}[:.]\d{2})\s?(am|pm)$/i;
    const match = time.match(timeRegex);

    if (match) {
      let formattedTime = match[1];
      const period = match[2].toUpperCase();

      formattedTime = formattedTime.replace(/^0+/, "");
      formattedTime = formattedTime.replace(/(\d):00/, "$1");

      return `${formattedTime} ${period}`;
    }

    return time;
  } catch (err) {
    console.error(err);
  }
};

export const generateTimeStamps = () => {
  try {
    const timestamps: string[] = [];
    const hours = Array.from({ length: 24 }, (_, i) => i);
    const minutes = ["00", "15", "30", "45"];

    hours.forEach((hour) => {
      minutes.forEach((minute) => {
        const formattedHour = hour === 0 ? 12 : hour <= 12 ? hour : hour - 12;
        const period = hour < 12 ? "AM" : "PM";
        const time = `${
          formattedHour < 10 ? "0" + formattedHour : formattedHour
        }:${minute} ${period}`;
        timestamps.push(time);
      });
    });

    return timestamps;
  } catch (error) {
    console.error(error);
    return [];
  }
};

export const getCurrentTime = (futureTimeInMinutes = 0) => {
  try {
    const currentTime = new Date();
    let hours = currentTime.getHours();
    let minutes = currentTime.getMinutes();

    if (futureTimeInMinutes > 0) {
      const futureTime = new Date(
        currentTime.getTime() + futureTimeInMinutes * 60 * 1000
      );
      hours = futureTime.getHours();
      minutes = futureTime.getMinutes();
    }

    const amPM = hours >= 12 ? "PM" : "AM";
    hours = hours % 12 || 12;
    const formattedHours = hours < 10 ? "0" + hours : hours;
    const formattedMinutes = minutes < 10 ? "0" + minutes : minutes;

    const timeString = `${formattedHours}:${formattedMinutes} ${amPM}`;
    return timeString;
  } catch (error) {
    console.error(error);
  }
};
