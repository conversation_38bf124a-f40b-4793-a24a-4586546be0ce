import { color } from "../../../styles/variables";
import { validArray } from "./array";

export const getRandomColor = (colors?: string[]) => {
  try {
    const localColors = [
      color.main,
      color.orange,
      color.blue,
      color.skyBlue,
      color.red,
      color.green,
      color.yellow,
    ];

    const finalColors = validArray(colors) ? colors : localColors;

    if (validArray(finalColors)) {
      const randomIndex = Math.floor(
        Math.random() * (finalColors as any)?.length
      );
      return (finalColors as any)[randomIndex];
    } else {
      return "orange";
    }
  } catch (error) {
    console.error(error);
    return "orange";
  }
};

export const getDominantColor = async (imageUrl: string) => {
  try {
    const image = new Image();
    image.src = imageUrl;

    let dominantColor = null;

    image.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      canvas.width = image.width;
      canvas.height = image.height;

      if (ctx) {
        ctx.drawImage(image, 0, 0);
      }

      let imageData: any = {};

      if (ctx) {
        imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);
      }

      const data = Boolean(imageData) && imageData?.data;

      const colorCounts: any = {};

      for (let i = 0; i < data.length; i += 4) {
        const color = `rgb(${data[i]}, ${data[i + 1]}, ${data[i + 2]})`;
        colorCounts[color] = (colorCounts[color] || 0) + 1;
      }

      let maxCount = 0;
      for (const color in colorCounts) {
        if (colorCounts[color] > maxCount) {
          dominantColor = color;
          maxCount = colorCounts[color];
        }
      }
    };

    return dominantColor;
  } catch (error) {
    console.error(error);
    return "orange";
  }
};
