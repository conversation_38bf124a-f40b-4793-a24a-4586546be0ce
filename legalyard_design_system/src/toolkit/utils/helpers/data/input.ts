export const getSelectData = (data: any[], titleKey: string) => {
  try {
    return data?.map((item) => {
      return { title: item[titleKey], ...item };
    });
  } catch (err) {
    console.error(err);
  }
};

export const getChecksData = (data: any[], titleKey: string) => {
  try {
    return data?.map((item) => {
      return {
        label: item[titleKey],
        name: item[titleKey]?.replaceAll(" ", ""),
        ...item,
      };
    });
  } catch (err) {
    console.error(err);
  }
};

export const handleChecked = (values: any[], key: string) => {
  try {
    if (Array.isArray(values) && values?.includes(key)) {
      const val = values?.filter((item) => item !== key);
      return val;
    } else {
      const initialArr = Array.isArray(values) ? [...values] : [];
      const val = [...initialArr, key];
      return val;
    }
  } catch (err) {
    console.error(err);
  }
};
