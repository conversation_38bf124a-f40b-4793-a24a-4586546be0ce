import * as ALLData from "@/toolkit";
import React, { FunctionComponent } from "react";
import Header from "../../elements/header";
import Section from "../../elements/section/sub";
import SitemapSections from "./section";

const isComponent = (item: any): item is FunctionComponent =>
  typeof item === "function" && item.prototype?.isReactComponent;

const isFunction = (item: any): item is Function =>
  typeof item === "function" && !isComponent(item);

const isObject = (item: any): item is Record<string, unknown> =>
  typeof item === "object" && item !== null;

const isTypeDeclaration = (item: any): boolean => {
  const keys = Object.keys(item);
  return keys.some((key) => key.startsWith("I") || key.includes("Type"));
};

const getCodeSnippet = (item: any): string => {
  try {
    if (typeof item === "function") {
      return item.toString();
    }
    if (typeof item === "object") {
      return JSON.stringify(item, null, 2);
    }
    return String(item);
  } catch (error) {
    return "// Error retrieving code snippet";
  }
};

const categorizeData = (data: Record<string, any>) => {
  const components: [string, any][] = [];
  const functions: [string, any][] = [];
  const objects: [string, any][] = [];
  const typeDeclarations: [string, any][] = [];

  Object.entries(data).forEach(([key, value]) => {
    try {
      if (isComponent(value)) {
        components.push([key, value]);
      } else if (isFunction(value)) {
        functions.push([key, value]);
      } else if (isObject(value)) {
        if (isTypeDeclaration(value)) {
          typeDeclarations.push([key, value]);
        } else {
          objects.push([key, value]);
        }
      }
    } catch (error) {
      console.error(`Error while categorizing ${key}:`, error);
    }
  });

  return { components, functions, objects, typeDeclarations };
};

const NotInvoke = ["fileDownload", "textDownload", "urlDownload"];

const RenderToolkitData: React.FC = () => {
  const { components, functions, objects, typeDeclarations } =
    categorizeData(ALLData);

  return (
    <div className="container mx-auto p-page">
      <Header
        title="All Toolkit"
        description="See the list of all available components, functions, Types, & Data Objects"
      />

      {components.length > 0 && (
        <Section title="Components" id="Components">
          {components.map(([key, Component]) => (
            <SitemapSections
              key={key}
              title={key}
              code={getCodeSnippet(Component)}
            >
              <div>
                {renderWithErrorBoundary(
                  key,
                  () => (
                    <Component />
                  ),
                  "Component"
                )}
              </div>
            </SitemapSections>
          ))}
        </Section>
      )}

      {functions.length > 0 && (
        <Section title="Functions" id="Functions">
          {functions.map(([key, func]) => (
            <SitemapSections key={key} title={key} code={getCodeSnippet(func)}>
              <div>
                {!NotInvoke?.includes(key) && (
                  <>
                    {renderWithErrorBoundary(
                      key,
                      () => func?.().toString(),
                      "Function"
                    )}
                  </>
                )}
              </div>
            </SitemapSections>
          ))}
        </Section>
      )}

      {objects.length > 0 && (
        <Section title="Objects" id="Objects">
          {objects.map(([key, obj]) => (
            <SitemapSections key={key} title={key} code={getCodeSnippet(obj)}>
              <div>
                {renderWithErrorBoundary(
                  key,
                  () => JSON.stringify(obj),
                  "Object"
                )}
              </div>
            </SitemapSections>
          ))}
        </Section>
      )}

      {typeDeclarations.length > 0 && (
        <Section title="Type Declarations" id="Type Declarations">
          {typeDeclarations.map(([key, typeDecl]) => (
            <SitemapSections
              key={key}
              title={key}
              code={getCodeSnippet(typeDecl)}
            >
              <div>
                {renderWithErrorBoundary(
                  key,
                  () => JSON.stringify(typeDecl),
                  "Type Declaration"
                )}
              </div>
            </SitemapSections>
          ))}
        </Section>
      )}
    </div>
  );
};

const renderWithErrorBoundary = (
  key: string,
  renderFn: () => JSX.Element | string,
  type: "Component" | "Function" | "Object" | "Type Declaration"
) => {
  try {
    return <span>{renderFn()}</span>;
  } catch (error) {
    console.error(`Error rendering ${type} "${key}":`, error);
    return <span style={{ color: "red" }}>Error rendering {type}</span>;
  }
};

export default RenderToolkitData;
