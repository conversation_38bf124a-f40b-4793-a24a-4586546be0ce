import React, { FC, ReactNode } from "react";
import CollapseHOC from "../../elements/section/sub/collapse";
import SubSection from "../../elements/section";
import CodeBlock from "../../elements/code";

interface PropsType {
  children?: ReactNode;
  title: string;
  code: string;
}

const SitemapSections: FC<PropsType> = ({ children, title, code }) => {
  return (
    <CollapseHOC
      id={title}
      title={title}
      className="border-b border-b-dark border-dashed "
    >
      <SubSection className="border-l border-l-main ps-6 text-font">
        {code && (
          <CodeBlock code={code} language={"javascript"} preview={children} />
        )}
      </SubSection>
    </CollapseHOC>
  );
};

export default SitemapSections;
