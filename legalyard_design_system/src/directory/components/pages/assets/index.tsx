import { validArray } from "@/toolkit/utils";
import { useParams } from "react-router-dom";
import CodeBlock from "../../elements/code";
import Header from "../../elements/header";
import SubSection from "../../elements/section";
import CollapseHOC from "../../elements/section/sub/collapse";
import { uiData } from "./data";

const Assets = () => {
  const { type } = useParams();

  const filterData = () => {
    try {
      if (type) {
        return uiData?.filter((item) => item?.id === type);
      } else {
        return uiData;
      }
    } catch (error) {
      return [];
    }
  };

  const finalData = filterData();

  return (
    <div className="container mx-auto p-page">
      <Header title="Assets" description="Learn how to use the assets" />

      {validArray(finalData) ? (
        finalData?.map((section, index) => {
          return (
            <CollapseHOC
              key={index}
              title={section?.title}
              id={section?.id}
              className="border-b border-b-dark border-dashed "
            >
              {validArray(section?.description) &&
                section?.description?.map((dsc, index) => (
                  <p
                    key={index}
                    className="text-zinc-600 text-sb leading-tight mb-4"
                  >
                    {dsc}
                  </p>
                ))}

              {section?.section?.map((sec, codeIndex) => {
                return (
                  <SubSection
                    key={codeIndex}
                    title={sec?.title}
                    className="border-l border-l-main ps-6 text-font"
                  >
                    {validArray(sec?.description) &&
                      sec?.description?.map((dsc, index) => (
                        <p
                          key={index}
                          className="text-zinc-600 text-sb leading-tight mb-4"
                        >
                          {dsc}
                        </p>
                      ))}
                    {sec?.code && (
                      <CodeBlock
                        code={sec?.code}
                        language={sec?.language ?? "javascript"}
                        preview={sec?.preview}
                      />
                    )}
                  </SubSection>
                );
              })}
            </CollapseHOC>
          );
        })
      ) : (
        <span className="text-red">Data not found</span>
      )}
    </div>
  );
};

export default Assets;
