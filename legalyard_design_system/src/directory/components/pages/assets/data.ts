import { createElement, ReactNode } from "react";

export interface dataType {
  id: string;
  title: string;
  description?: string[];
  section: {
    title: string;
    code: string;
    description?: string[];
    language?: string;
    preview?: ReactNode;
  }[];
}

export const uiData: dataType[] = [
  {
    id: "branding",
    title: "Branding",
    section: [
      {
        title: "Import: main logo",
        language: "javascript",
        code: `...`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement("img", {
            // src: LOGO,
          })
        ),
      },
    ],
  },
];
