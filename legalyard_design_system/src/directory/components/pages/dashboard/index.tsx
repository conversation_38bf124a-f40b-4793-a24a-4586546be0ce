import { menuData } from "@/directory/router/data/menu";
import { Fragment } from "react/jsx-runtime";
import Section from "../..//elements/section/sub";
import Card from "../../elements/card";
import Header from "../../elements/header";

const Dashboard = () => {
  return (
    <div className="p-page">
      <Header title="Dashboard" />

      {menuData
        ?.filter((item) => item?.title?.toLowerCase() !== "dashboard")
        ?.map((section, index) => {
          return (
            <Section key={index} title={section?.title} id={section?.path}>
              {section?.data?.map((menuSection, menuSectionIndex) => (
                <div
                  key={menuSectionIndex}
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-3"
                >
                  {menuSection?.menu?.map((menu, menuIndex) => {
                    return (
                      <Card
                        key={menuIndex}
                        title={menu?.name}
                        path={menu?.path}
                      />
                    );
                  })}
                </div>
              ))}
            </Section>
          );
        })}
    </div>
  );
};

export default Dashboard;
