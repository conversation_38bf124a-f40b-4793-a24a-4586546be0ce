import { DropdownMenu } from "@/toolkit";
import { Dropdown } from "@/toolkit";
import { MoreHorizontal } from "react-feather";

const DropdownRender = () => {
  return (
    <Dropdown content={<MoreContent />} contentClassName="z-20">
      <button className="flex items-center justify-center p-2 rounded-base">
        <div className="group flex flex-col items-center justify-center gap-2 ">
          <span className="border border-slate-300 group-hover:border-main text-slate-500 group-hover:text-main rounded-full flex items-center justify-center p-2 transition-all">
            <MoreHorizontal />
          </span>
          <span className="text-slate-600 group-hover:text-main transition-all text-sm ">
            More
          </span>
        </div>
      </button>
    </Dropdown>
  );
};

const MoreContent = () => {
  // const { handleClose } = useDropdown();

  return (
    <DropdownMenu
      search
      collapsible
      data={moreMenuData()}
      className={"bg-white !h-full !w-72 font-[400] z-50 overflow-auto"}
      fixContent={<div className="p-2 bg-amber-700 text-white sticky bottom-0 mt-2">Fixed Content</div>}
    />
  );
};

export const moreMenuData = () => {
  const ids: number[] = [10, 20, 50, 100];

  return [
    {
      title: "Public views",
      menu: ids?.map((item: number) => {
        return {
          title: `${item} View`,
          event: () => {},
        };
      }),
    },
    {
      title: "Created by me",
      menu: ids?.map((item: number) => {
        return {
          title: `${item * 2} View`,
          event: () => {},
        };
      }),
    },
  ];
};

export default DropdownRender;
