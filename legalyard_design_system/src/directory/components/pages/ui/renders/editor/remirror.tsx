// App.tsx
import { RemirrorEditor, RemirrorEditorReferenceType } from "@/toolkit";
import React, { useRef, useState } from "react";

const RemirrorEditorRender: React.FC = () => {
  const editorRef = useRef<RemirrorEditorReferenceType>(null);
  const [content, setContent] = useState<string>("");

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
  };

  const focusEditor = () => {
    // if (editorRef?.current?.insertImage) {
    //   editorRef?.current?.insertImage(
    //     "https://images.unsplash.com/photo-1441372069168-3194f577beeb?fm=jpg"
    //   );
    // }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Remirror Rich Text Editor</h1>
      <RemirrorEditor
        ref={editorRef}
        valueFormat="html"
        onChange={(value) => console.log({ value })}
        extensions={{
          image: {
            onImageUpload: (file) => console.log({ file }),
            // selectedImageUrl:
            //   "https://images.unsplash.com/photo-1441372069168-3194f577beeb?fm=jpg",
          },
        }}
        toolbar={{
          className: "flex-wrap gap-1",
        }}
      />
      <button
        className="mt-4 bg-blue-600 text-font px-4 py-2 rounded-md"
        onClick={focusEditor}
      >
        Focus Editor
      </button>
      <pre className="mt-4 bg-gray-100 p-4">{content}</pre>
    </div>
  );
};

export default RemirrorEditorRender;
