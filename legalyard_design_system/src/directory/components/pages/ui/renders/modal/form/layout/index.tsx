import { ModalFormLayout } from "@/toolkit";
import { useRef } from "react";

const ModalFormLayoutRender = ({ onClose }: { onClose: () => void }) => {
  const ModalRef = useRef<HTMLDivElement>(null);
  return (
    <ModalFormLayout
      ref={ModalRef}
      onSubmit={(e) => console.log(e)}
      title="New Contact"
      button={{
        content: (
          <div className="flex items-center justify-center gap-x-2">
            Continue
          </div>
        ),
      }}
      className="overflow-auto"
    >
      <input />
    </ModalFormLayout>
  );
};

export default ModalFormLayoutRender;
