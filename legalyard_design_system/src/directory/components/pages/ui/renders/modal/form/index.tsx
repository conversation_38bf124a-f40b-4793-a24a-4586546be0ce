import { ModalFormHandler } from "@/toolkit";
import { useState } from "react";
import ModalFormLayoutRender from "./layout";

const ModalFormRender = () => {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <ModalFormHandler open={open} onClose={setOpen} close>
      <ModalFormLayoutRender onClose={() => setOpen(false)} />
    </ModalFormHandler>
  );
};

export default ModalFormRender;
