import { MiniCollapse } from "@/toolkit";
import { useState } from "react";

const MiniCollapseRender = () => {
  const [open, setOpen] = useState<boolean>(false);
  const [childOpen, setChildOpen] = useState<boolean>(false);

  return (
    <div>
      <button onClick={() => setOpen((prev) => !prev)}>Toggle collapse</button>
      <MiniCollapse open={open}>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Omnis harum
        doloribus magni modi dolor alias ratione optio fuga quae, facere eveniet
        ad eligendi odio? Aliquam quae recusandae cumque sint molestias. Lorem
        ipsum dolor sit amet consectetur adipisicing elit. Omnis harum doloribus
        magni modi dolor alias ratione optio fuga quae, facere eveniet ad
        eligendi odio? Aliquam quae recusandae cumque sint molestias. Lorem
        ipsum dolor sit amet consectetur adipisicing elit. Omnis harum doloribus
        magni modi dolor alias ratione optio fuga quae, facere eveniet ad
        eligendi odio? Aliquam quae recusandae cumque sint molestias. Lorem
        ipsum dolor sit amet consectetur adipisicing elit. Omnis harum doloribus
        magni modi dolor alias ratione optio fuga quae, facere eveniet ad
        eligendi odio? Aliquam quae recusandae cumque sint molestias.

        <button
          className="block mt-5 mb-2"
          onClick={() => setChildOpen((prev) => !prev)}
        >
          Child Open
        </button>
        <MiniCollapse open={childOpen}>
          <div className="flex flex-col gap-5 ps-2">
            {[...Array(10).keys()]?.map((_, index: number) => {
              return <span key={index}>email.gmail.com</span>;
            })}
          </div>
        </MiniCollapse>
      </MiniCollapse>
    </div>
  );
};

export default MiniCollapseRender;
