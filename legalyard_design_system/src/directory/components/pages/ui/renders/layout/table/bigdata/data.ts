export const bigDataTableFormate = [
  {
    _id: "{{objectId(66ca2151b2038dfb2582f258)}}",
    guid: "{{guid()}}",
    type: "{{random('person', 'company')}}",
    status: "{{random('active', 'inactive')}}",
    category: {
      _id: "{{objectId()}}",
      name: "{{random('client', 'reference', 'open')}}",
    },
    avatar: "https://i.pravatar.cc/500",
    salutation: "{{person.prefix()}}",
    firstName: "{{person.firstName()}}",
    middleName: "{{person.middleName()}}",
    lastName: "{{person.lastName()}}",
    company: {
      _id: "{{objectId()}}",
      logo: "https://i.pravatar.cc/500",
      companyName: "{{company.name().toUpperCase()}}",
    },
    companyName: "{{company.name()}}",
    owner: {
      _id: "{{objectId()}}",
      name: "{{person.fullName()}}",
      avatar: "https://i.pravatar.cc/200",
    },
    email: "{{internet.email()}}",
    emails: [
      "{{repeat(4)}}",
      {
        _id: "{{objectId()}}",
        email: "{{internet.email()}}",
      },
    ],
    countryPhoneCode: "+{{integer(1, 99)}}",
    phone: "{{phone.number('##########')}}",
    phones: [
      "{{repeat(4)}}",
      {
        _id: "{{objectId()}}",
        countryPhoneCode: "+{{integer(1, 99)}}",
        phone: "{{phone.number('##########')}}",
      },
    ],
    group: {
      _id: "{{objectId()}}",
      name: "{{lorem.words(1)}}",
    },
    address: [
      {
        _id: "{{objectId()}}",
        type: "primary",
        country: "{{location.state()}}",
        street: "{{location.street()}}",
        city: "{{location.city(5)}}",
        state: "{{location.state()}}",
        postalCode: "{{integer(100000, 999999)}}",
        label: "home",
      },
      {
        _id: "{{objectId()}}",
        type: "billing",
        country: "{{location.state()}}",
        street: "{{location.street()}}",
        city: "{{location.city(5)}}",
        state: "{{location.state()}}",
        postalCode: "{{integer(100000, 999999)}}",
        label: "work",
      },
      "{{repeat(4)}}",
      {
        _id: "{{objectId()}}",
        type: "addon",
        country: "{{location.state()}}",
        street: "{{location.street()}}",
        city: "{{location.city(5)}}",
        state: "{{location.state()}}",
        postalCode: "{{integer(100000, 999999)}}",
        label: "work",
      },
    ],
    billingOnPrimaryAddress: "{{boolean()}}",
    designation: "{{person.jobTitle()}}",
    department: "{{person.jobArea()}}",
    reportingTo: "{{person.fullName()}}",
    createdBy: {
      _id: "{{objectId()}}",
      name: "{{person.fullName()}}",
    },
    assistant: "{{person.fullName()}}",
    assistantPhone: "+{{integer(1, 99)}} {{phone.number()}}",
    gender: "{{person.gender()}}",
    description: "{{person.bio()}}",
    leadSource: "{{random('apple', 'banana', 'strawberry')}}",
    notes: "{{lorem.paragraph(1)}}",
    dates: [
      "{{repeat(3)}}",
      {
        _id: "{{objectId()}}",
        date: "{{date.past()}}",
        label: "{{lorem.words(1)}}",
      },
    ],
    customFields: [
      "{{repeat(3)}}",
      {
        _id: "{{objectId()}}",
        field: "{{lorem.words(2)}}",
        label: "{{lorem.words(1)}}",
      },
    ],
    relatedPersons: [
      "{{repeat(3)}}",
      {
        _id: "{{objectId()}}",
        person: "{{person.fullName()}}",
        label: "{{lorem.words(1)}}",
      },
    ],
    websites: [
      "{{repeat(3)}}",
      {
        _id: "{{objectId()}}",
        url: "{{internet.url()}}",
        label: "{{lorem.words(1)}}",
      },
    ],
    tags: [
      "{{repeat(7)}}",
      {
        _id: "{{objectId()}}",
        tag: "{{lorem.words(1)}}",
        icon: "https://i.pravatar.cc/200",
      },
    ],
    createdAt: "{{date.past()}}",
    updatedAt: "{{date.recent()}}",
  },
];
