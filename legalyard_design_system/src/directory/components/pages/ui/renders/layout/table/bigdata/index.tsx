import UserPlaceholder from "@/assets/placeholder/user.svg";
import apiClient from "@/directory/services/axios/auth-client";
import {
  BigDataTableTypes,
  Button,
  Loader,
  useScrollDirection,
} from "@/toolkit";
import classNames from "classnames";
import {
  createElement,
  lazy,
  RefObject,
  Suspense,
  useCallback,
  useEffect,
  useState,
} from "react";
import { User } from "react-feather";
import { bigDataTableFormate } from "./data";
import { useNavigate } from "react-router-dom";

const DataTable = lazy(() =>
  import("@/toolkit").then((module) => ({ default: module.BigDataTable }))
);

const BigDataTableRender = ({
  companies,
  parentRef,
}: {
  companies: boolean;
  parentRef?: RefObject<HTMLElement>;
}) => {
  const navigate = useNavigate();
  const scrollDirection = useScrollDirection({ ref: parentRef });

  const [loading, setLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any[]>([]);
  const [deleteAction, setDeleteAction] = useState(false);

  const fetchData = useCallback(() => {
    setLoading(true);
    apiClient
      .post("/mock/contacts/all?records=300", {
        structure: bigDataTableFormate,
      })
      .then((res) => {
        setTableData(res?.data);
      })
      .catch((error) => console.error(error))
      .finally(() => {
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <div className="w-full h-full flex-1">
      <Button className="mb-4" loading={loading} onClick={fetchData}>
        Refetch Data
      </Button>
      <Suspense fallback={<Loader />}>
        <DataTable
          wrapperClassName="!max-h-full"
          loading={loading}
          rows={{ data: tableData }}
          columns={{
            data: columnsData,
          }}
          checkbox={{
            onChange: (ids, rows) => console.log({ ids, rows }),
            pin: true,
          }}
          action={{
            component: (data) => {
              return (
                <h1 title="" className="w-full">
                  {data?.row?.id}
                </h1>
              );
            },
            onRowHover: true,
            pin: true,
          }}
          header={{
            show: true,
            className: classNames(
              "!sticky top-0 z-20 transition-all duration-300",
              scrollDirection === "down" ? "top-0" : "top-0"
            ),
            showcase: { show: true, dataCount: tableData?.length },
            operation: {
              show: true,
              deleteComplete: deleteAction,
              onDelete: (d) => console.log({ rows: d }),
              onEdit: (d) => console.log({ rows: d }),
            },
            search: {
              show: true,
              onChange: (d: any) => console.log({ search: d }),
              // getFiltered: (count, data) => console.log({ count, data }),
            },
            pagination: {
              show: true,
              onPageChange: (page: any) => console.log({ page }),
              onRowLimitChange: (d: any) => console.log(d),
            },
          }}
          navigation={{ url: "/contacts/[_id]" }}
          onNavigate={(path) => navigate(path ?? "")}
        />
      </Suspense>
    </div>
  );
};

const columnsData: BigDataTableTypes.ColumnsType[] = [
  {
    field: "firstName",
    renderField: ["firstName", "middleName", "lastName"],
    key: "contact_name",
    icon: "avatar",
    iconPlaceholder: UserPlaceholder,
    title: "Name",
    resize: true,
    width: 200,
    minWidth: 250,
    sortable: true,
    component: ({ row, rowIndex }) => {
      return (
        <tr key={row?.id + String(rowIndex)}>
          <div>{row?.firstName}</div>
        </tr>
      );
    },
  },
  {
    field: "company.companyName",
    key: "contact_company_name",
    title: "Company",
    icon: "company.logo",
    resize: true,
    width: 200,
    minWidth: 150,
    sortable: true,
    navigation: {
      url: "/contacts/[company._id]",
      newTab: true,
    },
  },
  {
    field: "email",
    key: "contact_email",
    title: "Email",
    icon: createElement(User),
    minWidth: 150,
    resize: true,
  },
  {
    field: "phone",
    key: "contact_phone",
    title: "Phone",
    minWidth: 150,
    resize: true,
  },
  {
    field: "createdAt",
    key: "contact_created_date",
    title: "Created date",
    minWidth: 150,
    resize: true,
  },
];

export default BigDataTableRender;
