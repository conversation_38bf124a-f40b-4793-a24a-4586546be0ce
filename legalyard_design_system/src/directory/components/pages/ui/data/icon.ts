import { createElement } from "react";
import { dataType } from ".";
import { Link } from "react-router-dom";
import { LegalyardLogo } from "@lds/toolkit";
import IconRender from "@/toolkit/ui/icon/render";
import { Menu } from "react-feather";

export const iconData: dataType[] = [
  {
    id: "icon",
    title: "icon",
    description: ["Render icon string or icon element e.g. Feather Icon"],
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { Icon } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<Icon src={Menu} />`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(IconRender, {
            icon: createElement(Menu, {
              className: "bg-pink h-12 w-12",
            }),
          })
        ),
      },
    ],
  },
];
