import { createElement } from "react";
import { dataType } from ".";
import BigDataTableRender from "../renders/layout/table/bigdata";

const bigDataTable: dataType[] = [
  {
    id: "bigDataTable",
    title: "Big Data Table",
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { BigDataTable } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<DataTable
  wrapperClassName="!max-h-full"
  loading={loading}
  rows={{ data: tableData }}
  columns={{
    data: columnsData,
  }}
  checkbox={{
    onChange: (ids, rows) => console.log({ ids, rows }),
    pin: true,
  }}
  action={{
    component: (data) => {
      return (
        <h1 title="" className="w-full">
          {data?.row?.id}
        </h1>
      );
    },
    onRowHover: true,
    pin: true,
  }}
  header={{
    show: true,
    className: classNames(
      "!sticky top-0 z-20 transition-all duration-300",
      scrollDirection === "down" ? "top-0" : "top-0"
    ),
    showcase: { show: true, dataCount: tableData?.length },
    operation: {
      show: true,
      deleteComplete: deleteAction,
      onDelete: (d) => console.log({ rows: d }),
      onEdit: (d) => console.log({ rows: d }),
    },
    search: {
      show: true,
      onChange: (d: any) => console.log({ search: d }),
      // getFiltered: (count, data) => console.log({ count, data }),
    },
    pagination: {
      show: true,
      onPageChange: (page: any) => console.log({ page }),
      onRowLimitChange: (d: any) => console.log(d),
    },
  }}
  navigation={{ url: "/contacts/[_id]" }}
  onNavigate={(path) => navigate(path ?? "")}
/>;
`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(BigDataTableRender)
        ),
      },
    ],
  },
];

export const layoutData = [...bigDataTable];
