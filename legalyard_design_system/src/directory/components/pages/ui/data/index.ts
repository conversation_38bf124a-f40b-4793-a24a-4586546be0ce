import { ReactNode } from "react";
import { buttonData } from "./button";
import { collapseData } from "./collapse";
import { dropdownData } from "./dropdown";
import { editorData } from "./editor";
import { iconData } from "./icon";
import { inputData } from "./input";
import { layoutData } from "./layout";
import { logoData } from "./logo";
import { tabsData } from "./tabs";

export interface dataType {
  id: string;
  title: string;
  description?: string[];
  section: {
    title: string;
    code: string;
    description?: string[];
    language?: string;
    preview?: ReactNode;
  }[];
}

const uiData: dataType[] = [
  ...buttonData,
  ...logoData,
  ...layoutData,
  ...inputData,
  ...tabsData,
  ...dropdownData,
  ...collapseData,
  ...iconData,
  ...editorData,
];

export default uiData;
