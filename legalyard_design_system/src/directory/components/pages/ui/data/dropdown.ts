import { createElement } from "react";
import { dataType } from ".";
import DropdownRender from "../renders/dropdown";

export const dropdownData: dataType[] = [
  {
    id: "Dropdown",
    title: "Dropdown",
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { Dropdown } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<Dropdown content={<MoreContent />}>
  <button className="flex items-center justify-center p-2 rounded-base">
    <div className="group flex flex-col items-center justify-center gap-2 ">
      <span
        className="border border-slate-300 group-hover:border-main text-slate-500 group-hover:text-main rounded-full flex items-center justify-center p-2 transition-all"
      >
        <MoreHorizontal />
      </span>
      <span
        className="text-slate-600 group-hover:text-main transition-all text-sm "
      >
        More
      </span>
    </div>
  </button>
</Dropdown>

const MoreContent = () => {
  const { handleClose } = useDropdown();

  return (
    <DropdownMenu
      search
      collapsible
      data={moreMenuData()}
      className={"bg-white !h-full !w-72 font-[400] z-50 overflow-auto"}
      fixContent={<div className="p-2 bg-amber-700 text-white sticky bottom-0 mt-2">Fixed Content</div>}
    />
  );
};

export const moreMenuData = () => {
  const ids: number[] = [10, 20, 50, 100];

  return [
    {
      title: "Public views",
      menu: ids?.map((item: number) => {
        return {
          title: "menu 1",
          event: () => {},
        };
      }),
    },
    {
      title: "Created by me",
      menu: ids?.map((item: number) => {
        return {
          title: "menu 2",
          event: () => {},
        };
      }),
    },
  ];
};

`,
        preview: createElement(
          "div",
          { className: "flex flex-col flex-wrap gap-4" },
          createElement(DropdownRender)
        ),
      },
      {
        title: "Available Props",
        language: "javascript",
        code: `children?: ReactNode;
content?: ReactNode;
dropdownStyle?: any;
inlineAlign?: "left";
topAlign?: boolean;
hover?: boolean;
className?: string;
contentClassName?: string;
actionClassName?: string;
onClose?: (data?: any) => void;
onToggle?: (data?: any) => void;
    
interface DropdownReferenceType {
  toggle: (v: boolean) => void;
}

export interface MenuDataItemType {
    id?: string | number;
    title: string;
    text?: string;
    rightText?: string;
    icon?: string | ReactNode;
    active?: boolean;
    event?: () => void;
    child?: MenuDataType[];
}
export interface MenuDataType {
    id?: string;
    title?: string;
    menu: MenuDataItemType[];
}
export interface DropdownMenuType {
    className?: string;
    search?: boolean;
    collapsible?: boolean;
    data: MenuDataType[];
    onClose?: (data?: any) => void;
}
export interface MenuSectionType {
    collapsible?: boolean;
    section: MenuDataType;
    index: number;
}`,
      },
    ],
  },
];
