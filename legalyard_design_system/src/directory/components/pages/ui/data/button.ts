import { Button } from "@lds/toolkit";
import { buttonVariants } from "@/toolkit/ui/button";
import { createElement } from "react";
import { dataType } from ".";

export const buttonData: dataType[] = [
  {
    id: "button",
    title: "Button",
    description: [
      "The Button component is a highly customizable, reusable button with various styles, sizes, and additional effects. It accepts several props that allow you to define the appearance and behavior of the button, making it suitable for a wide range of use cases in your application.",
    ],
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { Button } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<Button>Text</Button>
<Button variant="action">Contained</Button>
<Button variant="success">Outlined</Button>
<Button variant="error">Outlined</Button>
<Button variant="warning">Outlined</Button>
<Button variant="ghost">Outlined</Button>
<Button variant="outline">Outlined</Button>
<Button variant="dark">Outlined</Button>`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(Button, {}, "Button"),
          createElement(
            Button,
            {
              variant: "action",
            },
            "action"
          ),
          createElement(
            Button,
            {
              variant: "success",
            },
            "success"
          ),
          createElement(
            Button,
            {
              variant: "error",
            },
            "error"
          ),
          createElement(
            Button,
            {
              variant: "warning",
            },
            "warning"
          ),
          createElement(
            Button,
            {
              variant: "ghost",
            },
            "ghost"
          ),
          createElement(
            Button,
            {
              variant: "outline",
            },
            "outline"
          ),
          createElement(
            Button,
            {
              variant: "dark",
            },
            "dark"
          )
        ),
      },
      {
        title: "Props: size",
        language: "javascript",
        code: `<Button size="full">Text</Button>
<Button size="half">Contained</Button>`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(
            Button,
            {
              size: "full",
            },
            "full size"
          ),
          createElement(
            Button,
            {
              size: "half",
            },
            "half size"
          )
        ),
      },
      {
        title: "className: fold",
        language: "javascript",
        code: `<Button className="fold fold-bg-current fold-size-xs hover:fold-none !px-7">Fold (extra small)</Button>
<Button className="fold fold-bg-current fold-size-sm hover:fold-none !px-7">Fold (small)</Button>
<Button className="fold fold-bg-current fold-size-lg hover:fold-none !px-7">Fold (large)</Button>`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(
            Button,
            {
              className:
                "fold fold-bg-current fold-size-xs hover:fold-none !px-7",
            },
            "Fold (extra small)"
          ),
          createElement(
            Button,
            {
              className:
                "fold fold-bg-current fold-size-sm hover:fold-none !py-5 !px-10",
            },
            "Fold (small)"
          ),
          createElement(
            Button,
            {
              className:
                "fold fold-bg-current fold-size-lg hover:fold-none !py-12 !px-24",
            },
            "Fold (large)"
          )
        ),
      },
      {
        title: "props: loading",
        language: "javascript",
        code: `<Button loading={true}>Loading</Button>`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(
            Button,
            {
              loading: true,
            },
            "Loading..."
          )
        ),
      },
      {
        title: "props: effect",
        description: [
          "With effect props you can apply basic animation on button, mostly in action variant",
        ],
        language: "javascript",
        code: `<Button effect={true} variant="action">effect</Button>`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(
            Button,
            {
              effect: true,
              variant: "action",
            },
            "Action button with effect"
          )
        ),
      },
      {
        title: "props: rightArrow",
        description: [
          "With rightArrow props you can add right arrow icon after button content",
        ],
        language: "javascript",
        code: `<Button rightArrow={true}>Right Arrow</Button>
<Button rightArrow={true} effect={true} variant="action">Right Arrow</Button>`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(
            Button,
            {
              rightArrow: true,
            },
            "Right Arrow"
          ),
          createElement(
            Button,
            {
              effect: true,
              rightArrow: true,
              variant: "action",
            },
            "Right Arrow"
          )
        ),
      },
      {
        title: "function: buttonVariants",
        description: [
          "With buttonVariants function you can add styles to your custom button tag",
        ],
        language: "javascript",
        code: `<button className={buttonVariants({ variant: "dark", size: "half" })}>buttonVariants</button>`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(
            "button",
            {
              className: buttonVariants({ variant: "dark", size: "half" }),
            },
            "buttonVariants"
          )
        ),
      },
    ],
  },
];
