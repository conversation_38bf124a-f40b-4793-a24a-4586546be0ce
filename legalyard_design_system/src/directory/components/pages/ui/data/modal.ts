import { dataType } from ".";

export const modalData: dataType[] = [
  {
    id: "modalFormLayout",
    title: "Modal Form Layout",
    description: ["Works Only For React as of now"],
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { ModalFormLayoutRender } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<ModalFormLayout
ref={ModalRef}
onSubmit={(e) => console.log(e)}
title="New Contact"
button={{
  content: (
    <div className="flex items-center justify-center gap-x-2">
      Continue
    </div>
    ),
  }}
  className="overflow-auto"
>
  <input />
</ModalFormLayout>`,
      },
    ],
  },
];
