import { createElement } from "react";
import { dataType } from ".";
import MiniCollapseRender from "../renders/collapse/mini";

export const collapseData: dataType[] = [
  {
    id: "MiniCollapse",
    title: "MiniCollapse",
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { MiniCollapse } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `import { MiniCollapse } from "@/toolkit";
import { useState } from "react";

const MiniCollapseRender = () => {
  const [open, setOpen] = useState<boolean>(false);
  return (
    <div>
      <button onClick={() => setOpen((prev) => !prev)}>Toggle collapse</button>
      <MiniCollapse open={open}>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Omnis harum
        doloribus magni modi dolor alias ratione optio fuga quae, facere eveniet
        ad eligendi odio? Aliquam quae recusandae cumque sint molestias. Lorem
        ipsum dolor sit amet consectetur adipisicing elit. Omnis harum doloribus
        magni modi dolor alias ratione optio fuga quae, facere eveniet ad
        eligendi odio? Aliquam quae recusandae cumque sint molestias. Lorem
        ipsum dolor sit amet consectetur adipisicing elit. Omnis harum doloribus
        magni modi dolor alias ratione optio fuga quae, facere eveniet ad
        eligendi odio? Aliquam quae recusandae cumque sint molestias. Lorem
        ipsum dolor sit amet consectetur adipisicing elit. Omnis harum doloribus
        magni modi dolor alias ratione optio fuga quae, facere eveniet ad
        eligendi odio? Aliquam quae recusandae cumque sint molestias.
      </MiniCollapse>
    </div>
  );
};

export default MiniCollapseRender;`,
        preview: createElement(
          "div",
          { className: "flex flex-col flex-wrap gap-4" },
          createElement(MiniCollapseRender)
        ),
      },
    ],
  },
];
