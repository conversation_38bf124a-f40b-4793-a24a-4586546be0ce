import { createElement } from "react";
import { dataType } from ".";
import RemirrorEditorRender from "../renders/editor/remirror";

export const editorData: dataType[] = [
  {
    id: "RemirrorEditor",
    title: "RemirrorEditor",
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { RemirrorEditor } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<RemirrorEditor />`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(RemirrorEditorRender)
        ),
      },
    ],
  },
];
