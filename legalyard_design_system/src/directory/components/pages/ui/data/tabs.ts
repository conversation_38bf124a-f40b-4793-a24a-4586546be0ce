import { Tabs } from "@/toolkit";
import { createElement } from "react";
import { dataType } from ".";

export const tabsData: dataType[] = [
  {
    id: "Tabs",
    title: "Tabs",
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { Tabs } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<Tabs
  data={[
    { name: "1st Tab", content: "tab content 1" },
    { name: "2nd Tab", content: "tab content 2" },
  ]}
  ghostTheme={true}
  variant="underline"
  defaultActive={1}
/>;`,
        preview: createElement(
          "div",
          { className: "flex flex-col flex-wrap gap-4" },
          createElement(Tabs, {
            data: [
              { name: "1st Tab", content: "tab content 1" },
              { name: "2nd Tab", content: "tab content 2" },
            ],
            ghostTheme: false,
            variant: "underline",
            defaultActive: 1,
          }),
          createElement(Tabs, {
            data: [
              { name: "1st Tab", content: "tab content 1" },
              { name: "2nd Tab", content: "tab content 2" },
            ],
            ghostTheme: true,
            variant: "underline",
            defaultActive: 1,
          }),
          createElement(Tabs, {
            data: [
              { name: "1st Tab", content: "tab content 1" },
              { name: "2nd Tab", content: "tab content 2" },
              { name: "3rd Tab", content: "tab content 3" },
              { name: "4rth Tab", content: "tab content 4" },
            ],
            ghostTheme: true,
            defaultActive: 1,
          })
        ),
      },
      {
        title: "Available Props",
        language: "javascript",
        code: `defaultActive?: number;
data: {
  name: string;
  content?:
    | string
    | ReactElement<any, string | JSXElementConstructor<any>>
    | LazyExoticComponent<ComponentType | FC>;
  props?: any;
}[];
justify?: boolean;
noContent?: boolean;
tabClassName?: string | ClassAttributes<string>;
variant?: "normal" | "underline";
ghostTheme?: boolean;
className?: string;
contentClassName?: string;
onChange?: (active: number) => void;`,
      },
    ],
  },
];
