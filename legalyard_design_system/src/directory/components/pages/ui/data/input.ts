import { DateSelectorInput, InputField, ProfileUploadInput } from "@/toolkit";
import { createElement } from "react";
import { dataType } from ".";

const fileInput: dataType[] = [
  {
    id: "InputField",
    title: "Input Field",
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { InputField } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<InputField
 name="date"
 label={{ name: "date" }}
 onChange={(e) => handleChange(e)}
 value={value || ""}
/>`,
        preview: createElement(
          "div",
          {
            className: "flex flex-wrap gap-4 max-h-40",
          },
          createElement(InputField, {
            type: "password",
            name: "password",
            label: { name: "password" },
            passwordViewToggle: true,
            // onChange: (e) => console.log(e),
            // value: "",
          })
        ),
      },
    ],
  },
  {
    id: "ProfileUpload",
    title: "Profile Upload Input",
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { ProfileUpload } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<ProfileUploadInput
  name="avatar"
  onChange={(e) => handleChange(e)}
  initials={"LY"}
  value={"https://i.pravatar.cc/1000"}
  preview
  className="hidden"
  hideAction
  imageHight={150}
  imageWidth={150}
/>;`,
        preview: createElement(
          "div",
          {
            className: "flex flex-wrap gap-4 max-h-40",
          },
          createElement(ProfileUploadInput, {
            name: "avatar",
            onChange: (e) => console.log(e),
            initials: "LY",
            value: "https://i.pravatar.cc/1000",
            preview: true,
            initialsClassName: "font-bold",
            // hideAction: true,
            // hideEdit: true,
            // previewOnly: true,
            imageHight: 75,
            imageWidth: 75,
          })
        ),
      },
      {
        title: "Available Props",
        language: "javascript",
        code: `initials?: string;
actionTitle?: string;
initialsClassName?: string;
preview?: boolean;
value?: string | File;
hideEdit?: boolean;
previewOnly?: boolean;
lightBox?: boolean;
imageHight?: number;
id?: string;
action?: string | ReactNode;
hideAction?: boolean;
multiple?: boolean;
attributes?: InputAttributesTypes;
accept?: string;
label?: InputLabelType;
actionTitle?: string;
heading?: string;
className?: string;
onChange?: (data: any) => void;
getEvent?: (
  e: ChangeEvent<HTMLInputElement> | DragEvent<HTMLInputElement>
  ) => void;`,
      },
    ],
  },
  {
    id: "DateSelectorInput",
    title: "Date Selector Input",
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { DateSelectorInput } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<DateSelectorInput
 name="date"
 label={{ name: "date" }}
 onChange={(e) => handleChange(e)}
 value={date?.date || ""}
/>`,
        preview: createElement(
          "div",
          {
            className: "flex flex-wrap gap-4 max-h-40",
          },
          createElement(DateSelectorInput, {
            name: "date",
            label: { name: "date" },
            onChange: (e) => console.log(e),
            value: "",
          })
        ),
      },
    ],
  },
];

export const inputData = [...fileInput];
