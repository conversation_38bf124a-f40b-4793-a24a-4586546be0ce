import { createElement } from "react";
import { dataType } from ".";
import { Link } from "react-router-dom";
import { LegalyardLogo } from "@lds/toolkit";

export const logoData: dataType[] = [
  {
    id: "logo",
    title: "Logo",
    description: ["Works Only For React as of now"],
    section: [
      {
        title: "Import",
        language: "javascript",
        code: `import { Logo } from "@lds/toolkit`,
      },
      {
        title: "Props: variant",
        language: "javascript",
        code: `<Logo />`,
        preview: createElement(
          "div",
          { className: "flex flex-wrap gap-4" },
          createElement(LegalyardLogo, {
            LinkComponent: Link,
            href: "/logo",
          })
        ),
      },
    ],
  },
];
