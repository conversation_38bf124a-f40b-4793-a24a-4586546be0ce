import { createElement, ReactNode } from "react";
import UseTyping from "./renders/use-typing";

export interface dataType {
  id: string;
  title: string;
  types: {
    id: string;
    title: string;
    groups: {
      id: string;
      title: string;
      codes: {
        id: string;
        title: string;
        sections: {
          title: string;
          code: string;
          preview?: ReactNode;
          description?: string[];
          language?: string;
        }[];
      }[];
    }[];
  }[];
}

export const utilsData: dataType[] = [
  {
    id: "helper",
    title: "Helpers",
    types: [
      {
        id: "api",
        title: "API",
        groups: [
          {
            id: "payload",
            title: "Payload",
            codes: [
              {
                id: "dataToSend",
                title: "dataToSend",
                sections: [
                  {
                    title: "Import",
                    code: `import { dataToSend } from "@lds/toolkit"`,
                  },
                  {
                    title: "Use Case",
                    description: [
                      "The dataToSend function processes a set of parameters and corresponding values to create a structured object for data transmission. This function efficiently handles both flat and deeply nested properties in the values object.",
                      "This function is ideal when you need to send filtered and structured data, especially when the structure may include both flat and nested keys, making it useful for API requests, form submissions, or configuration updates where nested data is prevalent.",
                    ],
                    language: "javascript",
                    code: `const parameters = ["user.name", "address.city", "country"];
    const values = {
      user: {
        name: "John Doe",
        age: 30,
        address: {
          city: "New York",
          street: "123 Main St",
        },
      },
      country: "USA",
      address: {
        city: "New York",
      },
    };
    
    const result = dataToSend(parameters, values);
    
    // output: {
    //   user: "John Doe",
    //   address: "New York",
    //   country: "USA",
    // }
    `,
                  },
                  {
                    title: "Function Code",
                    language: "javascript",
                    code: `export const dataToSend = (
      parameters: string[],
      values: { [key: string]: any }
    ) => {
      try {
        const data: any = {};
    
        const hasNested = (propertyString: any) => {
          return propertyString.includes(".");
        };
    
        parameters?.forEach((prop: any) => {
          if (!hasNested(prop)) {
            if (Boolean(values?.[prop])) {
              data[prop] = values?.[prop];
            }
          } else {
            const properties = prop.split(".");
            if (Boolean(values?.[properties[0]])) {
              let currentData = values;
    
              for (const property of properties) {
                if (property in currentData) {
                  currentData = currentData[property];
                } else {
                  currentData[property] = {};
                  currentData = currentData[property];
                }
              }
    
              currentData = values;
              for (const property of properties) {
                currentData = currentData[property];
              }
    
              data[properties[0]] = currentData;
            }
          }
        });
    
        return data;
      } catch (err) {
        console.error(err);
      }
    };`,
                  },
                ],
              },
              {
                id: "getItemIds",
                title: "getItemIds",
                sections: [
                  {
                    title: "Import",
                    code: `import { getItemIds } from "@lds/toolkit"`,
                  },
                  {
                    title: "Use Case",
                    description: [
                      "The getItemIds function extracts and returns an array of _id properties from the provided data array. Each item in the array is expected to contain an _id field, and the function safely maps over the array to gather these values. If an error occurs during processing, it is caught and logged.",
                      "This function is useful when you need to collect all unique identifiers (e.g., MongoDB ObjectIds) from a dataset, such as preparing a list of item IDs for further processing or API requests.",
                    ],
                    language: "javascript",
                    code: `const data = [
      { _id: '123', name: 'Item 1' },
      { _id: '456', name: 'Item 2' },
      { _id: '789', name: 'Item 3' }
    ];
    
    const ids = getItemIds(data);
    // Output: ['123', '456', '789']`,
                  },
                  {
                    title: "Function Code",
                    code: `export const getItemIds = (data: any[]) => {
      try {
        const idData = data?.map((item: any) => item?._id);
    
        return idData;
      } catch (err) {
        console.error(err);
      }
    };`,
                  },
                ],
              },
              {
                id: "toUseId",
                title: "toUseId",
                sections: [
                  {
                    title: "Import",
                    code: `import { toUseId } from "@lds/toolkit"`,
                  },
                  {
                    title: "Use Case",
                    description: [
                      "toUseId extracts and returns localId if available, otherwise it returns _id. If neither exists, it returns null.",
                      "Retrieve the most relevant ID from an object that may have a local or database identifier.",
                    ],
                    language: "javascript",
                    code: `const userOffline = { localId: 'temp-12345', _id: 'db-67890' };
    const userSynced = { _id: 'db-67890' };
    const userMissingId = {};
    
    console.log(toUseId(userOffline));  // Output: 'temp-12345'
    console.log(toUseId(userSynced));   // Output: 'db-67890'
    console.log(toUseId(userMissingId)); // Output: null`,
                  },
                  {
                    title: "Function Code",
                    code: `type outType = string | number | null | undefined;
    
    export const toUseId = (obj: objType): outType => {
      try {
        return obj?.localId ?? obj?._id;
      } catch (err) {
        console.error(err);
        return null;
      }
    };`,
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: "data",
        title: "data",
        groups: [
          {
            id: "array",
            title: "Array",
            codes: [
              {
                id: "isArraysEqual",
                title: "isArraysEqual",
                sections: [
                  {
                    title: "Import",
                    language: "javascript",
                    code: `import { isArraysEqual } from "@lds/toolkit`,
                  },
                  {
                    title: "Use Case",
                    description: [
                      "isArraysEqual compares two arrays to check if they are equal by sorting and converting them to strings. It returns true if they are identical, and false otherwise.",
                      "Compare two arrays regardless of their element order to check for equality.",
                    ],
                    language: "javascript",
                    code: `const array1 = [3, 2, 1];
const array2 = [1, 2, 3];
const array3 = [1, 2, 4];

console.log(isArraysEqual(array1, array2)); // Output: true
console.log(isArraysEqual(array1, array3)); // Output: false`,
                  },
                  {
                    title: "Function Code",
                    language: "javascript",
                    code: `export const isArraysEqual = (arr1: any, arr2: any) => {
  try {
    if (
      !Array.isArray(arr1) ||
      !Array.isArray(arr2) ||
      arr1?.length !== arr2?.length
    ) {
      return false;
    }

    const sortedArr1 = arr1?.slice()?.sort();
    const sortedArr2 = arr2?.slice()?.sort();

    const strArr1 = JSON.stringify(sortedArr1);
    const strArr2 = JSON.stringify(sortedArr2);

    return strArr1 === strArr2;
  } catch (err) {
    console.error(err);
  }
};`,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "hooks",
    title: "Hooks",
    types: [
      {
        id: "data",
        title: "Data",
        groups: [
          {
            id: "Text",
            title: "Text",
            codes: [
              {
                id: "useTyping",
                title: "useTyping",
                sections: [
                  {
                    title: "Import",
                    code: `import { useTyping } from "@lds/toolkit"`,
                  },
                  {
                    title: "Use Case",
                    description: [
                      "Get typed text animation for provided array of strings",
                    ],
                    language: "javascript",
                    code: ` const { text } = useTyping({
    texts: ["activity", "notes", "email"],
    prefix: "Search",
    suffix: "more",
    interval: 150,
    timeout: 1000,
  });`,
                    preview: createElement(
                      "div",
                      { className: "flex flex-col flex-wrap gap-4" },
                      createElement(UseTyping)
                    ),
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
];
