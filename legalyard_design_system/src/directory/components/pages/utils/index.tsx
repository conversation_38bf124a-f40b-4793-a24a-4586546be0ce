import { validArray } from "@/toolkit/utils";
import { useParams } from "react-router-dom";
import CodeBlock from "../../elements/code";
import Header from "../../elements/header";
import SubSection from "../../elements/section";
import Section from "../../elements/section/sub";
import CollapseHOC from "../../elements/section/sub/collapse";
import { utilsData } from "./data";

const Utils = () => {
  const { type } = useParams();

  const filterData = () => {
    try {
      if (type) {
        return utilsData?.filter((item) => item?.id === type);
      } else {
        return utilsData;
      }
    } catch (error) {
      return [];
    }
  };

  const finalData = filterData();

  return (
    <div className="container mx-auto p-page">
      <Header
        title="Utils"
        description="Learn how to use the helper functions, hooks & validators"
      />

      {validArray(finalData) ? (
        finalData?.map((section, index) => {
          return (
            <Section key={index} title={section?.title} id={section?.id}>
              {section?.types?.map((type, typeIndex) => {
                return (
                  <CollapseHOC
                    key={typeIndex}
                    title={<>{type?.title}</>}
                    id={type?.id}
                    className="border-b border-b-dark border-dashed "
                  >
                    {type?.groups?.map((group, typeIndex) => {
                      return (
                        <CollapseHOC
                          key={typeIndex}
                          title={<>{group?.title}</>}
                          id={group?.id}
                          className="border-l border-l-font ps-6 text-blue"
                        >
                          {group?.codes?.map((code, codeIndex) => {
                            return (
                              <CollapseHOC
                                key={codeIndex}
                                title={<>{code?.title}</>}
                                id={code?.id}
                                className="border-l border-l-blue ps-6 text-main"
                              >
                                {code?.sections?.map((sec, secIndex) => {
                                  return (
                                    <SubSection
                                      key={secIndex}
                                      title={sec?.title}
                                      className="border-l border-l-main ps-6 text-font"
                                    >
                                      {validArray(sec?.description) &&
                                        sec?.description?.map((dsc, index) => (
                                          <p
                                            key={index}
                                            className="text-zinc-600 text-sb leading-tight mb-4"
                                          >
                                            {dsc}
                                          </p>
                                        ))}
                                      {sec?.code && (
                                        <CodeBlock
                                          code={sec?.code}
                                          language={
                                            sec?.language ?? "javascript"
                                          }
                                          preview={sec?.preview}
                                        />
                                      )}
                                    </SubSection>
                                  );
                                })}
                              </CollapseHOC>
                            );
                          })}
                        </CollapseHOC>
                      );
                    })}
                  </CollapseHOC>
                );
              })}
            </Section>
          );
        })
      ) : (
        <span className="text-red">Data not found</span>
      )}
    </div>
  );
};

export default Utils;
