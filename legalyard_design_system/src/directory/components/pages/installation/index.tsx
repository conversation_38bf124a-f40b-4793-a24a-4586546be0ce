import { FC } from "react";
import CodeBlock from "../../elements/code";
import Header from "../../elements/header";
import Navigation from "../../elements/navigation";
import SubSection from "../../elements/section";
import Section from "../../elements/section/sub";

const DocumentationPage: FC = () => {
  return (
    <div className="container mx-auto px-6 py-10">
      <Header
        title="Toolkit Library Documentation"
        description="Learn how to use the toolkit library with detailed instructions and examples."
      />

      <Navigation
        list={[
          {
            title: "Installation",
            path: "#installation",
          },
        ]}
      />

      <Section title="Installation" id="installation">
        <SubSection title="Install via npm">
          <p className="text-gray-600 mb-4">
            You can install the toolkit library using npm by running the
            following command:
          </p>
          <CodeBlock code={`npm install @lds/toolkit`} language="bash" />
        </SubSection>

        <SubSection title="Installation requirements">
          <p className="text-gray-600 mb-4">
            Parent project should have <code>.npmrc</code> file with following
            code + auth token
          </p>
          <CodeBlock language="bash">
            {`@lds:registry=https://gitlab.com/api/v4/projects/61658942/packages/npm/
//gitlab.com/api/v4/projects/61658942/packages/npm/:_authToken=<AUTH_TOKEN>`}
          </CodeBlock>
        </SubSection>

        <SubSection title="Installation requirements">
          <p className="text-gray-600 mb-4">
            Parent project should have <code>.env</code> file with following
            variable
          </p>
          <CodeBlock language="bash">
            {`// For React App
REACT_APP_PUBLIC=true

// For NextJS App
NEXT_PUBLIC_APP=true`}
          </CodeBlock>
        </SubSection>
      </Section>
    </div>
  );
};

export default DocumentationPage;
