import { FC, ReactNode } from "react";
import { Clipboard } from "react-feather";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vs as codeStyle } from "react-syntax-highlighter/dist/esm/styles/prism";
import Preview from "./preview";

interface CodeBlockProps {
  code?: string;
  language?: string;
  children?: string;
  preview?: ReactNode;
}

const CodeBlock: FC<CodeBlockProps> = ({
  children,
  code,
  language = "javascript",
  preview,
}) => {
  const copyToClipboard = (str: string) => {
    navigator.clipboard.writeText(str ?? "").then(() => {
      alert("Code copied to clipboard!");
    });
  };

  const finalText = children ?? code ?? "";
  const codeSTyles = { ...codeStyle };

  return (
    <div className="relative bg-gray-900 text-font px-4 py-0.5 rounded-base my-2 shadow-md text-xl font-bold">
      <div className="flex justify-between items-center px-2 py-0.5 bg-gray-800 rounded-t-lg">
        <span className="text-xs text-gray">{language?.toUpperCase()}</span>
        <button
          onClick={() => copyToClipboard(finalText)}
          className="text-xs text-blue-400 hover:text-blue-300 font-medium px-2 py-1 border border-blue-400 rounded focus:outline-none flex items-center gap-1 hover:bg-blue hover:text-white transition-all"
        >
          <Clipboard />
          Copy
        </button>
      </div>
      <SyntaxHighlighter
        language={language}
        style={{ ...codeSTyles }}
        showLineNumbers={true}
      >
        {finalText}
      </SyntaxHighlighter>

      {}

      {preview && <Preview preview={preview} />}
    </div>
  );
};

export default CodeBlock;
