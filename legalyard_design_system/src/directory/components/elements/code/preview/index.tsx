import { FC, ReactNode } from "react";

interface PropsType {
  preview?: ReactNode;
}

const ComponentRenderer: FC<PropsType> = ({ preview }) => {
  try {
    return <>{preview}</>;
  } catch (error) {
    console.error("Error rendering component:", error);
    return <div>Error rendering component.</div>;
  }
};

const Preview: FC<PropsType> = ({ preview }) => {
  return (
    <div className="border border-zinc-200 p-4 mb-2">
      <ComponentRenderer preview={preview} />
    </div>
  );
};

export default Preview;
