import classNames from "classnames";
import React, { FC } from "react";

interface HeaderProps {
  title?: string;
  description?: string;
  children?: React.ReactNode;
  className?: string;
}

const Header: FC<HeaderProps> = ({
  title,
  description,
  children,
  className,
}) => {
  return (
    <header
      className={classNames(
        "py-4 pb-2 border-b border-blue border-opacity-95 mb-6",
        className
      )}
    >
      {children ?? (
        <>
          {title && (
            <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
          )}
          {description && (
            <p className="text-lg text-gray-600 mt-2">{description}</p>
          )}
        </>
      )}
    </header>
  );
};

export default Header;
