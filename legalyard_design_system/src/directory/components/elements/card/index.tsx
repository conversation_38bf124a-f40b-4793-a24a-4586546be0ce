import { FC } from "react";
import { ArrowRight } from "react-feather";
import { Link } from "react-router-dom";

interface CardProps {
  title?: string;
  description?: string;
  path?: string;
}

const Card: FC<CardProps> = ({ title, description, path }) => {
  return (
    <div className="max-w-sm p-6 bg-slate-50 border border-gray rounded-lg shadow">
      {title && (
        <h5 className="mb-2 text-2xl font-bold tracking-tight text-font">
          {title}
        </h5>
      )}

      {description && (
        <p className="mb-3 font-normal text-gray-700 dark:text-gray-400">
          {description}
        </p>
      )}

      {path && (
        <Link
          to={path}
          className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-base hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue bg-blue hover:bg-main hover:bg-opacity-40 transition-all"
        >
          Read more
          <ArrowRight />
        </Link>
      )}
    </div>
  );
};

export default Card;
