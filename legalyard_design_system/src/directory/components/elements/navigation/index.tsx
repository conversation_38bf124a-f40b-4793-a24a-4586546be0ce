import React, { <PERSON> } from "react";
import { ArrowRight } from "react-feather";

interface NavigationProps {
  list: {
    title: string;
    path: string;
  }[];
}

const Navigation: FC<NavigationProps> = ({ list }) => {
  return (
    <nav className="bg-blue-100 py-6 my-8 rounded-lg shadow-sm">
      <ul className="space-y-3">
        {Array.isArray(list) &&
          list?.length > 0 &&
          list?.map((menu, index) => {
            return (
              <li key={index}>
                <a
                  href={menu?.path}
                  className="text-blue-700 hover:text-blue-500 font-medium hover:text-blue transition-all flex items-center gap-3"
                >
                  <ArrowRight />
                  {menu?.title}
                </a>
              </li>
            );
          })}
      </ul>
    </nav>
  );
};

export default Navigation;
