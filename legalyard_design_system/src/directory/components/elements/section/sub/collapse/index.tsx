import { ReactNode, useState } from "react";
import SubSection from "../..";

const CollapseHOC = ({
  children,
  ...rest
}: {
  children?: ReactNode;
  id?: string;
  title?: string | ReactNode;
  className?: string;
}) => {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <SubSection open={open} setOpen={setOpen} collapse {...rest}>
      {open ? children : null}
    </SubSection>
  );
};

export default CollapseHOC;
