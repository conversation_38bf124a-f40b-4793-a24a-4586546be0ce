import classNames from "classnames";
import { FC } from "react";

interface SectionProps {
  title?: string;
  id?: string;
  className?: string;
  children?: React.ReactNode;
}

const Section: FC<SectionProps> = ({ title, id, children, className }) => {
  return (
    <section className={classNames("mb-10", className)} id={id}>
      {title && (
        <h2 className="text-xl font-extrabold text-gray-800 mb-6 border-b pb-2 border-blue border-opacity-50">
          {title}
        </h2>
      )}

      {children}
    </section>
  );
};

export default Section;
