import classNames from "classnames";
import { Dispatch, FC, ReactNode, SetStateAction } from "react";
import { ChevronDown, ChevronUp } from "react-feather";

interface SubSectionProps {
  id?: string;
  title?: string | ReactNode;
  className?: string;
  children?: React.ReactNode;
  collapse?: boolean;
  setOpen?: Dispatch<SetStateAction<boolean>>;
  open?: boolean;
}

const SubSection: FC<SubSectionProps> = ({
  title,
  children,
  id,
  className,
  collapse,
  setOpen,
  open,
}) => {
  return (
    <div className={classNames("mb-8", className)} id={id}>
      {title && (
        <div
          role="button"
          className={classNames(
            "relative text-base font-semibold mb-1.5 flex items-center gap-2",
            collapse && "px-0.5",
            collapse &&
              (open
                ? "hover:bg-red hover:bg-opacity-10"
                : "hover:bg-green hover:bg-opacity-10"),
            {
              "cursor-pointer": collapse,
            }
          )}
          {...(collapse
            ? {
                onClick: () => {
                  if (setOpen) {
                    setOpen((prev) => !prev);
                  }
                },
              }
            : {})}
        >
          {collapse && (
            <>
              {" "}
              {open ? (
                <ChevronUp className="stroke-2" />
              ) : (
                <ChevronDown className="stroke-2" />
              )}
            </>
          )}
          {title}
        </div>
      )}
      {children}
    </div>
  );
};

export default SubSection;
