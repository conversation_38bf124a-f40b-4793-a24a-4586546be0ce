import { FC, Fragment, ReactNode, Suspense } from "react";
import { Route, Routes, Navigate } from "react-router-dom";
import Loader from "@/toolkit/ui/loader";

interface propsType {
  routeData?: any[];
  addRoutes?: any[];
  loader?: ReactNode;
}

const CreateRouter: FC<propsType> = ({ routeData, addRoutes, loader }) => {
  return (
    <Suspense
      fallback={
        loader ?? <Loader box center big wrapperStyle={{ height: "80vh" }} />
      }
    >
      <Routes>
        <Route path="*" element={<Navigate to="" />} />
        {Array.isArray(addRoutes) &&
          addRoutes?.length > 0 &&
          addRoutes?.map((rt, index) => {
            return (
              <Fragment key={index * index + index}>
                {<Route exact {...rt} />}
              </Fragment>
            );
          })}
        {Array.isArray(routeData) && routeData?.length > 0 && (
          <>
            {routeData?.map((route, index) => {
              return (
                <Route
                  key={route.path + index}
                  path={route.path}
                  element={<route.component {...route.props} />}
                />
              );
            })}
          </>
        )}
      </Routes>
    </Suspense>
  );
};

export default CreateRouter;
