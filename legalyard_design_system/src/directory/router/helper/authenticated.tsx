import { useLocation, Outlet, Navigate } from "react-router-dom";
// import useAuth from "@/pages/Auth/@context/useAuth";

const Authenticated = () => {
  // const { auth, loading } = useAuth();
  const location = useLocation();
  const from = location?.state?.from?.pathname || "/";
  const loading = false;
  const isAuthenticated = true;

  return (
    <>
      {isAuthenticated && !loading ? (
        <Navigate to={from} state={{ from: location }} replace />
      ) : (
        <Outlet />
      )}
    </>
  );
};

export default Authenticated;
