import { AxiosInstance } from "axios";
// import * as API from "@/pages/Auth/@api/auth";

export const interceptor = (apiClient: AxiosInstance) => {
  try {
    apiClient.interceptors.request.use(
      (config) => {
        const userToken = localStorage.getItem("user_access_token");
        if (!config.headers["Authorization"]) {
          config.headers["Authorization"] = `Bearer ${userToken}`;
        }

        return config;
      },
      (err) => {
        return Promise.reject(err);
      }
    );

    let isRefreshing = false;

    apiClient.interceptors.response.use(
      (response) => {
        return response;
      },
      async (error) => {
        const prevRequest = error?.config;
        if (
          error?.response?.status === 403 &&
          !prevRequest?.sent &&
          !isRefreshing
        ) {
          isRefreshing = true;
          prevRequest.sent = true;

          try {
            // const refreshResponse: any = await API.refreshToken();
            // const newAccessToken = refreshResponse?.accessToken;
            // if (newAccessToken) {
            //   localStorage.setItem("user_access_token", newAccessToken);
            // }
            // prevRequest.headers["Authorization"] = `Bearer ${newAccessToken}`;
            return apiClient(prevRequest);
          } catch (err) {
            console.error(err);
          } finally {
            isRefreshing = false;
          }
        }
        return Promise.reject(error);
      }
    );
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};
