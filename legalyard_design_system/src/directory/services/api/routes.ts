const prodRoutes = {
  auth: {
    mobileAuth: "/api/client/auth/mobile-auth",
    verifyOtp: "/api/client/auth/verify-otp",
    refresh: "/api/client/auth/refresh",
    googleAuth: "/api/client/auth/google-auth",
    getUserAuth: "/api/client/auth/get-user-auth",
    getPartnerAuth: "/api/client/auth/partner/get-partner-auth",
    partnerExist: "/api/client/auth/partner/partner-exist",
    partnerLogin: "/api/client/auth/partner/partner-login",
    partnerRegisterCheck: "/api/client/auth/partner/partner-register-check",
    partnerRegister: "/api/client/auth/partner/partner-register",
    checkEmailExist: "/api/client/auth/partner/check-email-exist",
    sendEmailOTP: "/api/client/auth/partner/send-email-otp",
    verifyEmailOTP: "/api/client/auth/partner/verify-email-otp",
    onboarding: "/api/client/auth/partner/onboarding",
    checkHasPassword: "/api/client/auth/check-has-password",
    setNewPassword: "/api/client/auth/set-new-password",
    changePassword: "/api/client/auth/change-password",
    forgetCurrentPassword: "/api/client/auth/forget-current-password",
    guestSendMobileOTP: "/api/client/auth/guest-send-mobile-otp",
    logout: "/api/client/auth/logout",
  },
  payment: {
    request: "/api/client/user/payment/request",
  },
};

export const apiRoutes = () => prodRoutes;
