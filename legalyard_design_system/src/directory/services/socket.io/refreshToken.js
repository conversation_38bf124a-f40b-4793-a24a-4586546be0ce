import { store } from "../../redux/store";
import { setAccessToken } from "../../pages/Auth/@redux/actions";
import * as API from "../../pages/Auth/@api";

let action = {};

export const lastAction = (socket) => {
  const originalEmit = socket?.emit;

  socket.emit = function (event, ...args) {
    action = { event, args };
    originalEmit.call(socket, event, ...args);
  };
};

export const emptyAction = (event) => {
  if (Boolean(event)) {
    action = {};
  }
};

export const handleRefresh = async (socket) => {
  try {
    const refreshResponse = await API.refreshToken();
    const newAccessToken = refreshResponse?.data?.accessToken;
    store.dispatch(setAccessToken(newAccessToken));
    socket.io.opts.auth.token = newAccessToken;
    socket.connect();

    if (Object.keys(action)?.length !== 0) {
      const data =
        Array.isArray(action?.args) && action?.args[0] !== undefined
          ? action?.args[0]
          : null;
      socket?.emit("add-message", data);
      action = {};
    }
  } catch (err) {
    console.error(err);
  }
};
