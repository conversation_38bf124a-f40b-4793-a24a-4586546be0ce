{"name": "legalyard_design_system", "homepage": "https://lds.admin.staging.legalyard.com/", "version": "0.1.0", "private": true, "dependencies": {"@apollo/client": "^3.11.8", "@craco/craco": "^7.1.0", "@heroicons/react": "^2.1.5", "@lds/toolkit": "^1.0.0", "@remirror/react": "^3.0.1", "@remirror/react-editors": "^2.0.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.108", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "postcss": "^8.4.47", "react": "^18.3.1", "react-color": "^2.19.3", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-feather": "^2.0.10", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "remirror": "^3.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"lint": "CI=true eslint \"src/**/*.ts\" \"src/**/*.tsx\"", "start": "craco start", "build": "craco build", "build:staging": "env-cmd -f .env.staging craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": []}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/uuid": "^10.0.0", "classnames": "^2.5.1", "env-cmd": "^10.1.0", "moment": "^2.30.1", "react-syntax-highlighter": "^15.5.0", "styled-components": "^6.1.13", "tailwindcss": "^3.4.11", "uuid": "^10.0.0"}}