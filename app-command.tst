<task_info>
1. On path "legalyard_design_system/src/toolkit" we have a reusable liabrary with Ui component, utils, helper, hooks, validators etc.
2. You needs to refer to them & out of it create the new application which will be a documentation of this toolkit.
3. the documentation app will be created in current - "/Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai" folder. "mad_with_ai" will be newly created folder.
4. The documentation app will be named as "legalyard_design_system".
5. The documentation app will have following pages:
   1. Home page
   2. Installation page
   3. UI components page
   4. Utils page
   5. Hooks page
   6. Validators page
   7. Helpers page
   8 & all other was per requirements
6. The document will be in storybook formate.
7. Use the best design Ui/Ux in the market. 
8. You can refer to Th Material UI website https://mui.com/material-ui/
9. The documentation app will be created in nextJS, with all the optimized way.
10. The documentation should be in details with the installation, code samples, props, function, use cases etc.
11. YOu can refer best documentation apps in markets e.g. material ui, tailwind, shadcn etc.
</task_info>

<instructions>
1. Only refer to legalyard_design_system folder, don't refer to registry/legalyard_toolkit_npm_package folder.
2. Don't make any code changes to legalyard_design_system & registry folder. 
3. You needs to create & work complexly in new application.
</instructions>

<app_requirements>
- Below are the app development requirements:
1. Create new latest version nextjs application with tailwind v4, typescript & other required dependencies.
2. Create a new folder "mad_with_ai" in "/Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT" path.
3. Create a new nextjs application in "mad_with_ai" folder.
4. Create a new page for each of the above mentioned pages.
5. Create a new component for each of the above mentioned components.
6. Create a new utility for each of the above mentioned utilities.
7. Create a new hook for each of the above mentioned hooks.
8. Create a new validator for each of the above mentioned validators.
9. Create a new helper for each of the above mentioned helpers.
10. Create a new function for each of the above mentioned functions.
11. Create a new variable for each of the above mentioned variables.
12. Create a new constant for each of the above mentioned constants.
13. Create a new interface for each of the above mentioned interfaces.
14. Create a new type for each of the above mentioned types.
15. Create a new enum for each of the above mentioned enums.
16. Create a new class for each of the above mentioned classes.
17. Create a new file for each of the above mentioned files.
18. Create a new folder for each of the above mentioned folders.
19. App should use dark light mode for theme.
20. App should be fully responsive.
21. App should have proper error handling.
22. App should have proper loading states.
23. App should have proper form validation.
24. App should have proper form submission.
25. App should have proper form reset.
26. App should have proper form dirty check.
27. App should have proper form disable/enable.
28. App should have proper form read only.
29. the theme should be fully customable. use all latest practice of tailwind for theming
30. you can keep copy of the toolkit folder in new app to import & use them.
31. keep toolkit folder structure as it is, but you can improvise the code of the components, utils, hooks, validators, helpers, functions, variables, constants, interfaces, types, enums, classes, files, folders etc.
32. you can use storybook to create the documentation.
33. you can use any other best libraries to make this application great.
</app_requirements>

<additional>
- You can refer to Th Material UI website https://mui.com/material-ui/ for best design Ui/Ux in the market.
- add all other required things to make this application great document for toolkit,
- application should be Future ready,
- in future there Will be more more new components utils, hooks will be added, so application should be scalable & easy to add new things.
- you can stop & ask, while completing task if you get confused for further operations. will be good to have human in loop.
</additional>