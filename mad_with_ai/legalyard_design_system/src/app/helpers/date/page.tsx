import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"

export default function DateHelpersPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Date Helpers</h1>
          <p className="text-xl text-muted-foreground">
            A comprehensive collection of date manipulation utilities for common date operations and formatting.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Date Arithmetic */}
            <div>
              <h3 className="text-lg font-medium mb-4">Date Arithmetic</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Adding/Subtracting Days</h4>
                      <div className="space-y-2 text-sm">
                        <p><code className="bg-muted px-2 py-1 rounded">addDays(today, 7)</code></p>
                        <p className="text-green-600">→ {new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}</p>
                        <p><code className="bg-muted px-2 py-1 rounded">subtractDays(today, 30)</code></p>
                        <p className="text-green-600">→ {new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Date Differences</h4>
                      <div className="space-y-2 text-sm">
                        <p><code className="bg-muted px-2 py-1 rounded">daysBetween(start, end)</code></p>
                        <p className="text-green-600">→ 15 days</p>
                        <p><code className="bg-muted px-2 py-1 rounded">hoursBetween(start, end)</code></p>
                        <p className="text-green-600">→ 360 hours</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { 
  addDays, 
  subtractDays, 
  daysBetween, 
  hoursBetween 
} from '@legalyard/design-system/helpers'

const today = new Date()
const nextWeek = addDays(today, 7)
const lastMonth = subtractDays(today, 30)

// Calculate differences
const projectStart = new Date('2024-01-01')
const projectEnd = new Date('2024-01-15')
const duration = daysBetween(projectStart, projectEnd) // 14 days

// Working with hours
const meetingStart = new Date('2024-01-01T09:00:00')
const meetingEnd = new Date('2024-01-01T17:00:00')
const meetingHours = hoursBetween(meetingStart, meetingEnd) // 8 hours`}
              />
            </div>

            {/* Date Formatting */}
            <div>
              <h3 className="text-lg font-medium mb-4">Date Formatting</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Standard Formats</h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="text-green-600">formatDate:</span> "Jan 15, 2024"</p>
                        <p><span className="text-green-600">formatDateTime:</span> "Jan 15, 2024 2:30 PM"</p>
                        <p><span className="text-green-600">formatTime:</span> "2:30 PM"</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Relative Formats</h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="text-green-600">timeAgo:</span> "2 hours ago"</p>
                        <p><span className="text-green-600">timeUntil:</span> "in 3 days"</p>
                        <p><span className="text-green-600">formatRelative:</span> "tomorrow at 2:30 PM"</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { 
  formatDate, 
  formatDateTime, 
  formatTime,
  timeAgo,
  timeUntil,
  formatRelative 
} from '@legalyard/design-system/helpers'

const date = new Date('2024-01-15T14:30:00')

// Standard formatting
formatDate(date) // "Jan 15, 2024"
formatDate(date, 'MM/dd/yyyy') // "01/15/2024"
formatDate(date, 'MMMM dd, yyyy') // "January 15, 2024"

formatDateTime(date) // "Jan 15, 2024 2:30 PM"
formatTime(date) // "2:30 PM"
formatTime(date, '24h') // "14:30"

// Relative formatting
const pastDate = new Date(Date.now() - 2 * 60 * 60 * 1000)
timeAgo(pastDate) // "2 hours ago"

const futureDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)
timeUntil(futureDate) // "in 3 days"

formatRelative(futureDate) // "tomorrow at 2:30 PM"`}
              />
            </div>

            {/* Date Validation */}
            <div>
              <h3 className="text-lg font-medium mb-4">Date Validation & Comparison</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Validation</h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="text-green-600">isValidDate:</span> true/false</p>
                        <p><span className="text-green-600">isWeekend:</span> true/false</p>
                        <p><span className="text-green-600">isLeapYear:</span> true/false</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Comparison</h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="text-green-600">isSameDay:</span> true/false</p>
                        <p><span className="text-green-600">isToday:</span> true/false</p>
                        <p><span className="text-green-600">isBefore:</span> true/false</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { 
  isValidDate,
  isWeekend,
  isLeapYear,
  isSameDay,
  isToday,
  isBefore,
  isAfter 
} from '@legalyard/design-system/helpers'

// Validation
isValidDate(new Date()) // true
isValidDate(new Date('invalid')) // false

const saturday = new Date('2024-01-13') // Saturday
isWeekend(saturday) // true

isLeapYear(2024) // true
isLeapYear(2023) // false

// Comparison
const date1 = new Date('2024-01-15')
const date2 = new Date('2024-01-15')
isSameDay(date1, date2) // true

isToday(new Date()) // true

const past = new Date('2023-01-01')
const future = new Date('2025-01-01')
isBefore(past, future) // true
isAfter(future, past) // true`}
              />
            </div>

            {/* Date Ranges & Periods */}
            <div>
              <h3 className="text-lg font-medium mb-4">Date Ranges & Periods</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Start/End of Periods</h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="text-green-600">startOfDay:</span> "2024-01-15 00:00:00"</p>
                        <p><span className="text-green-600">endOfDay:</span> "2024-01-15 23:59:59"</p>
                        <p><span className="text-green-600">startOfWeek:</span> "2024-01-14 00:00:00"</p>
                        <p><span className="text-green-600">startOfMonth:</span> "2024-01-01 00:00:00"</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Date Ranges</h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="text-green-600">getDateRange:</span> Array of dates</p>
                        <p><span className="text-green-600">getWeekDays:</span> Mon-Fri dates</p>
                        <p><span className="text-green-600">getMonthDays:</span> All days in month</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { 
  startOfDay,
  endOfDay,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  getDateRange,
  getWeekDays,
  getMonthDays 
} from '@legalyard/design-system/helpers'

const date = new Date('2024-01-15T14:30:00')

// Start/End of periods
startOfDay(date) // 2024-01-15 00:00:00
endOfDay(date) // 2024-01-15 23:59:59

startOfWeek(date) // 2024-01-14 00:00:00 (Sunday)
endOfWeek(date) // 2024-01-20 23:59:59 (Saturday)

startOfMonth(date) // 2024-01-01 00:00:00
endOfMonth(date) // 2024-01-31 23:59:59

// Date ranges
const start = new Date('2024-01-01')
const end = new Date('2024-01-07')
const range = getDateRange(start, end) // Array of 7 dates

// Get all weekdays in January 2024
const weekdays = getWeekDays(2024, 0) // Month is 0-indexed

// Get all days in January 2024
const allDays = getMonthDays(2024, 0) // Array of 31 dates`}
              />
            </div>

            {/* Timezone & Locale */}
            <div>
              <h3 className="text-lg font-medium mb-4">Timezone & Locale</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Timezone Conversion</h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="text-green-600">toTimezone:</span> Convert to specific timezone</p>
                        <p><span className="text-green-600">toUTC:</span> Convert to UTC</p>
                        <p><span className="text-green-600">getTimezone:</span> Get current timezone</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Locale Formatting</h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="text-green-600">formatLocale:</span> Format for locale</p>
                        <p><span className="text-green-600">getWeekStart:</span> Week start day by locale</p>
                        <p><span className="text-green-600">getDateSeparator:</span> Date separator by locale</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { 
  toTimezone,
  toUTC,
  getTimezone,
  formatLocale,
  getWeekStart 
} from '@legalyard/design-system/helpers'

const date = new Date('2024-01-15T14:30:00')

// Timezone conversion
const nyTime = toTimezone(date, 'America/New_York')
const utcTime = toUTC(date)
const currentTz = getTimezone() // "America/Los_Angeles"

// Locale formatting
formatLocale(date, 'en-US') // "1/15/2024, 2:30:00 PM"
formatLocale(date, 'de-DE') // "15.1.2024, 14:30:00"
formatLocale(date, 'ja-JP') // "2024/1/15 14:30:00"

// Locale-specific settings
getWeekStart('en-US') // 0 (Sunday)
getWeekStart('en-GB') // 1 (Monday)

// Business days calculation
const businessDays = getBusinessDays(
  new Date('2024-01-01'),
  new Date('2024-01-31'),
  ['2024-01-01', '2024-01-15'] // holidays
) // Excludes weekends and holidays`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="legalyard-card overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">Function</th>
                  <th className="text-left p-4 font-medium">Parameters</th>
                  <th className="text-left p-4 font-medium">Returns</th>
                  <th className="text-left p-4 font-medium">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">addDays</td>
                  <td className="p-4 text-sm">date: Date, days: number</td>
                  <td className="p-4 text-sm">Date</td>
                  <td className="p-4 text-sm">Adds specified days to date</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">subtractDays</td>
                  <td className="p-4 text-sm">date: Date, days: number</td>
                  <td className="p-4 text-sm">Date</td>
                  <td className="p-4 text-sm">Subtracts specified days from date</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">daysBetween</td>
                  <td className="p-4 text-sm">start: Date, end: Date</td>
                  <td className="p-4 text-sm">number</td>
                  <td className="p-4 text-sm">Calculates days between two dates</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">formatDate</td>
                  <td className="p-4 text-sm">date: Date, format?: string</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">Formats date according to pattern</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">timeAgo</td>
                  <td className="p-4 text-sm">date: Date</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">Returns relative time string</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">isValidDate</td>
                  <td className="p-4 text-sm">date: any</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">Checks if value is valid date</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">isSameDay</td>
                  <td className="p-4 text-sm">date1: Date, date2: Date</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">Checks if dates are same day</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">startOfDay</td>
                  <td className="p-4 text-sm">date: Date</td>
                  <td className="p-4 text-sm">Date</td>
                  <td className="p-4 text-sm">Returns start of day (00:00:00)</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">getDateRange</td>
                  <td className="p-4 text-sm">start: Date, end: Date</td>
                  <td className="p-4 text-sm">Date[]</td>
                  <td className="p-4 text-sm">Returns array of dates in range</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">toTimezone</td>
                  <td className="p-4 text-sm">date: Date, timezone: string</td>
                  <td className="p-4 text-sm">Date</td>
                  <td className="p-4 text-sm">Converts date to specified timezone</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Always validate dates before processing</li>
                <li>• Use appropriate timezone handling</li>
                <li>• Consider locale-specific formatting</li>
                <li>• Handle edge cases (leap years, DST)</li>
                <li>• Use relative formatting for recent dates</li>
                <li>• Cache expensive date calculations</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't assume all months have 30 days</li>
                <li>• Avoid hardcoded date formats</li>
                <li>• Don't ignore timezone differences</li>
                <li>• Avoid mutating original date objects</li>
                <li>• Don't forget about daylight saving time</li>
                <li>• Avoid locale-specific assumptions</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
