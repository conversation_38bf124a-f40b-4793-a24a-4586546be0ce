import { PageLayout } from "@/components/page-layout"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Layout, <PERSON>tbrush } from "lucide-react"
import Link from "next/link"

export default function Home() {
  return (
    <PageLayout>
      <div className="max-w-6xl mx-auto">
        {/* Hero Section */}
        <div className="legalyard-hero text-center py-20 legalyard-fade-in">
          <div className="mb-8">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-orange-100 to-amber-100 dark:from-orange-900/20 dark:to-amber-900/20 text-orange-800 dark:text-orange-200 text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse"></span>
              Latest Design System v2.0
            </div>
            <h1 className="text-5xl md:text-7xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-orange-600 via-amber-500 to-blue-600 bg-clip-text text-transparent">
                Legalyard
              </span>
              <br />
              <span className="text-foreground">Design System</span>
            </h1>
          </div>
          <p className="text-xl text-muted-foreground mb-10 max-w-4xl mx-auto leading-relaxed">
            A comprehensive collection of reusable UI components, utilities, hooks, and helpers
            built with React, TypeScript, and Tailwind CSS for modern legal tech applications.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link
              href="/installation"
              className="legalyard-button inline-flex items-center justify-center text-sm font-semibold h-12 px-8 rounded-xl"
            >
              Get Started
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <Link
              href="/components"
              className="inline-flex items-center justify-center rounded-xl text-sm font-semibold transition-all duration-200 border-2 border-border bg-background hover:bg-accent hover:border-accent h-12 px-8 hover:shadow-lg"
            >
              View Components
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">25+</div>
              <div className="text-sm text-muted-foreground">Components</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-secondary">50+</div>
              <div className="text-sm text-muted-foreground">Utilities</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent">30+</div>
              <div className="text-sm text-muted-foreground">Hooks</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-sky-500 bg-clip-text text-transparent">100%</div>
              <div className="text-sm text-muted-foreground">TypeScript</div>
            </div>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-16">
          <FeatureCard
            icon={<Palette className="h-8 w-8" />}
            title="UI Components"
            description="25+ carefully crafted components including buttons, inputs, modals, and more."
            href="/components"
          />
          <FeatureCard
            icon={<Layout className="h-8 w-8" />}
            title="Layouts"
            description="Flexible layout components for headers, footers, sidebars, and page containers."
            href="/layouts"
          />
          <FeatureCard
            icon={<Paintbrush className="h-8 w-8" />}
            title="Styles & Theming"
            description="Comprehensive theming system with dark/light mode and customizable variables."
            href="/styles"
          />
          <FeatureCard
            icon={<Wrench className="h-8 w-8" />}
            title="Utilities"
            description="Helper functions for data manipulation, API calls, and common operations."
            href="/utils"
          />
          <FeatureCard
            icon={<Zap className="h-8 w-8" />}
            title="React Hooks"
            description="Custom hooks for state management, side effects, and enhanced functionality."
            href="/hooks"
          />
          <FeatureCard
            icon={<Shield className="h-8 w-8" />}
            title="Validators"
            description="Form validation utilities and schema validators for data integrity."
            href="/validators"
          />
        </div>
      </div>
    </PageLayout>
  )
}

interface FeatureCardProps {
  icon: React.ReactNode
  title: string
  description: string
  href: string
}

function FeatureCard({ icon, title, description, href }: FeatureCardProps) {
  return (
    <Link
      href={href}
      className="legalyard-card group relative p-6 block legalyard-slide-up"
    >
      <div className="flex items-center space-x-3 mb-4">
        <div className="p-2 rounded-lg bg-gradient-to-br from-orange-100 to-amber-100 dark:from-orange-900/20 dark:to-amber-900/20 text-primary">
          {icon}
        </div>
        <h3 className="text-lg font-semibold group-hover:text-primary transition-colors">{title}</h3>
      </div>
      <p className="text-muted-foreground text-sm leading-relaxed mb-4">{description}</p>
      <div className="flex items-center text-primary text-sm font-medium group-hover:translate-x-1 transition-transform">
        <span>Learn more</span>
        <ArrowRight className="ml-1 h-4 w-4" />
      </div>
    </Link>
  )
}
