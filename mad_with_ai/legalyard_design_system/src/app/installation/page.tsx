import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"

export default function InstallationPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Installation</h1>
          <p className="text-xl text-muted-foreground">
            Get started with Legalyard Design System in your React project.
          </p>
        </div>

        <div className="space-y-8">
          {/* Prerequisites */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Prerequisites</h2>
            <div className="bg-muted/50 rounded-lg p-6">
              <p className="mb-4">Before installing, make sure you have:</p>
              <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                <li>Node.js 18.0 or later</li>
                <li>React 18.0 or later</li>
                <li>TypeScript (recommended)</li>
                <li>Tailwind CSS configured in your project</li>
              </ul>
            </div>
          </section>

          {/* Installation Steps */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Installation Steps</h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">1. Install Dependencies</h3>
                <CodeBlock code="npm install @legalyard/design-system" />
                <p className="text-sm text-muted-foreground mt-2">
                  Or if you prefer yarn:
                </p>
                <CodeBlock code="yarn add @legalyard/design-system" />
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">2. Install Peer Dependencies</h3>
                <CodeBlock code="npm install react react-dom @types/react @types/react-dom" />
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">3. Configure Tailwind CSS</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  Add the design system paths to your tailwind.config.js:
                </p>
                <CodeBlock 
                  code={`module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@legalyard/design-system/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`}
                />
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">4. Import Styles</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  Import the CSS in your main application file:
                </p>
                <CodeBlock code={`import '@legalyard/design-system/dist/index.css'`} />
              </div>
            </div>
          </section>

          {/* Usage Example */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Basic Usage</h2>
            <CodeBlock 
              code={`import { Button, Input, Modal } from '@legalyard/design-system'

function App() {
  return (
    <div>
      <Button variant="primary">Click me</Button>
      <Input placeholder="Enter text..." />
    </div>
  )
}`}
            />
          </section>

          {/* Next Steps */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">Next Steps</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">Explore Components</h3>
                <p className="text-sm text-muted-foreground">
                  Browse our comprehensive component library with examples and documentation.
                </p>
              </div>
              <div className="border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">Theming Guide</h3>
                <p className="text-sm text-muted-foreground">
                  Learn how to customize colors, typography, and spacing to match your brand.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </PageLayout>
  )
}


