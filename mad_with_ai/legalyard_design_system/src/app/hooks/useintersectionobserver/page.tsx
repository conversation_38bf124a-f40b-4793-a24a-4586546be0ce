import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"

export default function UseIntersectionObserverHookPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">useIntersectionObserver</h1>
          <p className="text-xl text-muted-foreground">
            A React hook for detecting when elements enter or leave the viewport using the Intersection Observer API.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Usage */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Visibility Detection</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="h-32 bg-gradient-to-r from-orange-100 to-amber-100 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-sm font-medium">Target Element</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Status: <span className="font-mono text-green-600">Visible</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Scroll to see the visibility status change in real applications.
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { useRef } from 'react'
import { useIntersectionObserver } from '@legalyard/design-system/hooks'

function VisibilityTracker() {
  const targetRef = useRef<HTMLDivElement>(null)
  const isVisible = useIntersectionObserver(targetRef)

  return (
    <div>
      <div style={{ height: '100vh' }}>Scroll down...</div>
      
      <div 
        ref={targetRef}
        className={isVisible ? 'visible' : 'hidden'}
      >
        <h2>Target Element</h2>
        <p>Status: {isVisible ? 'Visible' : 'Hidden'}</p>
      </div>
      
      <div style={{ height: '100vh' }}>More content...</div>
    </div>
  )
}`}
              />
            </div>

            {/* Lazy Loading Images */}
            <div>
              <h3 className="text-lg font-medium mb-4">Lazy Loading Images</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="h-32 bg-muted rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-sm font-medium">Image 1</div>
                          <div className="text-xs text-green-600">✓ Loaded</div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-32 bg-muted rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-sm font-medium">Image 2</div>
                          <div className="text-xs text-muted-foreground">Loading...</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { useRef, useState } from 'react'
import { useIntersectionObserver } from '@legalyard/design-system/hooks'

function LazyImage({ src, alt, ...props }) {
  const imgRef = useRef<HTMLImageElement>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const isVisible = useIntersectionObserver(imgRef, {
    threshold: 0.1,
    triggerOnce: true // Only trigger once when first visible
  })

  return (
    <div className="relative">
      {isVisible && (
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          onLoad={() => setIsLoaded(true)}
          className={isLoaded ? 'opacity-100' : 'opacity-0'}
          style={{ transition: 'opacity 0.3s' }}
          {...props}
        />
      )}
      {!isLoaded && isVisible && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
    </div>
  )
}

function ImageGallery() {
  const images = [
    '/image1.jpg',
    '/image2.jpg',
    '/image3.jpg'
  ]

  return (
    <div className="space-y-8">
      {images.map((src, index) => (
        <LazyImage
          key={index}
          src={src}
          alt={\`Image \${index + 1}\`}
          className="w-full h-64 object-cover rounded-lg"
        />
      ))}
    </div>
  )
}`}
              />
            </div>

            {/* Infinite Scrolling */}
            <div>
              <h3 className="text-lg font-medium mb-4">Infinite Scrolling</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    {Array.from({ length: 5 }, (_, i) => (
                      <div key={i} className="p-4 border border-border rounded-lg">
                        <div className="font-medium">Item {i + 1}</div>
                        <div className="text-sm text-muted-foreground">Content for item {i + 1}</div>
                      </div>
                    ))}
                  </div>
                  <div className="text-center py-4">
                    <div className="inline-flex items-center text-sm text-muted-foreground">
                      <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2"></div>
                      Loading more items...
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { useRef, useState, useCallback } from 'react'
import { useIntersectionObserver } from '@legalyard/design-system/hooks'

function InfiniteList() {
  const [items, setItems] = useState(Array.from({ length: 20 }, (_, i) => i))
  const [isLoading, setIsLoading] = useState(false)
  const loadMoreRef = useRef<HTMLDivElement>(null)

  const loadMore = useCallback(async () => {
    if (isLoading) return
    
    setIsLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setItems(prev => [
      ...prev,
      ...Array.from({ length: 20 }, (_, i) => prev.length + i)
    ])
    
    setIsLoading(false)
  }, [isLoading])

  const isVisible = useIntersectionObserver(loadMoreRef, {
    threshold: 0.1
  })

  // Load more when the trigger element becomes visible
  useEffect(() => {
    if (isVisible && !isLoading) {
      loadMore()
    }
  }, [isVisible, isLoading, loadMore])

  return (
    <div>
      <div className="space-y-4">
        {items.map(item => (
          <div key={item} className="p-4 border rounded">
            Item {item + 1}
          </div>
        ))}
      </div>
      
      <div ref={loadMoreRef} className="py-4 text-center">
        {isLoading ? (
          <div>Loading more items...</div>
        ) : (
          <div>Scroll to load more</div>
        )}
      </div>
    </div>
  )
}`}
              />
            </div>

            {/* Scroll Animations */}
            <div>
              <h3 className="text-lg font-medium mb-4">Scroll Animations</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-8">
                  <div className="h-32 bg-gradient-to-r from-blue-100 to-sky-100 dark:from-blue-900/20 dark:to-sky-900/20 rounded-lg flex items-center justify-center transform transition-all duration-700 translate-y-0 opacity-100">
                    <div className="text-center">
                      <div className="text-sm font-medium">Animated Element 1</div>
                      <div className="text-xs text-muted-foreground">Slide in from bottom</div>
                    </div>
                  </div>
                  <div className="h-32 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg flex items-center justify-center transform transition-all duration-700 delay-200 translate-y-0 opacity-100">
                    <div className="text-center">
                      <div className="text-sm font-medium">Animated Element 2</div>
                      <div className="text-xs text-muted-foreground">Fade in with delay</div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { useRef } from 'react'
import { useIntersectionObserver } from '@legalyard/design-system/hooks'

function AnimatedSection({ children, animation = 'fadeIn', delay = 0 }) {
  const ref = useRef<HTMLDivElement>(null)
  const isVisible = useIntersectionObserver(ref, {
    threshold: 0.2,
    triggerOnce: true
  })

  const getAnimationClasses = () => {
    const base = 'transition-all duration-700'
    const delayClass = delay > 0 ? \`delay-\${delay}\` : ''
    
    if (!isVisible) {
      switch (animation) {
        case 'slideUp':
          return \`\${base} \${delayClass} translate-y-8 opacity-0\`
        case 'slideLeft':
          return \`\${base} \${delayClass} translate-x-8 opacity-0\`
        case 'scale':
          return \`\${base} \${delayClass} scale-95 opacity-0\`
        default:
          return \`\${base} \${delayClass} opacity-0\`
      }
    }
    
    return \`\${base} \${delayClass} translate-y-0 translate-x-0 scale-100 opacity-100\`
  }

  return (
    <div ref={ref} className={getAnimationClasses()}>
      {children}
    </div>
  )
}

function ScrollAnimations() {
  return (
    <div className="space-y-16">
      <AnimatedSection animation="slideUp">
        <h2>Slide Up Animation</h2>
        <p>This element slides up when it comes into view.</p>
      </AnimatedSection>
      
      <AnimatedSection animation="fadeIn" delay={200}>
        <h2>Fade In with Delay</h2>
        <p>This element fades in with a 200ms delay.</p>
      </AnimatedSection>
      
      <AnimatedSection animation="scale">
        <h2>Scale Animation</h2>
        <p>This element scales up when visible.</p>
      </AnimatedSection>
    </div>
  )
}`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Parameters</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Parameter</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">elementRef</td>
                      <td className="p-4 text-sm">RefObject&lt;Element&gt;</td>
                      <td className="p-4 text-sm">Ref to the element to observe</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">options</td>
                      <td className="p-4 text-sm">IntersectionObserverOptions</td>
                      <td className="p-4 text-sm">Optional configuration object</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Options</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Option</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Default</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">threshold</td>
                      <td className="p-4 text-sm">number | number[]</td>
                      <td className="p-4 text-sm">0</td>
                      <td className="p-4 text-sm">Percentage of element visibility to trigger</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">root</td>
                      <td className="p-4 text-sm">Element | null</td>
                      <td className="p-4 text-sm">null</td>
                      <td className="p-4 text-sm">Root element for intersection (viewport if null)</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">rootMargin</td>
                      <td className="p-4 text-sm">string</td>
                      <td className="p-4 text-sm">'0px'</td>
                      <td className="p-4 text-sm">Margin around the root element</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">triggerOnce</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">false</td>
                      <td className="p-4 text-sm">Whether to trigger only once</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Returns</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Return Value</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">isIntersecting</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">Whether the element is currently intersecting</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">entry</td>
                      <td className="p-4 text-sm">IntersectionObserverEntry | null</td>
                      <td className="p-4 text-sm">The latest intersection observer entry</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use triggerOnce for one-time animations</li>
                <li>• Set appropriate threshold values</li>
                <li>• Use rootMargin for early triggering</li>
                <li>• Implement loading states for better UX</li>
                <li>• Consider performance with many observers</li>
                <li>• Provide fallbacks for unsupported browsers</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't create too many observers</li>
                <li>• Avoid complex calculations in callbacks</li>
                <li>• Don't forget to handle edge cases</li>
                <li>• Avoid triggering expensive operations</li>
                <li>• Don't assume browser support</li>
                <li>• Avoid memory leaks with proper cleanup</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
