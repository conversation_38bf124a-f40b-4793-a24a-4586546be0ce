import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"

export default function UseLocalStorageHookPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">useLocalStorage</h1>
          <p className="text-xl text-muted-foreground">
            A React hook for managing state that persists in localStorage with automatic serialization and type safety.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Usage */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Usage</h3>
              <div className="legalyard-card p-6">
                <div className="max-w-md space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">User Preferences</label>
                    <div className="p-4 border border-border rounded-lg bg-muted/30">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Theme:</span>
                          <span className="text-sm font-mono">dark</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Language:</span>
                          <span className="text-sm font-mono">en</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Notifications:</span>
                          <span className="text-sm font-mono">true</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-9 px-4">
                      Toggle Theme
                    </button>
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-9 px-4">
                      Reset
                    </button>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { useLocalStorage } from '@legalyard/design-system/hooks'

function UserPreferences() {
  const [preferences, setPreferences] = useLocalStorage('userPrefs', {
    theme: 'light',
    language: 'en',
    notifications: true
  })

  const toggleTheme = () => {
    setPreferences(prev => ({
      ...prev,
      theme: prev.theme === 'light' ? 'dark' : 'light'
    }))
  }

  const resetPreferences = () => {
    setPreferences({
      theme: 'light',
      language: 'en',
      notifications: true
    })
  }

  return (
    <div>
      <h3>Current Preferences:</h3>
      <pre>{JSON.stringify(preferences, null, 2)}</pre>
      <button onClick={toggleTheme}>Toggle Theme</button>
      <button onClick={resetPreferences}>Reset</button>
    </div>
  )
}`}
              />
            </div>

            {/* Form State Persistence */}
            <div>
              <h3 className="text-lg font-medium mb-4">Form State Persistence</h3>
              <div className="legalyard-card p-6">
                <div className="max-w-md space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Draft Form</label>
                    <input
                      type="text"
                      placeholder="Title"
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                  <div className="space-y-2">
                    <textarea
                      placeholder="Content"
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 min-h-[100px] px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                    />
                  </div>
                  <div className="text-xs text-muted-foreground">
                    ✓ Draft saved automatically
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { useLocalStorage } from '@legalyard/design-system/hooks'

function DraftForm() {
  const [formData, setFormData] = useLocalStorage('formDraft', {
    title: '',
    content: '',
    lastSaved: null
  })

  const updateField = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      lastSaved: new Date().toISOString()
    }))
  }

  const clearDraft = () => {
    setFormData({
      title: '',
      content: '',
      lastSaved: null
    })
  }

  return (
    <form>
      <input
        value={formData.title}
        onChange={(e) => updateField('title', e.target.value)}
        placeholder="Title"
      />
      <textarea
        value={formData.content}
        onChange={(e) => updateField('content', e.target.value)}
        placeholder="Content"
      />
      {formData.lastSaved && (
        <p>Last saved: {new Date(formData.lastSaved).toLocaleString()}</p>
      )}
      <button type="button" onClick={clearDraft}>
        Clear Draft
      </button>
    </form>
  )
}`}
              />
            </div>

            {/* Shopping Cart */}
            <div>
              <h3 className="text-lg font-medium mb-4">Shopping Cart</h3>
              <div className="legalyard-card p-6">
                <div className="max-w-md space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Cart Items</label>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-3 border border-border rounded-lg">
                        <div>
                          <div className="font-medium text-sm">Laptop</div>
                          <div className="text-xs text-muted-foreground">$999.99</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <button className="w-6 h-6 rounded border border-border flex items-center justify-center text-xs hover:bg-accent">-</button>
                          <span className="text-sm font-mono">1</span>
                          <button className="w-6 h-6 rounded border border-border flex items-center justify-center text-xs hover:bg-accent">+</button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 border border-border rounded-lg">
                        <div>
                          <div className="font-medium text-sm">Mouse</div>
                          <div className="text-xs text-muted-foreground">$29.99</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <button className="w-6 h-6 rounded border border-border flex items-center justify-center text-xs hover:bg-accent">-</button>
                          <span className="text-sm font-mono">2</span>
                          <button className="w-6 h-6 rounded border border-border flex items-center justify-center text-xs hover:bg-accent">+</button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between pt-2 border-t border-border">
                    <span className="font-medium">Total: $1,059.97</span>
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-9 px-4">
                      Checkout
                    </button>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { useLocalStorage } from '@legalyard/design-system/hooks'

interface CartItem {
  id: string
  name: string
  price: number
  quantity: number
}

function ShoppingCart() {
  const [cartItems, setCartItems] = useLocalStorage<CartItem[]>('cart', [])

  const addItem = (item: Omit<CartItem, 'quantity'>) => {
    setCartItems(prev => {
      const existing = prev.find(i => i.id === item.id)
      if (existing) {
        return prev.map(i => 
          i.id === item.id 
            ? { ...i, quantity: i.quantity + 1 }
            : i
        )
      }
      return [...prev, { ...item, quantity: 1 }]
    })
  }

  const updateQuantity = (id: string, quantity: number) => {
    if (quantity <= 0) {
      setCartItems(prev => prev.filter(item => item.id !== id))
    } else {
      setCartItems(prev => 
        prev.map(item => 
          item.id === id ? { ...item, quantity } : item
        )
      )
    }
  }

  const total = cartItems.reduce((sum, item) => 
    sum + (item.price * item.quantity), 0
  )

  return (
    <div>
      {cartItems.map(item => (
        <div key={item.id}>
          <span>{item.name} - ${item.price}</span>
          <button onClick={() => updateQuantity(item.id, item.quantity - 1)}>
            -
          </button>
          <span>{item.quantity}</span>
          <button onClick={() => updateQuantity(item.id, item.quantity + 1)}>
            +
          </button>
        </div>
      ))}
      <div>Total: ${total.toFixed(2)}</div>
    </div>
  )
}`}
              />
            </div>

            {/* Error Handling */}
            <div>
              <h3 className="text-lg font-medium mb-4">Error Handling</h3>
              <div className="legalyard-card p-6">
                <div className="max-w-md space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Settings with Fallback</label>
                    <div className="p-4 border border-border rounded-lg bg-muted/30">
                      <div className="text-sm">
                        <div>Storage available: <span className="font-mono text-green-600">✓</span></div>
                        <div>Fallback mode: <span className="font-mono text-muted-foreground">disabled</span></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { useLocalStorage } from '@legalyard/design-system/hooks'

function SettingsWithFallback() {
  const [settings, setSettings] = useLocalStorage('appSettings', {
    theme: 'light',
    autoSave: true
  }, {
    // Error handling options
    fallbackToMemory: true,
    onError: (error) => {
      console.warn('localStorage error:', error)
      // Could show a toast notification
    }
  })

  // The hook will automatically fall back to memory storage
  // if localStorage is not available or throws an error

  return (
    <div>
      <label>
        <input
          type="checkbox"
          checked={settings.autoSave}
          onChange={(e) => setSettings(prev => ({
            ...prev,
            autoSave: e.target.checked
          }))}
        />
        Auto Save
      </label>
    </div>
  )
}`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="legalyard-card overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">Parameter</th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">key</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">The localStorage key to use</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">initialValue</td>
                  <td className="p-4 text-sm">T</td>
                  <td className="p-4 text-sm">The initial value if no stored value exists</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">options</td>
                  <td className="p-4 text-sm">UseLocalStorageOptions</td>
                  <td className="p-4 text-sm">Optional configuration object</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-medium mb-4">Options</h3>
            <div className="legalyard-card overflow-hidden">
              <table className="w-full">
                <thead className="bg-muted/50">
                  <tr>
                    <th className="text-left p-4 font-medium">Option</th>
                    <th className="text-left p-4 font-medium">Type</th>
                    <th className="text-left p-4 font-medium">Default</th>
                    <th className="text-left p-4 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-t border-border">
                    <td className="p-4 font-mono text-sm">fallbackToMemory</td>
                    <td className="p-4 text-sm">boolean</td>
                    <td className="p-4 text-sm">true</td>
                    <td className="p-4 text-sm">Fall back to memory storage if localStorage fails</td>
                  </tr>
                  <tr className="border-t border-border">
                    <td className="p-4 font-mono text-sm">onError</td>
                    <td className="p-4 text-sm">(error: Error) => void</td>
                    <td className="p-4 text-sm">-</td>
                    <td className="p-4 text-sm">Error handler callback</td>
                  </tr>
                  <tr className="border-t border-border">
                    <td className="p-4 font-mono text-sm">serializer</td>
                    <td className="p-4 text-sm">Serializer&lt;T&gt;</td>
                    <td className="p-4 text-sm">JSON</td>
                    <td className="p-4 text-sm">Custom serialization functions</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-medium mb-4">Returns</h3>
            <div className="legalyard-card overflow-hidden">
              <table className="w-full">
                <thead className="bg-muted/50">
                  <tr>
                    <th className="text-left p-4 font-medium">Return Value</th>
                    <th className="text-left p-4 font-medium">Type</th>
                    <th className="text-left p-4 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-t border-border">
                    <td className="p-4 font-mono text-sm">[0]</td>
                    <td className="p-4 text-sm">T</td>
                    <td className="p-4 text-sm">The current stored value</td>
                  </tr>
                  <tr className="border-t border-border">
                    <td className="p-4 font-mono text-sm">[1]</td>
                    <td className="p-4 text-sm">(value: T | ((prev: T) => T)) => void</td>
                    <td className="p-4 text-sm">Function to update the stored value</td>
                  </tr>
                  <tr className="border-t border-border">
                    <td className="p-4 font-mono text-sm">[2]</td>
                    <td className="p-4 text-sm">() => void</td>
                    <td className="p-4 text-sm">Function to remove the stored value</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use descriptive, unique keys</li>
                <li>• Provide sensible default values</li>
                <li>• Handle storage errors gracefully</li>
                <li>• Consider storage size limitations</li>
                <li>• Use TypeScript for type safety</li>
                <li>• Clear sensitive data when appropriate</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't store sensitive information</li>
                <li>• Avoid storing large objects</li>
                <li>• Don't assume localStorage is always available</li>
                <li>• Avoid frequent updates to large objects</li>
                <li>• Don't store functions or complex objects</li>
                <li>• Avoid using reserved key names</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
