import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import Link from "next/link"
import { <PERSON>R<PERSON>, Paintbrush, Palette, Type, Move } from "lucide-react"

const colorPalette = {
  primary: {
    50: "#eff6ff",
    100: "#dbeafe", 
    200: "#bfdbfe",
    300: "#93c5fd",
    400: "#60a5fa",
    500: "#3b82f6",
    600: "#2563eb",
    700: "#1d4ed8",
    800: "#1e40af",
    900: "#1e3a8a"
  },
  gray: {
    50: "#f9fafb",
    100: "#f3f4f6",
    200: "#e5e7eb", 
    300: "#d1d5db",
    400: "#9ca3af",
    500: "#6b7280",
    600: "#4b5563",
    700: "#374151",
    800: "#1f2937",
    900: "#111827"
  }
}

const typography = {
  fontSizes: {
    xs: "0.75rem",
    sm: "0.875rem", 
    base: "1rem",
    lg: "1.125rem",
    xl: "1.25rem",
    "2xl": "1.5rem",
    "3xl": "1.875rem",
    "4xl": "2.25rem",
    "5xl": "3rem",
    "6xl": "3.75rem"
  },
  fontWeights: {
    thin: "100",
    light: "300",
    normal: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
    extrabold: "800",
    black: "900"
  },
  lineHeights: {
    none: "1",
    tight: "1.25",
    snug: "1.375",
    normal: "1.5",
    relaxed: "1.625",
    loose: "2"
  }
}

const spacing = {
  0: "0px",
  1: "0.25rem",
  2: "0.5rem", 
  3: "0.75rem",
  4: "1rem",
  5: "1.25rem",
  6: "1.5rem",
  8: "2rem",
  10: "2.5rem",
  12: "3rem",
  16: "4rem",
  20: "5rem",
  24: "6rem",
  32: "8rem"
}

export default function StylesPage() {
  return (
    <PageLayout>
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Styles & Theming</h1>
          <p className="text-xl text-muted-foreground">
            Design tokens, color palettes, typography, and theming system for consistent visual design.
          </p>
        </div>

        {/* Color Palette */}
        <section className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <Palette className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-semibold">Color Palette</h2>
          </div>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-medium mb-4">Primary Colors</h3>
              <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
                {Object.entries(colorPalette.primary).map(([shade, color]) => (
                  <div key={shade} className="text-center">
                    <div 
                      className="w-full h-16 rounded-lg border border-border mb-2"
                      style={{ backgroundColor: color }}
                    ></div>
                    <div className="text-xs font-mono">{shade}</div>
                    <div className="text-xs text-muted-foreground">{color}</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Gray Scale</h3>
              <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
                {Object.entries(colorPalette.gray).map(([shade, color]) => (
                  <div key={shade} className="text-center">
                    <div 
                      className="w-full h-16 rounded-lg border border-border mb-2"
                      style={{ backgroundColor: color }}
                    ></div>
                    <div className="text-xs font-mono">{shade}</div>
                    <div className="text-xs text-muted-foreground">{color}</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Semantic Colors</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="w-full h-16 bg-green-500 rounded-lg border border-border mb-2"></div>
                  <div className="text-sm font-medium">Success</div>
                  <div className="text-xs text-muted-foreground">#10b981</div>
                </div>
                <div className="text-center">
                  <div className="w-full h-16 bg-yellow-500 rounded-lg border border-border mb-2"></div>
                  <div className="text-sm font-medium">Warning</div>
                  <div className="text-xs text-muted-foreground">#f59e0b</div>
                </div>
                <div className="text-center">
                  <div className="w-full h-16 bg-red-500 rounded-lg border border-border mb-2"></div>
                  <div className="text-sm font-medium">Error</div>
                  <div className="text-xs text-muted-foreground">#ef4444</div>
                </div>
                <div className="text-center">
                  <div className="w-full h-16 bg-blue-500 rounded-lg border border-border mb-2"></div>
                  <div className="text-sm font-medium">Info</div>
                  <div className="text-xs text-muted-foreground">#3b82f6</div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <CodeBlock 
              code={`// Using colors in CSS
.primary-button {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

// Using colors in Tailwind
<button className="bg-primary text-primary-foreground">
  Primary Button
</button>

// Using colors in JavaScript
import { colors } from '@legalyard/design-system/tokens'

const primaryColor = colors.primary[500]`}
            />
          </div>
        </section>

        {/* Typography */}
        <section className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <Type className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-semibold">Typography</h2>
          </div>

          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-medium mb-4">Font Sizes</h3>
              <div className="space-y-4">
                {Object.entries(typography.fontSizes).map(([size, value]) => (
                  <div key={size} className="flex items-center space-x-4">
                    <div className="w-16 text-sm font-mono text-muted-foreground">{size}</div>
                    <div className="w-20 text-xs text-muted-foreground">{value}</div>
                    <div style={{ fontSize: value }}>The quick brown fox jumps over the lazy dog</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Font Weights</h3>
              <div className="space-y-2">
                {Object.entries(typography.fontWeights).map(([weight, value]) => (
                  <div key={weight} className="flex items-center space-x-4">
                    <div className="w-20 text-sm font-mono text-muted-foreground">{weight}</div>
                    <div className="w-16 text-xs text-muted-foreground">{value}</div>
                    <div style={{ fontWeight: value }}>The quick brown fox jumps over the lazy dog</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Heading Styles</h3>
              <div className="space-y-4">
                <h1 className="text-4xl font-bold">Heading 1 - Main page title</h1>
                <h2 className="text-3xl font-semibold">Heading 2 - Section title</h2>
                <h3 className="text-2xl font-semibold">Heading 3 - Subsection title</h3>
                <h4 className="text-xl font-medium">Heading 4 - Component title</h4>
                <h5 className="text-lg font-medium">Heading 5 - Small section</h5>
                <h6 className="text-base font-medium">Heading 6 - Label</h6>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <CodeBlock 
              code={`// Typography classes
<h1 className="text-4xl font-bold">Main Title</h1>
<h2 className="text-2xl font-semibold">Section Title</h2>
<p className="text-base leading-relaxed">Body text with relaxed line height</p>
<small className="text-sm text-muted-foreground">Helper text</small>

// Custom typography
.heading-xl {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}`}
            />
          </div>
        </section>

        {/* Spacing */}
        <section className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <Move className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-semibold">Spacing</h2>
          </div>

          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Spacing Scale</h3>
              <div className="space-y-2">
                {Object.entries(spacing).map(([token, value]) => (
                  <div key={token} className="flex items-center space-x-4">
                    <div className="w-8 text-sm font-mono text-muted-foreground">{token}</div>
                    <div className="w-20 text-xs text-muted-foreground">{value}</div>
                    <div 
                      className="bg-primary/20 border-l-2 border-primary"
                      style={{ height: '1rem', width: value }}
                    ></div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Common Spacing Patterns</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 border border-border rounded-lg">
                  <h4 className="font-medium mb-2">Component Spacing</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Small components: 2-4 (0.5rem - 1rem)</li>
                    <li>• Medium components: 4-6 (1rem - 1.5rem)</li>
                    <li>• Large components: 6-8 (1.5rem - 2rem)</li>
                  </ul>
                </div>
                <div className="p-4 border border-border rounded-lg">
                  <h4 className="font-medium mb-2">Layout Spacing</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Section gaps: 8-12 (2rem - 3rem)</li>
                    <li>• Page margins: 4-8 (1rem - 2rem)</li>
                    <li>• Grid gaps: 4-6 (1rem - 1.5rem)</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <CodeBlock 
              code={`// Spacing utilities
<div className="p-4">Padding: 1rem</div>
<div className="m-6">Margin: 1.5rem</div>
<div className="space-y-4">Vertical spacing between children</div>
<div className="gap-6">Grid/flex gap: 1.5rem</div>

// Custom spacing
.card {
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-4);
}`}
            />
          </div>
        </section>

        {/* Dark Mode */}
        <section className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <Paintbrush className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-semibold">Dark Mode</h2>
          </div>

          <div className="space-y-6">
            <p className="text-muted-foreground">
              The design system includes comprehensive dark mode support with automatic theme switching.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-6 border border-border rounded-lg bg-background">
                <h3 className="font-medium mb-4">Light Theme</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-background border border-border rounded"></div>
                    <span className="text-sm">Background</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-foreground rounded"></div>
                    <span className="text-sm">Foreground</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-muted rounded"></div>
                    <span className="text-sm">Muted</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-primary rounded"></div>
                    <span className="text-sm">Primary</span>
                  </div>
                </div>
              </div>

              <div className="p-6 border border-border rounded-lg bg-gray-900 text-white">
                <h3 className="font-medium mb-4">Dark Theme</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-gray-900 border border-gray-700 rounded"></div>
                    <span className="text-sm">Background</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-white rounded"></div>
                    <span className="text-sm">Foreground</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-gray-800 rounded"></div>
                    <span className="text-sm">Muted</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-blue-500 rounded"></div>
                    <span className="text-sm">Primary</span>
                  </div>
                </div>
              </div>
            </div>

            <CodeBlock 
              code={`// Automatic dark mode with CSS variables
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
}

// Using theme-aware colors
<div className="bg-background text-foreground">
  Content that adapts to theme
</div>

// Theme toggle component
import { useTheme } from 'next-themes'

function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  
  return (
    <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
      Toggle Theme
    </button>
  )
}`}
            />
          </div>
        </section>

        {/* Customization */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Customization</h2>
          
          <div className="bg-muted/50 rounded-lg p-6">
            <p className="mb-4">Customize the design system to match your brand:</p>
            <CodeBlock 
              code={`// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#your-color-50',
          500: '#your-primary-color',
          900: '#your-color-900',
        },
        brand: {
          primary: '#your-brand-primary',
          secondary: '#your-brand-secondary',
        }
      },
      fontFamily: {
        sans: ['Your Font', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      }
    }
  }
}

// CSS custom properties
:root {
  --your-primary: 220 100% 50%;
  --your-secondary: 280 100% 70%;
  --your-radius: 0.75rem;
}`}
            />
          </div>
        </section>

        {/* Related Links */}
        <section className="mt-16 p-8 bg-muted/50 rounded-lg">
          <h3 className="text-xl font-semibold mb-4">Related</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/components"
              className="flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors"
            >
              <span>UI Components</span>
              <ArrowRight className="h-4 w-4" />
            </Link>
            <Link
              href="/layouts"
              className="flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors"
            >
              <span>Layouts</span>
              <ArrowRight className="h-4 w-4" />
            </Link>
            <Link
              href="/installation"
              className="flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors"
            >
              <span>Installation</span>
              <ArrowRight className="h-4 w-4" />
            </Link>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
