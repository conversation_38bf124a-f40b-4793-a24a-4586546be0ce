import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { Calendar, ChevronLeft, ChevronRight } from "lucide-react"

export default function DatePickerPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Date Picker</h1>
          <p className="text-xl text-muted-foreground">
            A calendar-based date selection component with range selection and customizable formatting.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Usage */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Usage</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Birth Date</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span className="text-muted-foreground">Select a date</span>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Event Date</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span>December 25, 2023</span>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { DatePicker } from '@legalyard/design-system'

function Example() {
  const [date, setDate] = useState<Date>()

  return (
    <div className="space-y-4">
      <DatePicker
        label="Birth Date"
        placeholder="Select a date"
        value={date}
        onChange={setDate}
      />
      
      <DatePicker
        label="Event Date"
        value={new Date('2023-12-25')}
        onChange={setDate}
      />
    </div>
  )
}`}
              />
            </div>

            {/* Date Range */}
            <div>
              <h3 className="text-lg font-medium mb-4">Date Range</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Project Duration</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span>Jan 15, 2024 - Feb 28, 2024</span>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { DateRangePicker } from '@legalyard/design-system'

function Example() {
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: new Date('2024-01-15'),
    to: new Date('2024-02-28')
  })

  return (
    <DateRangePicker
      label="Project Duration"
      value={dateRange}
      onChange={setDateRange}
    />
  )
}`}
              />
            </div>

            {/* Calendar View */}
            <div>
              <h3 className="text-lg font-medium mb-4">Calendar View</h3>
              <div className="legalyard-card p-6">
                <div className="max-w-sm mx-auto">
                  <div className="rounded-lg border border-border bg-background p-4">
                    {/* Calendar Header */}
                    <div className="flex items-center justify-between mb-4">
                      <button className="p-1 hover:bg-accent rounded">
                        <ChevronLeft className="h-4 w-4" />
                      </button>
                      <h3 className="font-medium">December 2023</h3>
                      <button className="p-1 hover:bg-accent rounded">
                        <ChevronRight className="h-4 w-4" />
                      </button>
                    </div>
                    
                    {/* Calendar Grid */}
                    <div className="grid grid-cols-7 gap-1 text-center text-sm">
                      {/* Day headers */}
                      {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
                        <div key={day} className="p-2 text-muted-foreground font-medium">
                          {day}
                        </div>
                      ))}
                      
                      {/* Calendar days */}
                      {Array.from({ length: 35 }, (_, i) => {
                        const day = i - 4; // Adjust for month start
                        const isCurrentMonth = day > 0 && day <= 31;
                        const isSelected = day === 15;
                        const isToday = day === 10;
                        
                        return (
                          <button
                            key={i}
                            className={`p-2 rounded text-sm hover:bg-accent ${
                              !isCurrentMonth 
                                ? 'text-muted-foreground/50' 
                                : isSelected 
                                ? 'bg-primary text-primary-foreground' 
                                : isToday 
                                ? 'bg-accent font-medium' 
                                : ''
                            }`}
                          >
                            {isCurrentMonth ? day : ''}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<DatePicker
  mode="calendar"
  showCalendar={true}
  value={selectedDate}
  onChange={setSelectedDate}
/>`}
              />
            </div>

            {/* With Time */}
            <div>
              <h3 className="text-lg font-medium mb-4">Date and Time</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Meeting Schedule</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span>Dec 15, 2023 at 2:30 PM</span>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<DateTimePicker
  label="Meeting Schedule"
  value={meetingDate}
  onChange={setMeetingDate}
  showTime={true}
  timeFormat="12"
/>`}
              />
            </div>

            {/* States */}
            <div>
              <h3 className="text-lg font-medium mb-4">States</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Default</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span className="text-muted-foreground">Select a date</span>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Error</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-red-500 bg-background focus-visible:border-red-500 focus-visible:ring-2 focus-visible:ring-red-500/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span className="text-muted-foreground">Select a date</span>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                    <p className="text-sm text-red-600">Please select a valid date</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Disabled</label>
                    <div className="relative">
                      <button disabled className="flex w-full items-center justify-between rounded-lg border border-border bg-muted cursor-not-allowed opacity-50 h-10 px-3 text-sm">
                        <span className="text-muted-foreground">Disabled date picker</span>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<DatePicker
  placeholder="Select a date"
  value={date}
  onChange={setDate}
/>

<DatePicker
  placeholder="Select a date"
  value={date}
  onChange={setDate}
  error="Please select a valid date"
/>

<DatePicker
  placeholder="Disabled date picker"
  disabled
/>`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">DatePicker</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Prop</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Default</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">value</td>
                      <td className="p-4 text-sm">Date | undefined</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">The selected date</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">onChange</td>
                      <td className="p-4 text-sm">(date: Date | undefined) => void</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">Callback when date changes</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">placeholder</td>
                      <td className="p-4 text-sm">string</td>
                      <td className="p-4 text-sm">'Select a date'</td>
                      <td className="p-4 text-sm">Placeholder text</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">format</td>
                      <td className="p-4 text-sm">string</td>
                      <td className="p-4 text-sm">'MMM dd, yyyy'</td>
                      <td className="p-4 text-sm">Date format string</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">disabled</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">false</td>
                      <td className="p-4 text-sm">Whether the picker is disabled</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">error</td>
                      <td className="p-4 text-sm">string</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">Error message to display</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">minDate</td>
                      <td className="p-4 text-sm">Date</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">Minimum selectable date</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">maxDate</td>
                      <td className="p-4 text-sm">Date</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">Maximum selectable date</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">DateRangePicker</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Prop</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">value</td>
                      <td className="p-4 text-sm">{ from: Date | undefined, to: Date | undefined }</td>
                      <td className="p-4 text-sm">The selected date range</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">onChange</td>
                      <td className="p-4 text-sm">(range: DateRange) => void</td>
                      <td className="p-4 text-sm">Callback when range changes</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">numberOfMonths</td>
                      <td className="p-4 text-sm">number</td>
                      <td className="p-4 text-sm">Number of months to display</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use appropriate date formats for your locale</li>
                <li>• Set reasonable min/max date constraints</li>
                <li>• Provide clear labels and placeholders</li>
                <li>• Use range pickers for duration selection</li>
                <li>• Show validation feedback clearly</li>
                <li>• Consider keyboard navigation support</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't use date pickers for birth years (use select)</li>
                <li>• Avoid unclear date format expectations</li>
                <li>• Don't forget to handle timezone considerations</li>
                <li>• Avoid overly restrictive date ranges</li>
                <li>• Don't use date pickers for approximate dates</li>
                <li>• Avoid complex date validation without clear feedback</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
