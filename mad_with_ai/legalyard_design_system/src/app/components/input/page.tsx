import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { Search, Mail, Phone } from "lucide-react"

export default function InputPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Input</h1>
          <p className="text-xl text-muted-foreground">
            A flexible input component with multiple variants, states, and built-in validation support.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Usage */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Usage</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Email</label>
                    <input
                      type="email"
                      placeholder="Enter your email"
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Password</label>
                    <input
                      type="password"
                      placeholder="Enter your password"
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Input } from '@legalyard/design-system'

function Example() {
  return (
    <div className="space-y-4">
      <Input 
        label="Email" 
        type="email" 
        placeholder="Enter your email" 
      />
      <Input 
        label="Password" 
        type="password" 
        placeholder="Enter your password" 
      />
    </div>
  )
}`}
              />
            </div>

            {/* Variants */}
            <div>
              <h3 className="text-lg font-medium mb-4">Variants</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Default</label>
                    <input
                      placeholder="Default variant"
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Filled</label>
                    <input
                      placeholder="Filled variant"
                      className="flex w-full rounded-lg border border-transparent bg-muted hover:bg-muted/80 focus-visible:bg-background focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Outline</label>
                    <input
                      placeholder="Outline variant"
                      className="flex w-full rounded-lg border-2 border-border bg-transparent hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Ghost</label>
                    <input
                      placeholder="Ghost variant"
                      className="flex w-full rounded-lg border border-transparent bg-transparent hover:bg-muted/50 focus-visible:bg-muted focus-visible:border-border h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Input variant="default" placeholder="Default variant" />
<Input variant="filled" placeholder="Filled variant" />
<Input variant="outline" placeholder="Outline variant" />
<Input variant="ghost" placeholder="Ghost variant" />`}
              />
            </div>

            {/* Sizes */}
            <div>
              <h3 className="text-lg font-medium mb-4">Sizes</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Small</label>
                    <input
                      placeholder="Small input"
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-8 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Medium</label>
                    <input
                      placeholder="Medium input"
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Large</label>
                    <input
                      placeholder="Large input"
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-12 px-4 text-base transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Input size="sm" placeholder="Small input" />
<Input size="md" placeholder="Medium input" />
<Input size="lg" placeholder="Large input" />`}
              />
            </div>

            {/* With Icons */}
            <div>
              <h3 className="text-lg font-medium mb-4">With Icons</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Search</label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <input
                        placeholder="Search..."
                        className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 pl-10 pr-3 text-sm transition-all duration-200 focus-visible:outline-none"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 pl-10 pr-3 text-sm transition-all duration-200 focus-visible:outline-none"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Phone</label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <input
                        type="tel"
                        placeholder="+****************"
                        className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 pl-10 pr-3 text-sm transition-all duration-200 focus-visible:outline-none"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Search, Mail, Phone } from 'lucide-react'

<Input leftIcon={<Search />} placeholder="Search..." />
<Input leftIcon={<Mail />} type="email" placeholder="<EMAIL>" />
<Input leftIcon={<Phone />} type="tel" placeholder="+****************" />`}
              />
            </div>

            {/* States */}
            <div>
              <h3 className="text-lg font-medium mb-4">States</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Default</label>
                    <input
                      placeholder="Default state"
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Error</label>
                    <input
                      placeholder="Error state"
                      className="flex w-full rounded-lg border border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20 bg-background focus-visible:ring-2 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                    <p className="text-sm text-red-600">This field is required</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Success</label>
                    <input
                      placeholder="Success state"
                      value="<EMAIL>"
                      className="flex w-full rounded-lg border border-green-500 focus-visible:border-green-500 focus-visible:ring-green-500/20 bg-background focus-visible:ring-2 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                    <p className="text-sm text-green-600">Email is valid</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Warning</label>
                    <input
                      placeholder="Warning state"
                      className="flex w-full rounded-lg border border-yellow-500 focus-visible:border-yellow-500 focus-visible:ring-yellow-500/20 bg-background focus-visible:ring-2 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                    />
                    <p className="text-sm text-yellow-600">Password should be stronger</p>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Input placeholder="Default state" />
<Input 
  state="error" 
  placeholder="Error state" 
  error="This field is required" 
/>
<Input 
  state="success" 
  placeholder="Success state" 
  success="Email is valid" 
/>
<Input 
  state="warning" 
  placeholder="Warning state" 
  warning="Password should be stronger" 
/>`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="legalyard-card overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">Prop</th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Default</th>
                  <th className="text-left p-4 font-medium">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">variant</td>
                  <td className="p-4 text-sm">'default' | 'filled' | 'outline' | 'ghost'</td>
                  <td className="p-4 text-sm">'default'</td>
                  <td className="p-4 text-sm">The visual style variant of the input</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">size</td>
                  <td className="p-4 text-sm">'sm' | 'md' | 'lg'</td>
                  <td className="p-4 text-sm">'md'</td>
                  <td className="p-4 text-sm">The size of the input</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">state</td>
                  <td className="p-4 text-sm">'default' | 'error' | 'success' | 'warning'</td>
                  <td className="p-4 text-sm">'default'</td>
                  <td className="p-4 text-sm">The validation state of the input</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">label</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Label text for the input</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">error</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Error message to display</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">leftIcon</td>
                  <td className="p-4 text-sm">ReactNode</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Icon to display on the left side</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">rightIcon</td>
                  <td className="p-4 text-sm">ReactNode</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Icon to display on the right side</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">clearable</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether to show a clear button</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Do</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use clear, descriptive labels</li>
                <li>• Provide helpful placeholder text</li>
                <li>• Show validation feedback immediately</li>
                <li>• Use appropriate input types (email, tel, etc.)</li>
                <li>• Group related inputs logically</li>
                <li>• Make required fields obvious</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Don't</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use placeholder text as labels</li>
                <li>• Make inputs too small for touch devices</li>
                <li>• Show errors before user interaction</li>
                <li>• Use vague error messages</li>
                <li>• Disable copy/paste functionality</li>
                <li>• Use too many required fields</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
