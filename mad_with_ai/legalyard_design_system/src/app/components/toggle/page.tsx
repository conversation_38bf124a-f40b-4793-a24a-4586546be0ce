import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { Moon, Sun, Bell, Wifi, Bluetooth, Volume2 } from "lucide-react"
import { ToggleInput } from "@/toolkit/ui/input"

export default function TogglePage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Toggle</h1>
          <p className="text-xl text-muted-foreground">
            Switch component for binary choices with smooth animations and customizable styling options.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Toggle */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Toggle</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Email Notifications</label>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 data-[checked]:bg-primary">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 data-[checked]:translate-x-6" />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Push Notifications</label>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-muted-foreground">SMS Notifications</label>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 opacity-50 cursor-not-allowed">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1" />
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { ToggleInput } from '@legalyard/design-system'

function BasicToggle() {
  const [emailNotifications, setEmailNotifications] = useState(false)
  const [pushNotifications, setPushNotifications] = useState(true)

  return (
    <div className="space-y-4">
      <ToggleInput
        label="Email Notifications"
        checked={emailNotifications}
        onChange={setEmailNotifications}
      />
      
      <ToggleInput
        label="Push Notifications"
        checked={pushNotifications}
        onChange={setPushNotifications}
      />
      
      <ToggleInput
        label="SMS Notifications"
        disabled
        checked={false}
      />
    </div>
  )
}`}
              />
            </div>

            {/* Sizes */}
            <div>
              <h3 className="text-lg font-medium mb-4">Sizes</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="flex items-center justify-between">
                    <label className="text-xs font-medium">Small Toggle</label>
                    <div className="relative inline-flex h-4 w-7 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                      <span className="inline-block h-3 w-3 transform rounded-full bg-white transition-transform translate-x-4" />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Medium Toggle</label>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-base font-medium">Large Toggle</label>
                    <div className="relative inline-flex h-8 w-14 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                      <span className="inline-block h-6 w-6 transform rounded-full bg-white transition-transform translate-x-7" />
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<ToggleInput size="sm" label="Small Toggle" checked />
<ToggleInput size="md" label="Medium Toggle" checked />
<ToggleInput size="lg" label="Large Toggle" checked />`}
              />
            </div>

            {/* With Icons */}
            <div>
              <h3 className="text-lg font-medium mb-4">With Icons</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Moon className="h-4 w-4 text-muted-foreground" />
                      <label className="text-sm font-medium">Dark Mode</label>
                    </div>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 flex items-center justify-center">
                        <Sun className="h-2 w-2 text-yellow-500" />
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Bell className="h-4 w-4 text-muted-foreground" />
                      <label className="text-sm font-medium">Notifications</label>
                    </div>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6 flex items-center justify-center">
                        <Bell className="h-2 w-2 text-primary" />
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Wifi className="h-4 w-4 text-muted-foreground" />
                      <label className="text-sm font-medium">WiFi</label>
                    </div>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Moon, Sun, Bell, Wifi } from 'lucide-react'

<ToggleInput
  label="Dark Mode"
  leftIcon={<Moon className="h-4 w-4" />}
  checkedIcon={<Moon className="h-2 w-2" />}
  uncheckedIcon={<Sun className="h-2 w-2" />}
  checked={darkMode}
  onChange={setDarkMode}
/>

<ToggleInput
  label="Notifications"
  leftIcon={<Bell className="h-4 w-4" />}
  checkedIcon={<Bell className="h-2 w-2" />}
  checked={notifications}
  onChange={setNotifications}
/>

<ToggleInput
  label="WiFi"
  leftIcon={<Wifi className="h-4 w-4" />}
  checked={wifi}
  onChange={setWifi}
/>`}
              />
            </div>

            {/* Color Variants */}
            <div>
              <h3 className="text-lg font-medium mb-4">Color Variants</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Primary</label>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Success</label>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-green-500 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Warning</label>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-yellow-500 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Error</label>
                    <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-red-500 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<ToggleInput variant="primary" label="Primary" checked />
<ToggleInput variant="success" label="Success" checked />
<ToggleInput variant="warning" label="Warning" checked />
<ToggleInput variant="error" label="Error" checked />`}
              />
            </div>

            {/* Settings Panel Example */}
            <div>
              <h3 className="text-lg font-medium mb-4">Settings Panel</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-6 max-w-md">
                  <div>
                    <h4 className="font-medium mb-3">Privacy Settings</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-sm font-medium">Profile Visibility</label>
                          <p className="text-xs text-muted-foreground">Make your profile visible to others</p>
                        </div>
                        <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                          <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-sm font-medium">Activity Status</label>
                          <p className="text-xs text-muted-foreground">Show when you're online</p>
                        </div>
                        <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                          <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1" />
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-3">Communication</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Bell className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <label className="text-sm font-medium">Push Notifications</label>
                            <p className="text-xs text-muted-foreground">Receive notifications on your device</p>
                          </div>
                        </div>
                        <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                          <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Volume2 className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <label className="text-sm font-medium">Sound Effects</label>
                            <p className="text-xs text-muted-foreground">Play sounds for interactions</p>
                          </div>
                        </div>
                        <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                          <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`function SettingsPanel() {
  const [profileVisible, setProfileVisible] = useState(true)
  const [activityStatus, setActivityStatus] = useState(false)
  const [pushNotifications, setPushNotifications] = useState(true)
  const [soundEffects, setSoundEffects] = useState(false)

  return (
    <div className="space-y-6">
      <div>
        <h4 className="font-medium mb-3">Privacy Settings</h4>
        <div className="space-y-3">
          <ToggleInput
            label="Profile Visibility"
            description="Make your profile visible to others"
            checked={profileVisible}
            onChange={setProfileVisible}
          />
          
          <ToggleInput
            label="Activity Status"
            description="Show when you're online"
            checked={activityStatus}
            onChange={setActivityStatus}
          />
        </div>
      </div>
      
      <div>
        <h4 className="font-medium mb-3">Communication</h4>
        <div className="space-y-3">
          <ToggleInput
            label="Push Notifications"
            description="Receive notifications on your device"
            leftIcon={<Bell className="h-4 w-4" />}
            checked={pushNotifications}
            onChange={setPushNotifications}
          />
          
          <ToggleInput
            label="Sound Effects"
            description="Play sounds for interactions"
            leftIcon={<Volume2 className="h-4 w-4" />}
            checked={soundEffects}
            onChange={setSoundEffects}
          />
        </div>
      </div>
    </div>
  )
}`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="legalyard-card overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">Prop</th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Default</th>
                  <th className="text-left p-4 font-medium">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">checked</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the toggle is checked</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">onChange</td>
                  <td className="p-4 text-sm">(checked: boolean) =&gt; void</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Callback when toggle state changes</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">size</td>
                  <td className="p-4 text-sm">&apos;sm&apos; | &apos;md&apos; | &apos;lg&apos;</td>
                  <td className="p-4 text-sm">&apos;md&apos;</td>
                  <td className="p-4 text-sm">The size of the toggle</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">variant</td>
                  <td className="p-4 text-sm">&apos;primary&apos; | &apos;success&apos; | &apos;warning&apos; | &apos;error&apos;</td>
                  <td className="p-4 text-sm">&apos;primary&apos;</td>
                  <td className="p-4 text-sm">The color variant of the toggle</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">disabled</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the toggle is disabled</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">label</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Label text for the toggle</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">description</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Description text below the label</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">leftIcon</td>
                  <td className="p-4 text-sm">ReactNode</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Icon to display on the left</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">checkedIcon</td>
                  <td className="p-4 text-sm">ReactNode</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Icon to show when checked</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">uncheckedIcon</td>
                  <td className="p-4 text-sm">ReactNode</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Icon to show when unchecked</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use for binary on/off states</li>
                <li>• Provide clear labels for toggle purpose</li>
                <li>• Use immediate effect (no save button needed)</li>
                <li>• Group related toggles logically</li>
                <li>• Use appropriate colors for context</li>
                <li>• Include descriptions for complex settings</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don&apos;t use for actions (use buttons instead)</li>
                <li>• Avoid unclear or ambiguous labels</li>
                <li>• Don&apos;t use for multiple choice options</li>
                <li>• Avoid requiring confirmation for toggle changes</li>
                <li>• Don&apos;t make toggles too small for touch</li>
                <li>• Avoid overusing different color variants</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
