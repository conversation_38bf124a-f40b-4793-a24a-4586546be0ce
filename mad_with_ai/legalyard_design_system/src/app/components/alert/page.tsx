import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Info, X, XCircle } from "lucide-react"

export default function AlertPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Alert</h1>
          <p className="text-xl text-muted-foreground">
            Display important messages and notifications to users with contextual styling.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Variants */}
            <div>
              <h3 className="text-lg font-medium mb-4">Variants</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  {/* Info Alert */}
                  <div className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="font-medium text-blue-900 dark:text-blue-100">Information</h4>
                      <p className="text-sm text-blue-800 dark:text-blue-200 mt-1">
                        This is an informational alert. It provides helpful context or additional information.
                      </p>
                    </div>
                  </div>

                  {/* Success Alert */}
                  <div className="flex items-start space-x-3 p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="font-medium text-green-900 dark:text-green-100">Success</h4>
                      <p className="text-sm text-green-800 dark:text-green-200 mt-1">
                        Great! Your action was completed successfully.
                      </p>
                    </div>
                  </div>

                  {/* Warning Alert */}
                  <div className="flex items-start space-x-3 p-4 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="font-medium text-yellow-900 dark:text-yellow-100">Warning</h4>
                      <p className="text-sm text-yellow-800 dark:text-yellow-200 mt-1">
                        Please review this information carefully before proceeding.
                      </p>
                    </div>
                  </div>

                  {/* Error Alert */}
                  <div className="flex items-start space-x-3 p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <XCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="font-medium text-red-900 dark:text-red-100">Error</h4>
                      <p className="text-sm text-red-800 dark:text-red-200 mt-1">
                        Something went wrong. Please try again or contact support.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Alert, AlertDescription, AlertTitle } from '@legalyard/design-system'
import { Info, CheckCircle, AlertTriangle, XCircle } from 'lucide-react'

function AlertExamples() {
  return (
    <div className="space-y-4">
      <Alert variant="info">
        <Info className="h-4 w-4" />
        <AlertTitle>Information</AlertTitle>
        <AlertDescription>
          This is an informational alert.
        </AlertDescription>
      </Alert>

      <Alert variant="success">
        <CheckCircle className="h-4 w-4" />
        <AlertTitle>Success</AlertTitle>
        <AlertDescription>
          Your action was completed successfully.
        </AlertDescription>
      </Alert>

      <Alert variant="warning">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Warning</AlertTitle>
        <AlertDescription>
          Please review this information carefully.
        </AlertDescription>
      </Alert>

      <Alert variant="error">
        <XCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Something went wrong. Please try again.
        </AlertDescription>
      </Alert>
    </div>
  )
}`}
              />
            </div>

            {/* Dismissible Alerts */}
            <div>
              <h3 className="text-lg font-medium mb-4">Dismissible Alerts</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="font-medium text-blue-900 dark:text-blue-100">New Feature Available</h4>
                      <p className="text-sm text-blue-800 dark:text-blue-200 mt-1">
                        Check out our new dashboard analytics feature. Click here to learn more.
                      </p>
                    </div>
                    <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors">
                      <X className="h-4 w-4" />
                    </button>
                  </div>

                  <div className="flex items-start space-x-3 p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="font-medium text-green-900 dark:text-green-100">Profile Updated</h4>
                      <p className="text-sm text-green-800 dark:text-green-200 mt-1">
                        Your profile information has been successfully updated.
                      </p>
                    </div>
                    <button className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 transition-colors">
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Alert, AlertDescription, AlertTitle } from '@legalyard/design-system'
import { Info, CheckCircle, X } from 'lucide-react'

function DismissibleAlert() {
  const [isVisible, setIsVisible] = useState(true)

  if (!isVisible) return null

  return (
    <Alert variant="info" dismissible onDismiss={() => setIsVisible(false)}>
      <Info className="h-4 w-4" />
      <AlertTitle>New Feature Available</AlertTitle>
      <AlertDescription>
        Check out our new dashboard analytics feature.
      </AlertDescription>
    </Alert>
  )
}`}
              />
            </div>

            {/* Compact Alerts */}
            <div>
              <h3 className="text-lg font-medium mb-4">Compact Alerts</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2 p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <Info className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      System maintenance scheduled for tonight at 2 AM EST.
                    </p>
                  </div>

                  <div className="flex items-center space-x-2 p-3 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
                    <p className="text-sm text-yellow-800 dark:text-yellow-200">
                      Your session will expire in 5 minutes.
                    </p>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Alert variant="info" size="compact">
  <Info className="h-4 w-4" />
  <AlertDescription>
    System maintenance scheduled for tonight at 2 AM EST.
  </AlertDescription>
</Alert>

<Alert variant="warning" size="compact">
  <AlertTriangle className="h-4 w-4" />
  <AlertDescription>
    Your session will expire in 5 minutes.
  </AlertDescription>
</Alert>`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Alert</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Prop</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Default</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">variant</td>
                      <td className="p-4 text-sm">'info' | 'success' | 'warning' | 'error'</td>
                      <td className="p-4 text-sm">'info'</td>
                      <td className="p-4 text-sm">The visual style variant of the alert</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">size</td>
                      <td className="p-4 text-sm">'default' | 'compact'</td>
                      <td className="p-4 text-sm">'default'</td>
                      <td className="p-4 text-sm">The size of the alert</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">dismissible</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">false</td>
                      <td className="p-4 text-sm">Whether the alert can be dismissed</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">onDismiss</td>
                      <td className="p-4 text-sm">() => void</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">Callback when alert is dismissed</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Sub-components</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Component</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">AlertTitle</td>
                      <td className="p-4 text-sm">Title/heading for the alert</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">AlertDescription</td>
                      <td className="p-4 text-sm">Main content/description of the alert</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use appropriate variants for different message types</li>
                <li>• Keep alert messages concise and actionable</li>
                <li>• Include relevant icons to improve recognition</li>
                <li>• Make dismissible alerts for non-critical information</li>
                <li>• Position alerts where users expect to see them</li>
                <li>• Use consistent language and tone</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't overuse alerts - they lose impact</li>
                <li>• Avoid vague or unclear messages</li>
                <li>• Don't use error alerts for non-error states</li>
                <li>• Avoid making critical alerts dismissible</li>
                <li>• Don't stack too many alerts at once</li>
                <li>• Avoid alerts that interrupt user workflow</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
