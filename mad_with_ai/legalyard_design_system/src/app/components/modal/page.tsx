import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, Info } from "lucide-react"

export default function ModalPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Modal</h1>
          <p className="text-xl text-muted-foreground">
            An overlay dialog component for displaying important content that requires user attention.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Modal */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Modal</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4">
                    Open Modal
                  </button>
                  
                  {/* Modal Preview */}
                  <div className="relative border border-border rounded-lg p-4 bg-muted/30">
                    <div className="text-xs text-muted-foreground mb-2">Modal Preview:</div>
                    <div className="bg-background border border-border rounded-lg shadow-lg max-w-md mx-auto">
                      <div className="flex items-center justify-between p-6 border-b border-border">
                        <h3 className="text-lg font-semibold">Modal Title</h3>
                        <button className="text-muted-foreground hover:text-foreground">
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                      <div className="p-6">
                        <p className="text-sm text-muted-foreground mb-4">
                          This is the modal content. You can put any content here including forms, images, or other components.
                        </p>
                        <div className="flex justify-end space-x-2">
                          <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-9 px-4">
                            Cancel
                          </button>
                          <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-9 px-4">
                            Confirm
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Modal, ModalContent, ModalHeader, ModalTitle, ModalBody, ModalFooter } from '@legalyard/design-system'

function BasicModal() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        Open Modal
      </Button>
      
      <Modal open={isOpen} onOpenChange={setIsOpen}>
        <ModalContent>
          <ModalHeader>
            <ModalTitle>Modal Title</ModalTitle>
          </ModalHeader>
          <ModalBody>
            <p>This is the modal content. You can put any content here.</p>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setIsOpen(false)}>
              Confirm
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}`}
              />
            </div>

            {/* Confirmation Modal */}
            <div>
              <h3 className="text-lg font-medium mb-4">Confirmation Modal</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="flex gap-2">
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-red-600 to-rose-500 text-white hover:from-red-700 hover:to-rose-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-9 px-4">
                      Delete Item
                    </button>
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-green-600 to-emerald-500 text-white hover:from-green-700 hover:to-emerald-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-9 px-4">
                      Save Changes
                    </button>
                  </div>
                  
                  {/* Confirmation Modal Preview */}
                  <div className="relative border border-border rounded-lg p-4 bg-muted/30">
                    <div className="text-xs text-muted-foreground mb-2">Confirmation Modal Preview:</div>
                    <div className="bg-background border border-border rounded-lg shadow-lg max-w-sm mx-auto">
                      <div className="p-6">
                        <div className="flex items-center space-x-3 mb-4">
                          <div className="flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold">Delete Item</h3>
                            <p className="text-sm text-muted-foreground">This action cannot be undone.</p>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mb-6">
                          Are you sure you want to delete this item? This will permanently remove it from your account.
                        </p>
                        <div className="flex justify-end space-x-2">
                          <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-9 px-4">
                            Cancel
                          </button>
                          <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-red-600 to-rose-500 text-white hover:from-red-700 hover:to-rose-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-9 px-4">
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Modal, ModalContent, ModalBody } from '@legalyard/design-system'
import { AlertTriangle } from 'lucide-react'

function ConfirmationModal() {
  const [isOpen, setIsOpen] = useState(false)

  const handleDelete = () => {
    // Perform delete action
    setIsOpen(false)
  }

  return (
    <>
      <Button variant="error" onClick={() => setIsOpen(true)}>
        Delete Item
      </Button>
      
      <Modal open={isOpen} onOpenChange={setIsOpen}>
        <ModalContent className="max-w-sm">
          <ModalBody>
            <div className="flex items-center space-x-3 mb-4">
              <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Delete Item</h3>
                <p className="text-sm text-muted-foreground">This action cannot be undone.</p>
              </div>
            </div>
            <p className="text-sm text-muted-foreground mb-6">
              Are you sure you want to delete this item?
            </p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button variant="error" onClick={handleDelete}>
                Delete
              </Button>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  )
}`}
              />
            </div>

            {/* Form Modal */}
            <div>
              <h3 className="text-lg font-medium mb-4">Form Modal</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4">
                    Add User
                  </button>
                  
                  {/* Form Modal Preview */}
                  <div className="relative border border-border rounded-lg p-4 bg-muted/30">
                    <div className="text-xs text-muted-foreground mb-2">Form Modal Preview:</div>
                    <div className="bg-background border border-border rounded-lg shadow-lg max-w-md mx-auto">
                      <div className="flex items-center justify-between p-6 border-b border-border">
                        <h3 className="text-lg font-semibold">Add New User</h3>
                        <button className="text-muted-foreground hover:text-foreground">
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                      <div className="p-6 space-y-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Name</label>
                          <input
                            type="text"
                            placeholder="Enter full name"
                            className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Email</label>
                          <input
                            type="email"
                            placeholder="Enter email address"
                            className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Role</label>
                          <select className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                            <option>Select role</option>
                            <option>Admin</option>
                            <option>User</option>
                            <option>Viewer</option>
                          </select>
                        </div>
                      </div>
                      <div className="flex justify-end space-x-2 p-6 border-t border-border">
                        <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-9 px-4">
                          Cancel
                        </button>
                        <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-9 px-4">
                          Add User
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Modal, ModalContent, ModalHeader, ModalTitle, ModalBody, ModalFooter } from '@legalyard/design-system'

function FormModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form data:', formData)
    setIsOpen(false)
  }

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        Add User
      </Button>
      
      <Modal open={isOpen} onOpenChange={setIsOpen}>
        <ModalContent>
          <ModalHeader>
            <ModalTitle>Add New User</ModalTitle>
          </ModalHeader>
          <form onSubmit={handleSubmit}>
            <ModalBody className="space-y-4">
              <Input
                label="Name"
                placeholder="Enter full name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
              <Input
                label="Email"
                type="email"
                placeholder="Enter email address"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                required
              />
              <Select
                label="Role"
                value={formData.role}
                onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="viewer">Viewer</SelectItem>
                </SelectContent>
              </Select>
            </ModalBody>
            <ModalFooter>
              <Button variant="outline" type="button" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Add User
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </>
  )
}`}
              />
            </div>

            {/* Modal Sizes */}
            <div>
              <h3 className="text-lg font-medium mb-4">Modal Sizes</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div className="flex gap-2 flex-wrap">
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-9 px-4">
                      Small Modal
                    </button>
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-9 px-4">
                      Medium Modal
                    </button>
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-9 px-4">
                      Large Modal
                    </button>
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-9 px-4">
                      Full Screen
                    </button>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Modal size="sm">
  <ModalContent>
    <ModalBody>Small modal content</ModalBody>
  </ModalContent>
</Modal>

<Modal size="md">
  <ModalContent>
    <ModalBody>Medium modal content</ModalBody>
  </ModalContent>
</Modal>

<Modal size="lg">
  <ModalContent>
    <ModalBody>Large modal content</ModalBody>
  </ModalContent>
</Modal>

<Modal size="full">
  <ModalContent>
    <ModalBody>Full screen modal content</ModalBody>
  </ModalContent>
</Modal>`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Modal</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Prop</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Default</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">open</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">false</td>
                      <td className="p-4 text-sm">Whether the modal is open</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">onOpenChange</td>
                      <td className="p-4 text-sm">(open: boolean) => void</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">Callback when open state changes</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">size</td>
                      <td className="p-4 text-sm">'sm' | 'md' | 'lg' | 'xl' | 'full'</td>
                      <td className="p-4 text-sm">'md'</td>
                      <td className="p-4 text-sm">The size of the modal</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">closeOnOverlayClick</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">true</td>
                      <td className="p-4 text-sm">Whether clicking overlay closes modal</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">closeOnEscape</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">true</td>
                      <td className="p-4 text-sm">Whether pressing Escape closes modal</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Sub-components</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Component</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">ModalContent</td>
                      <td className="p-4 text-sm">Main container for modal content</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">ModalHeader</td>
                      <td className="p-4 text-sm">Header section with title and close button</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">ModalTitle</td>
                      <td className="p-4 text-sm">Title text for the modal</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">ModalBody</td>
                      <td className="p-4 text-sm">Main content area of the modal</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">ModalFooter</td>
                      <td className="p-4 text-sm">Footer section for actions</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use modals for important actions that need focus</li>
                <li>• Keep modal content concise and focused</li>
                <li>• Provide clear action buttons</li>
                <li>• Include a close button or escape mechanism</li>
                <li>• Use appropriate modal sizes for content</li>
                <li>• Handle keyboard navigation properly</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't overuse modals for simple actions</li>
                <li>• Avoid stacking multiple modals</li>
                <li>• Don't make modals too large or complex</li>
                <li>• Avoid unclear or confusing action buttons</li>
                <li>• Don't trap users without escape options</li>
                <li>• Avoid modals for non-critical information</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
