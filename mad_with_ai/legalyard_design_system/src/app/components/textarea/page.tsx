import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { MessageSquare, FileText, Edit3 } from "lucide-react"
import { TextareaInput } from "@/toolkit/ui/input"

export default function TextareaPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Textarea</h1>
          <p className="text-xl text-muted-foreground">
            Multi-line text input component for longer content with auto-resize, character counting, and validation.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Textarea */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Textarea</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Description</label>
                    <textarea
                      placeholder="Enter your description..."
                      rows={4}
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Comments</label>
                    <textarea
                      placeholder="Add your comments..."
                      rows={3}
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                    />
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { TextareaInput } from '@legalyard/design-system'

function BasicTextarea() {
  const [description, setDescription] = useState('')
  const [comments, setComments] = useState('')

  return (
    <div className="space-y-4">
      <TextareaInput
        label="Description"
        placeholder="Enter your description..."
        value={description}
        onChange={setDescription}
        rows={4}
      />
      
      <TextareaInput
        label="Comments"
        placeholder="Add your comments..."
        value={comments}
        onChange={setComments}
        rows={3}
      />
    </div>
  )
}`}
              />
            </div>

            {/* Auto-resize Textarea */}
            <div>
              <h3 className="text-lg font-medium mb-4">Auto-resize</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Auto-expanding Message</label>
                    <textarea
                      placeholder="Start typing and watch this expand..."
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none min-h-[80px] max-h-[200px]"
                      style={{ height: 'auto' }}
                    />
                    <p className="text-xs text-muted-foreground">This textarea automatically adjusts its height</p>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<TextareaInput
  label="Auto-expanding Message"
  placeholder="Start typing and watch this expand..."
  autoResize
  minRows={3}
  maxRows={8}
  value={message}
  onChange={setMessage}
/>`}
              />
            </div>

            {/* Character Count */}
            <div>
              <h3 className="text-lg font-medium mb-4">Character Count</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Tweet</label>
                    <textarea
                      placeholder="What's happening?"
                      rows={3}
                      maxLength={280}
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                    />
                    <div className="flex justify-between items-center">
                      <p className="text-xs text-muted-foreground">Share your thoughts</p>
                      <span className="text-xs text-muted-foreground">0/280</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Bio</label>
                    <textarea
                      placeholder="Tell us about yourself..."
                      rows={4}
                      maxLength={160}
                      className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                    />
                    <div className="flex justify-between items-center">
                      <p className="text-xs text-muted-foreground">Keep it concise</p>
                      <span className="text-xs text-muted-foreground">0/160</span>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<TextareaInput
  label="Tweet"
  placeholder="What's happening?"
  value={tweet}
  onChange={setTweet}
  maxLength={280}
  showCharacterCount
  helperText="Share your thoughts"
/>

<TextareaInput
  label="Bio"
  placeholder="Tell us about yourself..."
  value={bio}
  onChange={setBio}
  maxLength={160}
  showCharacterCount
  helperText="Keep it concise"
  rows={4}
/>`}
              />
            </div>

            {/* With Icons */}
            <div>
              <h3 className="text-lg font-medium mb-4">With Icons</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Message</label>
                    <div className="relative">
                      <MessageSquare className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <textarea
                        placeholder="Type your message..."
                        rows={3}
                        className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 pl-10 pr-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Article Content</label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <textarea
                        placeholder="Write your article..."
                        rows={5}
                        className="flex w-full rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 pl-10 pr-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { MessageSquare, FileText } from 'lucide-react'

<TextareaInput
  label="Message"
  placeholder="Type your message..."
  leftIcon={<MessageSquare className="h-4 w-4" />}
  value={message}
  onChange={setMessage}
  rows={3}
/>

<TextareaInput
  label="Article Content"
  placeholder="Write your article..."
  leftIcon={<FileText className="h-4 w-4" />}
  value={content}
  onChange={setContent}
  rows={5}
/>`}
              />
            </div>

            {/* Validation States */}
            <div>
              <h3 className="text-lg font-medium mb-4">Validation States</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Success State</label>
                    <textarea
                      placeholder="This looks good!"
                      rows={3}
                      className="flex w-full rounded-lg border border-green-500 bg-background hover:border-green-600 focus-visible:border-green-500 focus-visible:ring-2 focus-visible:ring-green-500/20 px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                    />
                    <p className="text-sm text-green-600">Great! Your input is valid.</p>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Error State</label>
                    <textarea
                      placeholder="This field has an error"
                      rows={3}
                      className="flex w-full rounded-lg border border-red-500 bg-background hover:border-red-600 focus-visible:border-red-500 focus-visible:ring-2 focus-visible:ring-red-500/20 px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                    />
                    <p className="text-sm text-red-600">This field is required and must be at least 10 characters.</p>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-muted-foreground">Disabled State</label>
                    <textarea
                      placeholder="This field is disabled"
                      rows={3}
                      disabled
                      className="flex w-full rounded-lg border border-border bg-muted text-muted-foreground px-3 py-2 text-sm transition-all duration-200 resize-none cursor-not-allowed"
                    />
                    <p className="text-sm text-muted-foreground">This field cannot be edited.</p>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<TextareaInput
  label="Success State"
  placeholder="This looks good!"
  value={validInput}
  onChange={setValidInput}
  success="Great! Your input is valid."
  rows={3}
/>

<TextareaInput
  label="Error State"
  placeholder="This field has an error"
  value={invalidInput}
  onChange={setInvalidInput}
  error="This field is required and must be at least 10 characters."
  rows={3}
/>

<TextareaInput
  label="Disabled State"
  placeholder="This field is disabled"
  disabled
  helperText="This field cannot be edited."
  rows={3}
/>`}
              />
            </div>

            {/* Rich Text Features */}
            <div>
              <h3 className="text-lg font-medium mb-4">Rich Text Features</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Code Editor</label>
                    <textarea
                      placeholder="// Write your code here..."
                      rows={6}
                      className="flex w-full rounded-lg border border-border bg-slate-50 dark:bg-slate-900 hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 px-3 py-2 text-sm font-mono transition-all duration-200 focus-visible:outline-none resize-none"
                    />
                    <p className="text-xs text-muted-foreground">Monospace font for code editing</p>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Markdown Editor</label>
                    <div className="border border-border rounded-lg">
                      <div className="flex items-center space-x-2 p-2 border-b border-border bg-muted/50">
                        <button className="p-1 rounded hover:bg-background">
                          <strong className="text-xs">B</strong>
                        </button>
                        <button className="p-1 rounded hover:bg-background">
                          <em className="text-xs">I</em>
                        </button>
                        <button className="p-1 rounded hover:bg-background">
                          <span className="text-xs">Link</span>
                        </button>
                      </div>
                      <textarea
                        placeholder="Write in **markdown**..."
                        rows={4}
                        className="w-full border-0 bg-background px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none resize-none"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<TextareaInput
  label="Code Editor"
  placeholder="// Write your code here..."
  value={code}
  onChange={setCode}
  fontFamily="mono"
  backgroundColor="slate"
  rows={6}
  helperText="Monospace font for code editing"
/>

<TextareaInput
  label="Markdown Editor"
  placeholder="Write in **markdown**..."
  value={markdown}
  onChange={setMarkdown}
  toolbar={[
    { type: 'bold', label: 'B' },
    { type: 'italic', label: 'I' },
    { type: 'link', label: 'Link' }
  ]}
  rows={4}
/>`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="legalyard-card overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">Prop</th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Default</th>
                  <th className="text-left p-4 font-medium">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">value</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">The textarea value</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">onChange</td>
                  <td className="p-4 text-sm">(value: string) =&gt; void</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Callback when value changes</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">rows</td>
                  <td className="p-4 text-sm">number</td>
                  <td className="p-4 text-sm">3</td>
                  <td className="p-4 text-sm">Number of visible text lines</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">autoResize</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Auto-resize based on content</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">minRows</td>
                  <td className="p-4 text-sm">number</td>
                  <td className="p-4 text-sm">1</td>
                  <td className="p-4 text-sm">Minimum rows when auto-resizing</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">maxRows</td>
                  <td className="p-4 text-sm">number</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Maximum rows when auto-resizing</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">maxLength</td>
                  <td className="p-4 text-sm">number</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Maximum character length</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">showCharacterCount</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Show character count</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">leftIcon</td>
                  <td className="p-4 text-sm">ReactNode</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Icon to display on the left</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">error</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Error message to display</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use for multi-line text input</li>
                <li>• Provide clear labels and placeholders</li>
                <li>• Set appropriate initial height with rows</li>
                <li>• Use character limits for constrained content</li>
                <li>• Enable auto-resize for dynamic content</li>
                <li>• Show character count for limited fields</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don&apos;t use for single-line input (use Input)</li>
                <li>• Avoid making textarea too small for content</li>
                <li>• Don&apos;t forget to handle validation</li>
                <li>• Avoid unclear placeholder text</li>
                <li>• Don&apos;t disable resize without good reason</li>
                <li>• Avoid excessive character limits</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
