import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { ChevronDown, Check, Search } from "lucide-react"

export default function SelectPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Select</h1>
          <p className="text-xl text-muted-foreground">
            A dropdown selection component with search functionality and customizable options.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Usage */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Usage</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Country</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span className="text-muted-foreground">Select a country</span>
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Language</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span>English</span>
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@legalyard/design-system'

function Example() {
  return (
    <div className="space-y-4">
      <Select>
        <SelectTrigger>
          <SelectValue placeholder="Select a country" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="us">United States</SelectItem>
          <SelectItem value="ca">Canada</SelectItem>
          <SelectItem value="uk">United Kingdom</SelectItem>
          <SelectItem value="de">Germany</SelectItem>
        </SelectContent>
      </Select>

      <Select defaultValue="en">
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="en">English</SelectItem>
          <SelectItem value="es">Spanish</SelectItem>
          <SelectItem value="fr">French</SelectItem>
          <SelectItem value="de">German</SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}`}
              />
            </div>

            {/* With Search */}
            <div>
              <h3 className="text-lg font-medium mb-4">With Search</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">City</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span className="text-muted-foreground">Search for a city</span>
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Select searchable>
  <SelectTrigger>
    <SelectValue placeholder="Search for a city" />
  </SelectTrigger>
  <SelectContent>
    <SelectSearch placeholder="Search cities..." />
    <SelectItem value="nyc">New York City</SelectItem>
    <SelectItem value="la">Los Angeles</SelectItem>
    <SelectItem value="chicago">Chicago</SelectItem>
    <SelectItem value="houston">Houston</SelectItem>
    <SelectItem value="phoenix">Phoenix</SelectItem>
  </SelectContent>
</Select>`}
              />
            </div>

            {/* Multiple Selection */}
            <div>
              <h3 className="text-lg font-medium mb-4">Multiple Selection</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Skills</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 min-h-10 px-3 py-2 text-sm transition-all duration-200 focus-visible:outline-none">
                        <div className="flex flex-wrap gap-1">
                          <span className="inline-flex items-center rounded-md bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                            React
                            <button className="ml-1 text-primary/60 hover:text-primary">×</button>
                          </span>
                          <span className="inline-flex items-center rounded-md bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                            TypeScript
                            <button className="ml-1 text-primary/60 hover:text-primary">×</button>
                          </span>
                        </div>
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Select multiple>
  <SelectTrigger>
    <SelectValue placeholder="Select skills" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="react">React</SelectItem>
    <SelectItem value="typescript">TypeScript</SelectItem>
    <SelectItem value="nextjs">Next.js</SelectItem>
    <SelectItem value="nodejs">Node.js</SelectItem>
    <SelectItem value="python">Python</SelectItem>
  </SelectContent>
</Select>`}
              />
            </div>

            {/* Grouped Options */}
            <div>
              <h3 className="text-lg font-medium mb-4">Grouped Options</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium leading-none">Framework</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span className="text-muted-foreground">Select a framework</span>
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Select>
  <SelectTrigger>
    <SelectValue placeholder="Select a framework" />
  </SelectTrigger>
  <SelectContent>
    <SelectGroup>
      <SelectLabel>Frontend</SelectLabel>
      <SelectItem value="react">React</SelectItem>
      <SelectItem value="vue">Vue.js</SelectItem>
      <SelectItem value="angular">Angular</SelectItem>
    </SelectGroup>
    <SelectGroup>
      <SelectLabel>Backend</SelectLabel>
      <SelectItem value="nodejs">Node.js</SelectItem>
      <SelectItem value="python">Python</SelectItem>
      <SelectItem value="java">Java</SelectItem>
    </SelectGroup>
  </SelectContent>
</Select>`}
              />
            </div>

            {/* States */}
            <div>
              <h3 className="text-lg font-medium mb-4">States</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Default</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span className="text-muted-foreground">Select an option</span>
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Error</label>
                    <div className="relative">
                      <button className="flex w-full items-center justify-between rounded-lg border border-red-500 bg-background focus-visible:border-red-500 focus-visible:ring-2 focus-visible:ring-red-500/20 h-10 px-3 text-sm transition-all duration-200 focus-visible:outline-none">
                        <span className="text-muted-foreground">Select an option</span>
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                    <p className="text-sm text-red-600">Please select an option</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Disabled</label>
                    <div className="relative">
                      <button disabled className="flex w-full items-center justify-between rounded-lg border border-border bg-muted cursor-not-allowed opacity-50 h-10 px-3 text-sm">
                        <span className="text-muted-foreground">Disabled select</span>
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Select>
  <SelectTrigger>
    <SelectValue placeholder="Select an option" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>

<Select error="Please select an option">
  <SelectTrigger>
    <SelectValue placeholder="Select an option" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>

<Select disabled>
  <SelectTrigger>
    <SelectValue placeholder="Disabled select" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
  </SelectContent>
</Select>`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Select</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Prop</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Default</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">value</td>
                      <td className="p-4 text-sm">string | string[]</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">The controlled value of the select</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">defaultValue</td>
                      <td className="p-4 text-sm">string | string[]</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">The default value of the select</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">multiple</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">false</td>
                      <td className="p-4 text-sm">Whether multiple selection is allowed</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">searchable</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">false</td>
                      <td className="p-4 text-sm">Whether the select is searchable</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">disabled</td>
                      <td className="p-4 text-sm">boolean</td>
                      <td className="p-4 text-sm">false</td>
                      <td className="p-4 text-sm">Whether the select is disabled</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">error</td>
                      <td className="p-4 text-sm">string</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">Error message to display</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">onValueChange</td>
                      <td className="p-4 text-sm">(value: string | string[]) => void</td>
                      <td className="p-4 text-sm">-</td>
                      <td className="p-4 text-sm">Callback when value changes</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Sub-components</h3>
              <div className="legalyard-card overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="text-left p-4 font-medium">Component</th>
                      <th className="text-left p-4 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">SelectTrigger</td>
                      <td className="p-4 text-sm">The button that opens the select dropdown</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">SelectValue</td>
                      <td className="p-4 text-sm">Displays the selected value or placeholder</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">SelectContent</td>
                      <td className="p-4 text-sm">Container for the dropdown options</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">SelectItem</td>
                      <td className="p-4 text-sm">Individual selectable option</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">SelectGroup</td>
                      <td className="p-4 text-sm">Groups related options together</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">SelectLabel</td>
                      <td className="p-4 text-sm">Label for a group of options</td>
                    </tr>
                    <tr className="border-t border-border">
                      <td className="p-4 font-mono text-sm">SelectSearch</td>
                      <td className="p-4 text-sm">Search input for filtering options</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use clear, descriptive option labels</li>
                <li>• Group related options logically</li>
                <li>• Provide search for long option lists</li>
                <li>• Use appropriate placeholder text</li>
                <li>• Show validation feedback clearly</li>
                <li>• Consider default selections for common choices</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't use select for 2-3 options (use radio instead)</li>
                <li>• Avoid very long option lists without search</li>
                <li>• Don't use vague option labels</li>
                <li>• Avoid auto-selecting first option</li>
                <li>• Don't make required selects without clear indication</li>
                <li>• Avoid nested dropdowns</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
