import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { CreditCard, Truck, Plane, Car, Home, Building } from "lucide-react"
import { RadioInput } from "@/toolkit/ui/input"

export default function RadioPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Radio</h1>
          <p className="text-xl text-muted-foreground">
            Radio button component for mutually exclusive selections with customizable styling and grouping options.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Radio */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Radio Group</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div>
                    <label className="text-sm font-medium mb-3 block">Preferred Contact Method</label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="contact"
                            value="email"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                            defaultChecked
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-0 peer-checked:opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <label className="text-sm">Email</label>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="contact"
                            value="phone"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-0 peer-checked:opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <label className="text-sm">Phone</label>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="contact"
                            value="sms"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-0 peer-checked:opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <label className="text-sm">SMS</label>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-3 block">Subscription Plan</label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="plan"
                            value="basic"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-0 peer-checked:opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Basic Plan</label>
                          <p className="text-xs text-muted-foreground">$9/month - Essential features</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="plan"
                            value="pro"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                            defaultChecked
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Pro Plan</label>
                          <p className="text-xs text-muted-foreground">$19/month - Advanced features</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="plan"
                            value="enterprise"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-0 peer-checked:opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Enterprise Plan</label>
                          <p className="text-xs text-muted-foreground">$49/month - All features + support</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { RadioInput } from '@legalyard/design-system'

function BasicRadio() {
  const [contactMethod, setContactMethod] = useState('email')
  const [plan, setPlan] = useState('pro')

  const contactOptions = [
    { value: 'email', label: 'Email' },
    { value: 'phone', label: 'Phone' },
    { value: 'sms', label: 'SMS' }
  ]

  const planOptions = [
    { 
      value: 'basic', 
      label: 'Basic Plan',
      description: '$9/month - Essential features'
    },
    { 
      value: 'pro', 
      label: 'Pro Plan',
      description: '$19/month - Advanced features'
    },
    { 
      value: 'enterprise', 
      label: 'Enterprise Plan',
      description: '$49/month - All features + support'
    }
  ]

  return (
    <div className="space-y-6">
      <RadioInput
        label="Preferred Contact Method"
        name="contact"
        value={contactMethod}
        onChange={setContactMethod}
        options={contactOptions}
      />
      
      <RadioInput
        label="Subscription Plan"
        name="plan"
        value={plan}
        onChange={setPlan}
        options={planOptions}
        showDescription
      />
    </div>
  )
}`}
              />
            </div>

            {/* Card Style Radio */}
            <div>
              <h3 className="text-lg font-medium mb-4">Card Style</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-2xl">
                  <label className="text-sm font-medium mb-3 block">Payment Method</label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div className="relative">
                      <input
                        type="radio"
                        name="payment"
                        value="card"
                        className="peer sr-only"
                        defaultChecked
                      />
                      <div className="p-4 border-2 border-border rounded-lg cursor-pointer transition-all peer-checked:border-primary peer-checked:bg-primary/5 hover:border-primary/50">
                        <div className="flex items-center space-x-3">
                          <CreditCard className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <div className="font-medium text-sm">Credit Card</div>
                            <div className="text-xs text-muted-foreground">Visa, Mastercard</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="relative">
                      <input
                        type="radio"
                        name="payment"
                        value="paypal"
                        className="peer sr-only"
                      />
                      <div className="p-4 border-2 border-border rounded-lg cursor-pointer transition-all peer-checked:border-primary peer-checked:bg-primary/5 hover:border-primary/50">
                        <div className="flex items-center space-x-3">
                          <div className="w-5 h-5 bg-blue-600 rounded flex items-center justify-center">
                            <span className="text-white text-xs font-bold">P</span>
                          </div>
                          <div>
                            <div className="font-medium text-sm">PayPal</div>
                            <div className="text-xs text-muted-foreground">Digital wallet</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="relative">
                      <input
                        type="radio"
                        name="payment"
                        value="bank"
                        className="peer sr-only"
                      />
                      <div className="p-4 border-2 border-border rounded-lg cursor-pointer transition-all peer-checked:border-primary peer-checked:bg-primary/5 hover:border-primary/50">
                        <div className="flex items-center space-x-3">
                          <Building className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <div className="font-medium text-sm">Bank Transfer</div>
                            <div className="text-xs text-muted-foreground">Direct transfer</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`const paymentOptions = [
  { 
    value: 'card', 
    label: 'Credit Card',
    description: 'Visa, Mastercard',
    icon: <CreditCard className="h-5 w-5" />
  },
  { 
    value: 'paypal', 
    label: 'PayPal',
    description: 'Digital wallet',
    icon: <div className="w-5 h-5 bg-blue-600 rounded flex items-center justify-center">
      <span className="text-white text-xs font-bold">P</span>
    </div>
  },
  { 
    value: 'bank', 
    label: 'Bank Transfer',
    description: 'Direct transfer',
    icon: <Building className="h-5 w-5" />
  }
]

<RadioInput
  label="Payment Method"
  name="payment"
  value={paymentMethod}
  onChange={setPaymentMethod}
  options={paymentOptions}
  variant="card"
  layout="grid"
  columns={3}
/>`}
              />
            </div>

            {/* With Icons */}
            <div>
              <h3 className="text-lg font-medium mb-4">With Icons</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div>
                    <label className="text-sm font-medium mb-3 block">Delivery Method</label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="delivery"
                            value="standard"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                            defaultChecked
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <Truck className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <label className="text-sm font-medium">Standard Delivery</label>
                          <p className="text-xs text-muted-foreground">5-7 business days - Free</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="delivery"
                            value="express"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-0 peer-checked:opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <Plane className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <label className="text-sm font-medium">Express Delivery</label>
                          <p className="text-xs text-muted-foreground">1-2 business days - $9.99</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="delivery"
                            value="pickup"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-0 peer-checked:opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <Car className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <label className="text-sm font-medium">Store Pickup</label>
                          <p className="text-xs text-muted-foreground">Available today - Free</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Truck, Plane, Car } from 'lucide-react'

const deliveryOptions = [
  { 
    value: 'standard', 
    label: 'Standard Delivery',
    description: '5-7 business days - Free',
    icon: <Truck className="h-4 w-4" />
  },
  { 
    value: 'express', 
    label: 'Express Delivery',
    description: '1-2 business days - $9.99',
    icon: <Plane className="h-4 w-4" />
  },
  { 
    value: 'pickup', 
    label: 'Store Pickup',
    description: 'Available today - Free',
    icon: <Car className="h-4 w-4" />
  }
]

<RadioInput
  label="Delivery Method"
  name="delivery"
  value={deliveryMethod}
  onChange={setDeliveryMethod}
  options={deliveryOptions}
  showIcon
  showDescription
/>`}
              />
            </div>

            {/* Sizes and States */}
            <div>
              <h3 className="text-lg font-medium mb-4">Sizes and States</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-6 max-w-md">
                  <div>
                    <label className="text-xs font-medium mb-2 block">Small Size</label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <div className="relative">
                          <input
                            type="radio"
                            name="size-small"
                            value="option1"
                            className="peer h-3 w-3 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                            defaultChecked
                          />
                          <div className="absolute inset-0 rounded-full border border-primary bg-primary opacity-100 transition-opacity">
                            <div className="absolute inset-0.5 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <label className="text-xs">Small option 1</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="relative">
                          <input
                            type="radio"
                            name="size-small"
                            value="option2"
                            className="peer h-3 w-3 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                          />
                          <div className="absolute inset-0 rounded-full border border-primary bg-primary opacity-0 peer-checked:opacity-100 transition-opacity">
                            <div className="absolute inset-0.5 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <label className="text-xs">Small option 2</label>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-base font-medium mb-3 block">Large Size</label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="size-large"
                            value="option1"
                            className="peer h-5 w-5 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                            defaultChecked
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <label className="text-base">Large option 1</label>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="size-large"
                            value="option2"
                            className="peer h-5 w-5 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                          />
                          <div className="absolute inset-0 rounded-full border-2 border-primary bg-primary opacity-0 peer-checked:opacity-100 transition-opacity">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <label className="text-base">Large option 2</label>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-3 block text-muted-foreground">Disabled State</label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="radio"
                            name="disabled"
                            value="option1"
                            className="peer h-4 w-4 rounded-full border border-border text-primary focus:ring-2 focus:ring-primary focus:ring-offset-2"
                            disabled
                          />
                          <div className="absolute inset-0 rounded-full border border-muted bg-muted opacity-50">
                            <div className="absolute inset-1 rounded-full bg-white"></div>
                          </div>
                        </div>
                        <label className="text-sm text-muted-foreground">Disabled option</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<RadioInput size="sm" label="Small Size" options={options} />
<RadioInput size="lg" label="Large Size" options={options} />
<RadioInput disabled label="Disabled State" options={options} />`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="legalyard-card overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">Prop</th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Default</th>
                  <th className="text-left p-4 font-medium">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">options</td>
                  <td className="p-4 text-sm">Array&lt;{`{value: string, label: string}`}&gt;</td>
                  <td className="p-4 text-sm">[]</td>
                  <td className="p-4 text-sm">Array of radio options</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">value</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Selected value</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">onChange</td>
                  <td className="p-4 text-sm">(value: string) =&gt; void</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Callback when selection changes</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">name</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Name attribute for radio group</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">size</td>
                  <td className="p-4 text-sm">&apos;sm&apos; | &apos;md&apos; | &apos;lg&apos;</td>
                  <td className="p-4 text-sm">&apos;md&apos;</td>
                  <td className="p-4 text-sm">The size of the radio buttons</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">variant</td>
                  <td className="p-4 text-sm">&apos;default&apos; | &apos;card&apos;</td>
                  <td className="p-4 text-sm">&apos;default&apos;</td>
                  <td className="p-4 text-sm">Visual style variant</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">layout</td>
                  <td className="p-4 text-sm">&apos;vertical&apos; | &apos;horizontal&apos; | &apos;grid&apos;</td>
                  <td className="p-4 text-sm">&apos;vertical&apos;</td>
                  <td className="p-4 text-sm">Layout direction</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">disabled</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the radio group is disabled</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">showIcon</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Show icons for options</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">showDescription</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Show descriptions for options</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use for mutually exclusive choices</li>
                <li>• Provide clear, descriptive labels</li>
                <li>• Group related options logically</li>
                <li>• Use card style for complex options</li>
                <li>• Include descriptions when helpful</li>
                <li>• Ensure adequate touch targets</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don&apos;t use for multiple selections (use checkboxes)</li>
                <li>• Avoid too many options in one group</li>
                <li>• Don&apos;t use unclear or similar labels</li>
                <li>• Avoid making radio buttons too small</li>
                <li>• Don&apos;t forget to set default selection</li>
                <li>• Avoid inconsistent styling within groups</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
