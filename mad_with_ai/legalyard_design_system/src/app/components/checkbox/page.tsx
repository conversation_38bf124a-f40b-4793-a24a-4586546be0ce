import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { Check, Minus } from "lucide-react"

export default function CheckboxPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Checkbox</h1>
          <p className="text-xl text-muted-foreground">
            A checkbox input component for boolean selections with support for indeterminate states.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Usage */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Usage</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <input
                        type="checkbox"
                        className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                      />
                      <Check className="absolute inset-0 h-4 w-4 text-primary-foreground opacity-0 peer-checked:opacity-100 pointer-events-none" />
                    </div>
                    <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Accept terms and conditions
                    </label>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                      />
                      <Check className="absolute inset-0 h-4 w-4 text-primary-foreground opacity-100 pointer-events-none" />
                    </div>
                    <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Subscribe to newsletter
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <input
                        type="checkbox"
                        disabled
                        className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                      />
                    </div>
                    <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Disabled option
                    </label>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Checkbox } from '@legalyard/design-system'

function Example() {
  const [accepted, setAccepted] = useState(false)
  const [subscribed, setSubscribed] = useState(true)

  return (
    <div className="space-y-4">
      <Checkbox
        checked={accepted}
        onCheckedChange={setAccepted}
        label="Accept terms and conditions"
      />
      
      <Checkbox
        checked={subscribed}
        onCheckedChange={setSubscribed}
        label="Subscribe to newsletter"
      />
      
      <Checkbox
        disabled
        label="Disabled option"
      />
    </div>
  )
}`}
              />
            </div>

            {/* Indeterminate State */}
            <div>
              <h3 className="text-lg font-medium mb-4">Indeterminate State</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <input
                        type="checkbox"
                        className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                      />
                      <Minus className="absolute inset-0 h-4 w-4 text-primary-foreground opacity-100 pointer-events-none" />
                    </div>
                    <label className="text-sm font-medium leading-none">
                      Select all items
                    </label>
                  </div>
                  
                  <div className="ml-6 space-y-2">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                        />
                        <Check className="absolute inset-0 h-4 w-4 text-primary-foreground opacity-100 pointer-events-none" />
                      </div>
                      <label className="text-sm leading-none">Item 1</label>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <input
                          type="checkbox"
                          className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                        />
                      </div>
                      <label className="text-sm leading-none">Item 2</label>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                        />
                        <Check className="absolute inset-0 h-4 w-4 text-primary-foreground opacity-100 pointer-events-none" />
                      </div>
                      <label className="text-sm leading-none">Item 3</label>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Checkbox } from '@legalyard/design-system'

function IndeterminateExample() {
  const [items, setItems] = useState([
    { id: 1, name: 'Item 1', checked: true },
    { id: 2, name: 'Item 2', checked: false },
    { id: 3, name: 'Item 3', checked: true }
  ])

  const checkedItems = items.filter(item => item.checked)
  const isAllChecked = checkedItems.length === items.length
  const isIndeterminate = checkedItems.length > 0 && checkedItems.length < items.length

  const handleSelectAll = (checked: boolean) => {
    setItems(items.map(item => ({ ...item, checked })))
  }

  const handleItemChange = (id: number, checked: boolean) => {
    setItems(items.map(item => 
      item.id === id ? { ...item, checked } : item
    ))
  }

  return (
    <div className="space-y-4">
      <Checkbox
        checked={isAllChecked}
        indeterminate={isIndeterminate}
        onCheckedChange={handleSelectAll}
        label="Select all items"
      />
      
      <div className="ml-6 space-y-2">
        {items.map(item => (
          <Checkbox
            key={item.id}
            checked={item.checked}
            onCheckedChange={(checked) => handleItemChange(item.id, checked)}
            label={item.name}
          />
        ))}
      </div>
    </div>
  )
}`}
              />
            </div>

            {/* Sizes */}
            <div>
              <h3 className="text-lg font-medium mb-4">Sizes</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="peer h-3 w-3 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                      />
                      <Check className="absolute inset-0 h-3 w-3 text-primary-foreground opacity-100 pointer-events-none" />
                    </div>
                    <label className="text-xs leading-none">Small checkbox</label>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                      />
                      <Check className="absolute inset-0 h-4 w-4 text-primary-foreground opacity-100 pointer-events-none" />
                    </div>
                    <label className="text-sm leading-none">Medium checkbox</label>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="peer h-5 w-5 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                      />
                      <Check className="absolute inset-0 h-5 w-5 text-primary-foreground opacity-100 pointer-events-none" />
                    </div>
                    <label className="text-base leading-none">Large checkbox</label>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<Checkbox size="sm" checked label="Small checkbox" />
<Checkbox size="md" checked label="Medium checkbox" />
<Checkbox size="lg" checked label="Large checkbox" />`}
              />
            </div>

            {/* Form Integration */}
            <div>
              <h3 className="text-lg font-medium mb-4">Form Integration</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Preferences</label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="checkbox"
                            name="notifications"
                            defaultChecked
                            className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                          />
                          <Check className="absolute inset-0 h-4 w-4 text-primary-foreground opacity-100 pointer-events-none" />
                        </div>
                        <label className="text-sm leading-none">Email notifications</label>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="checkbox"
                            name="marketing"
                            className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                          />
                        </div>
                        <label className="text-sm leading-none">Marketing emails</label>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <input
                            type="checkbox"
                            name="updates"
                            defaultChecked
                            className="peer h-4 w-4 shrink-0 rounded-sm border border-border ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                          />
                          <Check className="absolute inset-0 h-4 w-4 text-primary-foreground opacity-100 pointer-events-none" />
                        </div>
                        <label className="text-sm leading-none">Product updates</label>
                      </div>
                    </div>
                  </div>
                  
                  <div className="pt-4">
                    <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-9 px-4">
                      Save Preferences
                    </button>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`import { Checkbox } from '@legalyard/design-system'

function PreferencesForm() {
  const [preferences, setPreferences] = useState({
    notifications: true,
    marketing: false,
    updates: true
  })

  const handleChange = (key: string, checked: boolean) => {
    setPreferences(prev => ({ ...prev, [key]: checked }))
  }

  return (
    <form>
      <fieldset>
        <legend>Preferences</legend>
        <div className="space-y-3">
          <Checkbox
            name="notifications"
            checked={preferences.notifications}
            onCheckedChange={(checked) => handleChange('notifications', checked)}
            label="Email notifications"
          />
          
          <Checkbox
            name="marketing"
            checked={preferences.marketing}
            onCheckedChange={(checked) => handleChange('marketing', checked)}
            label="Marketing emails"
          />
          
          <Checkbox
            name="updates"
            checked={preferences.updates}
            onCheckedChange={(checked) => handleChange('updates', checked)}
            label="Product updates"
          />
        </div>
      </fieldset>
      
      <button type="submit">Save Preferences</button>
    </form>
  )
}`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="legalyard-card overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">Prop</th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Default</th>
                  <th className="text-left p-4 font-medium">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">checked</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the checkbox is checked</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">defaultChecked</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Default checked state (uncontrolled)</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">indeterminate</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the checkbox is in indeterminate state</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">disabled</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the checkbox is disabled</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">size</td>
                  <td className="p-4 text-sm">'sm' | 'md' | 'lg'</td>
                  <td className="p-4 text-sm">'md'</td>
                  <td className="p-4 text-sm">The size of the checkbox</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">label</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Label text for the checkbox</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">description</td>
                  <td className="p-4 text-sm">string</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Description text below the label</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">onCheckedChange</td>
                  <td className="p-4 text-sm">(checked: boolean) => void</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Callback when checked state changes</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use clear, descriptive labels</li>
                <li>• Group related checkboxes logically</li>
                <li>• Use indeterminate state for partial selections</li>
                <li>• Provide adequate touch targets (minimum 44px)</li>
                <li>• Use consistent sizing throughout your interface</li>
                <li>• Make labels clickable to improve usability</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't use checkboxes for mutually exclusive options</li>
                <li>• Avoid unclear or ambiguous labels</li>
                <li>• Don't make checkboxes too small for touch devices</li>
                <li>• Avoid using checkboxes for actions (use buttons)</li>
                <li>• Don't nest checkboxes too deeply</li>
                <li>• Avoid overusing indeterminate states</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
