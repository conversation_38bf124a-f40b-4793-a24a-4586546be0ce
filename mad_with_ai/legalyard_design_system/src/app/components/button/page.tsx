import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { Play, Download, Heart, ArrowRight, Plus, Star, Trash2, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/toolkit/ui/button"

export default function ButtonPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Button</h1>
          <p className="text-xl text-muted-foreground">
            Versatile button component with multiple variants, sizes, and interactive states for user actions.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>

          <div className="space-y-8">
            {/* Variants */}
            <div>
              <h3 className="text-lg font-medium mb-4">Variants</h3>
              <div className="legalyard-card p-6">
                <div className="flex flex-wrap gap-3">
                  <Button variant="primary">Primary</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="accent">Accent</Button>
                  <Button variant="success">Success</Button>
                  <Button variant="warning">Warning</Button>
                  <Button variant="error">Error</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="link">Link</Button>
                </div>
              </div>
              <CodeBlock
                code={`import { Button } from '@legalyard/design-system'

function ButtonVariants() {
  return (
    <div className="flex gap-3">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="accent">Accent</Button>
      <Button variant="success">Success</Button>
      <Button variant="warning">Warning</Button>
      <Button variant="error">Error</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="link">Link</Button>
    </div>
  )
}`}
              />
            </div>

            {/* Sizes */}
            <div>
              <h3 className="text-lg font-medium mb-4">Sizes</h3>
              <div className="legalyard-card p-6">
                <div className="flex items-center gap-3">
                  <Button size="sm">Small</Button>
                  <Button size="md">Medium</Button>
                  <Button size="lg">Large</Button>
                  <Button size="xl">Extra Large</Button>
                </div>
                <div className="mt-4">
                  <Button size="full">Full Width</Button>
                </div>
              </div>
              <CodeBlock
                code={`<Button size="sm">Small</Button>
<Button size="md">Medium</Button>
<Button size="lg">Large</Button>
<Button size="xl">Extra Large</Button>
<Button size="full">Full Width</Button>`}
              />
            </div>

            {/* With Icons */}
            <div>
              <h3 className="text-lg font-medium mb-4">With Icons</h3>
              <div className="legalyard-card p-6">
                <div className="flex flex-wrap gap-3">
                  <Button leftIcon={<Download className="h-4 w-4" />}>
                    Download
                  </Button>
                  <Button rightIcon={<ArrowRight className="h-4 w-4" />}>
                    Continue
                  </Button>
                  <Button variant="outline" leftIcon={<Plus className="h-4 w-4" />}>
                    Add Item
                  </Button>
                  <Button variant="error" leftIcon={<Trash2 className="h-4 w-4" />}>
                    Delete
                  </Button>
                  <Button variant="ghost" leftIcon={<Heart className="h-4 w-4" />}>
                    Like
                  </Button>
                </div>
              </div>
              <CodeBlock
                code={`import { Download, ArrowRight, Plus, Trash2, Heart } from 'lucide-react'

<Button leftIcon={<Download className="h-4 w-4" />}>
  Download
</Button>

<Button rightIcon={<ArrowRight className="h-4 w-4" />}>
  Continue
</Button>

<Button variant="outline" leftIcon={<Plus className="h-4 w-4" />}>
  Add Item
</Button>`}
              />
            </div>

            {/* Loading States */}
            <div>
              <h3 className="text-lg font-medium mb-4">Loading States</h3>
              <div className="legalyard-card p-6">
                <div className="flex flex-wrap gap-3">
                  <Button loading>Loading...</Button>
                  <Button variant="outline" loading>
                    Processing
                  </Button>
                  <Button variant="success" loading>
                    Saving
                  </Button>
                  <Button variant="error" loading>
                    Deleting
                  </Button>
                </div>
              </div>
              <CodeBlock
                code={`<Button loading>Loading...</Button>
<Button variant="outline" loading>Processing</Button>
<Button variant="success" loading>Saving</Button>
<Button variant="error" loading>Deleting</Button>`}
              />
            </div>

            {/* Disabled States */}
            <div>
              <h3 className="text-lg font-medium mb-4">Disabled States</h3>
              <div className="legalyard-card p-6">
                <div className="flex flex-wrap gap-3">
                  <Button disabled>Disabled</Button>
                  <Button variant="outline" disabled>
                    Disabled Outline
                  </Button>
                  <Button variant="ghost" disabled>
                    Disabled Ghost
                  </Button>
                  <Button variant="link" disabled>
                    Disabled Link
                  </Button>
                </div>
              </div>
              <CodeBlock
                code={`<Button disabled>Disabled</Button>
<Button variant="outline" disabled>Disabled Outline</Button>
<Button variant="ghost" disabled>Disabled Ghost</Button>
<Button variant="link" disabled>Disabled Link</Button>`}
              />
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}