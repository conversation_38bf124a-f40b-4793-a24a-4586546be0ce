import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { Play, Download, Heart, ArrowRight, Plus } from "lucide-react"

export default function ButtonPage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Button</h1>
          <p className="text-xl text-muted-foreground">
            A versatile button component with multiple variants, sizes, and states built with the Legalyard design system.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Usage */}
            <div>
              <h3 className="text-lg font-medium mb-4">Variants</h3>
              <div className="legalyard-card p-6">
                <div className="flex flex-wrap gap-4">
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2">
                    Primary
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-amber-500 to-yellow-500 text-white hover:from-amber-600 hover:to-yellow-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2">
                    Secondary
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-blue-600 to-sky-500 text-white hover:from-blue-700 hover:to-sky-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2">
                    Accent
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-green-600 to-emerald-500 text-white hover:from-green-700 hover:to-emerald-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2">
                    Success
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-red-600 to-rose-500 text-white hover:from-red-700 hover:to-rose-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2">
                    Error
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-10 px-4 py-2">
                    Outline
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                    Ghost
                  </button>
                </div>
              </div>
              <CodeBlock
                code={`import { Button } from '@legalyard/design-system'

function Example() {
  return (
    <div className="flex gap-4 flex-wrap">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="accent">Accent</Button>
      <Button variant="success">Success</Button>
      <Button variant="error">Error</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="ghost">Ghost</Button>
    </div>
  )
}`}
              />
            </div>

            {/* Sizes */}
            <div>
              <h3 className="text-lg font-medium mb-4">Sizes</h3>
              <div className="legalyard-card p-6">
                <div className="flex flex-wrap items-center gap-4">
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-xs font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-8 px-3">
                    Small
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2">
                    Medium
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-base font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-12 px-6">
                    Large
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-lg font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-14 px-8">
                    Extra Large
                  </button>
                </div>
              </div>
              <CodeBlock
                code={`<Button size="sm">Small</Button>
<Button size="md">Medium</Button>
<Button size="lg">Large</Button>
<Button size="xl">Extra Large</Button>`}
              />
            </div>

            {/* With Icons */}
            <div>
              <h3 className="text-lg font-medium mb-4">With Icons</h3>
              <div className="legalyard-card p-6">
                <div className="flex flex-wrap gap-4">
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2">
                    <Play className="mr-2 h-4 w-4" />
                    Play Video
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-blue-600 to-sky-500 text-white hover:from-blue-700 hover:to-sky-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2">
                    Download
                    <Download className="ml-2 h-4 w-4" />
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-10 px-4 py-2">
                    <Heart className="mr-2 h-4 w-4" />
                    Like
                  </button>
                  <button className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden hover:bg-accent hover:text-accent-foreground h-10 w-10">
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <CodeBlock
                code={`import { Play, Download, Heart, Plus } from 'lucide-react'

<Button leftIcon={<Play />}>Play Video</Button>
<Button variant="accent" rightIcon={<Download />}>Download</Button>
<Button variant="outline" leftIcon={<Heart />}>Like</Button>
<Button variant="ghost" size="md" className="w-10 h-10 p-0">
  <Plus />
</Button>`}
              />
            </div>

            {/* States */}
            <div>
              <h3 className="text-lg font-medium mb-4">States</h3>
              <div className="p-6 border border-border rounded-lg bg-background">
                <div className="flex flex-wrap gap-4">
                  <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                    Default
                  </button>
                  <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 opacity-50 pointer-events-none" disabled>
                    Disabled
                  </button>
                  <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Loading
                  </button>
                </div>
              </div>
              <CodeBlock 
                code={`<Button>Default</Button>
<Button disabled>Disabled</Button>
<Button loading>Loading</Button>`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="border border-border rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">Prop</th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Default</th>
                  <th className="text-left p-4 font-medium">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">variant</td>
                  <td className="p-4 text-sm">'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'outline' | 'ghost' | 'link'</td>
                  <td className="p-4 text-sm">'primary'</td>
                  <td className="p-4 text-sm">The visual style variant of the button</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">size</td>
                  <td className="p-4 text-sm">'sm' | 'md' | 'lg' | 'xl' | 'full'</td>
                  <td className="p-4 text-sm">'md'</td>
                  <td className="p-4 text-sm">The size of the button</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">disabled</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the button is disabled</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">loading</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the button is in loading state</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">leftIcon</td>
                  <td className="p-4 text-sm">ReactNode</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Icon to display on the left side</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">rightIcon</td>
                  <td className="p-4 text-sm">ReactNode</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Icon to display on the right side</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">fullWidth</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the button should take full width</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">onClick</td>
                  <td className="p-4 text-sm">(event: MouseEvent) => void</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Click event handler</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">children</td>
                  <td className="p-4 text-sm">ReactNode</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Button content</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-3">When to Use</h3>
              <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                <li>For primary actions that users need to take</li>
                <li>To submit forms or trigger important actions</li>
                <li>For navigation between different sections</li>
                <li>To open modals or dialogs</li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-3">Best Practices</h3>
              <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                <li>Use clear, action-oriented labels</li>
                <li>Limit primary buttons to one per section</li>
                <li>Provide feedback for loading states</li>
                <li>Ensure adequate touch targets (minimum 44px)</li>
                <li>Use consistent sizing throughout your application</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
