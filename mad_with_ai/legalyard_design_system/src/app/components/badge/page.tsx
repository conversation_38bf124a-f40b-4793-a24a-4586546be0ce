import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import { <PERSON>, X, <PERSON><PERSON><PERSON>riangle, Info, Star, Zap } from "lucide-react"

export default function BadgePage() {
  return (
    <PageLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Badge</h1>
          <p className="text-xl text-muted-foreground">
            Small status indicators and labels for displaying metadata, counts, and categorical information.
          </p>
        </div>

        {/* Examples Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Examples</h2>
          
          <div className="space-y-8">
            {/* Basic Variants */}
            <div>
              <h3 className="text-lg font-medium mb-4">Variants</h3>
              <div className="legalyard-card p-6">
                <div className="flex flex-wrap gap-3">
                  <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                    Primary
                  </span>
                  <span className="inline-flex items-center rounded-full bg-secondary/10 px-2.5 py-0.5 text-xs font-medium text-secondary-foreground">
                    Secondary
                  </span>
                  <span className="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/20 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:text-green-400">
                    Success
                  </span>
                  <span className="inline-flex items-center rounded-full bg-red-100 dark:bg-red-900/20 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:text-red-400">
                    Error
                  </span>
                  <span className="inline-flex items-center rounded-full bg-yellow-100 dark:bg-yellow-900/20 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:text-yellow-400">
                    Warning
                  </span>
                  <span className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/20 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:text-blue-400">
                    Info
                  </span>
                  <span className="inline-flex items-center rounded-full bg-muted px-2.5 py-0.5 text-xs font-medium text-muted-foreground">
                    Muted
                  </span>
                </div>
              </div>
              <CodeBlock 
                code={`import { Badge } from '@legalyard/design-system'

function BadgeVariants() {
  return (
    <div className="flex gap-2">
      <Badge variant="primary">Primary</Badge>
      <Badge variant="secondary">Secondary</Badge>
      <Badge variant="success">Success</Badge>
      <Badge variant="error">Error</Badge>
      <Badge variant="warning">Warning</Badge>
      <Badge variant="info">Info</Badge>
      <Badge variant="muted">Muted</Badge>
    </div>
  )
}`}
              />
            </div>

            {/* Sizes */}
            <div>
              <h3 className="text-lg font-medium mb-4">Sizes</h3>
              <div className="legalyard-card p-6">
                <div className="flex items-center gap-3">
                  <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary">
                    Small
                  </span>
                  <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                    Medium
                  </span>
                  <span className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary">
                    Large
                  </span>
                </div>
              </div>
              <CodeBlock 
                code={`<Badge size="sm">Small</Badge>
<Badge size="md">Medium</Badge>
<Badge size="lg">Large</Badge>`}
              />
            </div>

            {/* With Icons */}
            <div>
              <h3 className="text-lg font-medium mb-4">With Icons</h3>
              <div className="legalyard-card p-6">
                <div className="flex flex-wrap gap-3">
                  <span className="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/20 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:text-green-400">
                    <Check className="mr-1 h-3 w-3" />
                    Verified
                  </span>
                  <span className="inline-flex items-center rounded-full bg-red-100 dark:bg-red-900/20 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:text-red-400">
                    <X className="mr-1 h-3 w-3" />
                    Failed
                  </span>
                  <span className="inline-flex items-center rounded-full bg-yellow-100 dark:bg-yellow-900/20 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:text-yellow-400">
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    Warning
                  </span>
                  <span className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/20 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:text-blue-400">
                    <Info className="mr-1 h-3 w-3" />
                    Info
                  </span>
                  <span className="inline-flex items-center rounded-full bg-purple-100 dark:bg-purple-900/20 px-2.5 py-0.5 text-xs font-medium text-purple-800 dark:text-purple-400">
                    <Star className="mr-1 h-3 w-3" />
                    Featured
                  </span>
                  <span className="inline-flex items-center rounded-full bg-orange-100 dark:bg-orange-900/20 px-2.5 py-0.5 text-xs font-medium text-orange-800 dark:text-orange-400">
                    <Zap className="mr-1 h-3 w-3" />
                    Premium
                  </span>
                </div>
              </div>
              <CodeBlock 
                code={`import { Check, X, AlertTriangle, Info, Star, Zap } from 'lucide-react'

<Badge variant="success">
  <Check className="mr-1 h-3 w-3" />
  Verified
</Badge>

<Badge variant="error">
  <X className="mr-1 h-3 w-3" />
  Failed
</Badge>

<Badge variant="warning">
  <AlertTriangle className="mr-1 h-3 w-3" />
  Warning
</Badge>`}
              />
            </div>

            {/* Notification Badges */}
            <div>
              <h3 className="text-lg font-medium mb-4">Notification Badges</h3>
              <div className="legalyard-card p-6">
                <div className="flex items-center gap-6">
                  <div className="relative">
                    <button className="p-2 rounded-lg border border-border hover:bg-accent">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7H6a2 2 0 00-2 2v9a2 2 0 002 2h8a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 0V4a2 2 0 00-2-2H9a2 2 0 00-2 2v3m1 0h4" />
                      </svg>
                    </button>
                    <span className="absolute -top-1 -right-1 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full">
                      3
                    </span>
                  </div>
                  
                  <div className="relative">
                    <button className="p-2 rounded-lg border border-border hover:bg-accent">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                      </svg>
                    </button>
                    <span className="absolute -top-1 -right-1 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-primary rounded-full">
                      12
                    </span>
                  </div>
                  
                  <div className="relative">
                    <button className="p-2 rounded-lg border border-border hover:bg-accent">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </button>
                    <span className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"></span>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`// Notification count badge
<div className="relative">
  <Button variant="outline">
    <MessageIcon />
  </Button>
  <Badge 
    variant="error" 
    className="absolute -top-1 -right-1 min-w-5 h-5 flex items-center justify-center rounded-full text-xs"
  >
    3
  </Badge>
</div>

// Status indicator
<div className="relative">
  <Avatar />
  <Badge 
    variant="success" 
    className="absolute -top-1 -right-1 w-3 h-3 rounded-full p-0"
  />
</div>`}
              />
            </div>

            {/* Interactive Badges */}
            <div>
              <h3 className="text-lg font-medium mb-4">Interactive Badges</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Removable Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                        React
                        <button className="ml-1 text-primary/60 hover:text-primary">
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                      <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                        TypeScript
                        <button className="ml-1 text-primary/60 hover:text-primary">
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                      <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                        Next.js
                        <button className="ml-1 text-primary/60 hover:text-primary">
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Clickable Badges</h4>
                    <div className="flex flex-wrap gap-2">
                      <button className="inline-flex items-center rounded-full bg-muted px-2.5 py-0.5 text-xs font-medium text-muted-foreground hover:bg-primary/10 hover:text-primary transition-colors">
                        #frontend
                      </button>
                      <button className="inline-flex items-center rounded-full bg-muted px-2.5 py-0.5 text-xs font-medium text-muted-foreground hover:bg-primary/10 hover:text-primary transition-colors">
                        #design
                      </button>
                      <button className="inline-flex items-center rounded-full bg-muted px-2.5 py-0.5 text-xs font-medium text-muted-foreground hover:bg-primary/10 hover:text-primary transition-colors">
                        #javascript
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`// Removable badge
<Badge variant="primary" removable onRemove={() => handleRemove(id)}>
  React
</Badge>

// Clickable badge
<Badge 
  variant="muted" 
  clickable 
  onClick={() => handleTagClick('frontend')}
>
  #frontend
</Badge>`}
              />
            </div>

            {/* Real-world Examples */}
            <div>
              <h3 className="text-lg font-medium mb-4">Real-world Examples</h3>
              <div className="legalyard-card p-6">
                <div className="space-y-6">
                  {/* User Profile */}
                  <div className="flex items-center space-x-4 p-4 border border-border rounded-lg">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-amber-400 rounded-full flex items-center justify-center text-white font-semibold">
                      JD
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">John Doe</h4>
                        <span className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/20 px-2 py-0.5 text-xs font-medium text-blue-800 dark:text-blue-400">
                          <Check className="mr-1 h-3 w-3" />
                          Verified
                        </span>
                        <span className="inline-flex items-center rounded-full bg-purple-100 dark:bg-purple-900/20 px-2 py-0.5 text-xs font-medium text-purple-800 dark:text-purple-400">
                          Pro
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">Software Engineer</p>
                    </div>
                  </div>
                  
                  {/* Product Card */}
                  <div className="p-4 border border-border rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium">Premium Design System</h4>
                      <span className="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/20 px-2 py-0.5 text-xs font-medium text-green-800 dark:text-green-400">
                        In Stock
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      Complete UI component library with React, Vue, and Angular support.
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex space-x-1">
                        <span className="inline-flex items-center rounded-full bg-muted px-2 py-0.5 text-xs font-medium text-muted-foreground">
                          React
                        </span>
                        <span className="inline-flex items-center rounded-full bg-muted px-2 py-0.5 text-xs font-medium text-muted-foreground">
                          TypeScript
                        </span>
                        <span className="inline-flex items-center rounded-full bg-orange-100 dark:bg-orange-900/20 px-2 py-0.5 text-xs font-medium text-orange-800 dark:text-orange-400">
                          New
                        </span>
                      </div>
                      <span className="text-lg font-semibold">$99</span>
                    </div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`// User profile with status badges
<div className="flex items-center space-x-2">
  <h4>John Doe</h4>
  <Badge variant="info">
    <Check className="mr-1 h-3 w-3" />
    Verified
  </Badge>
  <Badge variant="premium">Pro</Badge>
</div>

// Product with tags and status
<div className="product-card">
  <div className="flex justify-between">
    <h4>Premium Design System</h4>
    <Badge variant="success">In Stock</Badge>
  </div>
  <div className="tags">
    <Badge variant="muted">React</Badge>
    <Badge variant="muted">TypeScript</Badge>
    <Badge variant="warning">New</Badge>
  </div>
</div>`}
              />
            </div>
          </div>
        </section>

        {/* API Reference */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">API Reference</h2>
          
          <div className="legalyard-card overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">Prop</th>
                  <th className="text-left p-4 font-medium">Type</th>
                  <th className="text-left p-4 font-medium">Default</th>
                  <th className="text-left p-4 font-medium">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">variant</td>
                  <td className="p-4 text-sm">'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info' | 'muted'</td>
                  <td className="p-4 text-sm">'primary'</td>
                  <td className="p-4 text-sm">The visual style variant of the badge</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">size</td>
                  <td className="p-4 text-sm">'sm' | 'md' | 'lg'</td>
                  <td className="p-4 text-sm">'md'</td>
                  <td className="p-4 text-sm">The size of the badge</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">removable</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the badge can be removed</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">clickable</td>
                  <td className="p-4 text-sm">boolean</td>
                  <td className="p-4 text-sm">false</td>
                  <td className="p-4 text-sm">Whether the badge is clickable</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">onRemove</td>
                  <td className="p-4 text-sm">() => void</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Callback when badge is removed</td>
                </tr>
                <tr className="border-t border-border">
                  <td className="p-4 font-mono text-sm">onClick</td>
                  <td className="p-4 text-sm">() => void</td>
                  <td className="p-4 text-sm">-</td>
                  <td className="p-4 text-sm">Callback when badge is clicked</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Usage Guidelines</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-green-600">✅ Best Practices</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use badges to highlight important status information</li>
                <li>• Keep badge text short and descriptive</li>
                <li>• Use consistent colors for similar types of information</li>
                <li>• Position badges near related content</li>
                <li>• Use notification badges sparingly to avoid overwhelming users</li>
                <li>• Make interactive badges clearly clickable</li>
              </ul>
            </div>
            <div className="legalyard-card p-6">
              <h3 className="font-semibold mb-3 text-red-600">❌ Common Pitfalls</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't use badges for lengthy text content</li>
                <li>• Avoid using too many badges in one area</li>
                <li>• Don't use badges as primary navigation elements</li>
                <li>• Avoid unclear or ambiguous badge labels</li>
                <li>• Don't make non-interactive badges look clickable</li>
                <li>• Avoid using badges for critical actions</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
