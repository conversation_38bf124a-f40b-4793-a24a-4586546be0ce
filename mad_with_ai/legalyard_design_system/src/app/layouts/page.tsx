import { PageLayout } from "@/components/page-layout"
import { CodeBlock } from "@/components/code-block"
import Link from "next/link"
import { ArrowRight, Layout, PanelLeft, Columns, Grid } from "lucide-react"

const layoutCategories = [
  {
    name: "Page Layouts",
    description: "Complete page layout components and templates",
    icon: <Layout className="h-6 w-6" />,
    layouts: [
      {
        name: "AppLayout",
        description: "Main application layout with header, sidebar, and content area",
        example: "<AppLayout header={<Header />} sidebar={<Sidebar />}>{content}</AppLayout>"
      },
      {
        name: "DashboardLayout",
        description: "Dashboard layout with navigation and widget areas",
        example: "<DashboardLayout>{dashboardContent}</DashboardLayout>"
      },
      {
        name: "AuthLayout",
        description: "Authentication pages layout with centered forms",
        example: "<AuthLayout>{loginForm}</AuthLayout>"
      },
      {
        name: "LandingLayout",
        description: "Marketing/landing page layout with hero sections",
        example: "<LandingLayout>{landingContent}</LandingLayout>"
      },
      {
        name: "DocumentLayout",
        description: "Document-style layout with table of contents",
        example: "<DocumentLayout toc={toc}>{documentContent}</DocumentLayout>"
      }
    ]
  },
  {
    name: "Navigation Components",
    description: "Header, sidebar, and navigation components",
    icon: <Columns className="h-6 w-6" />,
    layouts: [
      {
        name: "Header",
        description: "Application header with logo, navigation, and user menu",
        example: "<Header logo={logo} navigation={nav} userMenu={menu} />"
      },
      {
        name: "Sidebar",
        description: "Collapsible sidebar navigation component",
        example: "<Sidebar items={navItems} collapsed={isCollapsed} />"
      },
      {
        name: "Breadcrumb",
        description: "Breadcrumb navigation component",
        example: "<Breadcrumb items={breadcrumbItems} />"
      },
      {
        name: "TabNavigation",
        description: "Tab-based navigation component",
        example: "<TabNavigation tabs={tabs} activeTab={activeTab} />"
      },
      {
        name: "Footer",
        description: "Application footer with links and information",
        example: "<Footer links={footerLinks} copyright={copyrightText} />"
      }
    ]
  },
  {
    name: "Grid Systems",
    description: "Flexible grid and layout systems",
    icon: <Grid className="h-6 w-6" />,
    layouts: [
      {
        name: "Grid",
        description: "Responsive grid system with customizable columns",
        example: "<Grid cols={12} gap={4}>{gridItems}</Grid>"
      },
      {
        name: "Container",
        description: "Responsive container with max-width constraints",
        example: "<Container size='lg'>{content}</Container>"
      },
      {
        name: "Stack",
        description: "Vertical or horizontal stack layout",
        example: "<Stack direction='vertical' spacing={4}>{items}</Stack>"
      },
      {
        name: "Flex",
        description: "Flexbox layout component with common patterns",
        example: "<Flex justify='between' align='center'>{flexItems}</Flex>"
      },
      {
        name: "Masonry",
        description: "Pinterest-style masonry layout",
        example: "<Masonry columns={3} gap={16}>{masonryItems}</Masonry>"
      }
    ]
  },
  {
    name: "Content Layouts",
    description: "Specialized layouts for different content types",
    icon: <PanelLeft className="h-6 w-6" />,
    layouts: [
      {
        name: "ArticleLayout",
        description: "Blog post or article layout with metadata",
        example: "<ArticleLayout meta={articleMeta}>{articleContent}</ArticleLayout>"
      },
      {
        name: "CardLayout",
        description: "Card-based layout for displaying collections",
        example: "<CardLayout cards={cardData} columns={3} />"
      },
      {
        name: "ListLayout",
        description: "List layout with filtering and sorting",
        example: "<ListLayout items={listItems} filters={filters} />"
      },
      {
        name: "SplitLayout",
        description: "Split-screen layout with resizable panels",
        example: "<SplitLayout left={leftPanel} right={rightPanel} />"
      },
      {
        name: "ModalLayout",
        description: "Modal and dialog layout component",
        example: "<ModalLayout isOpen={isOpen} onClose={handleClose}>{modalContent}</ModalLayout>"
      }
    ]
  }
]

export default function LayoutsPage() {
  return (
    <PageLayout>
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Layouts</h1>
          <p className="text-xl text-muted-foreground">
            Flexible layout components and systems for building consistent user interfaces.
          </p>
        </div>

        {/* Quick Start */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Quick Start</h2>
          <div className="bg-muted/50 rounded-lg p-6">
            <p className="mb-4">Import layout components from the design system:</p>
            <CodeBlock 
              code={`import { 
  AppLayout, 
  Header, 
  Sidebar, 
  Container,
  Grid,
  Stack 
} from '@legalyard/design-system/layouts'

function App() {
  return (
    <AppLayout
      header={
        <Header 
          logo={<Logo />}
          navigation={<Navigation />}
          userMenu={<UserMenu />}
        />
      }
      sidebar={
        <Sidebar 
          items={navigationItems}
          collapsed={sidebarCollapsed}
        />
      }
    >
      <Container size="xl">
        <Grid cols={12} gap={6}>
          <div className="col-span-8">
            <Stack spacing={6}>
              {mainContent}
            </Stack>
          </div>
          <div className="col-span-4">
            {sidebarContent}
          </div>
        </Grid>
      </Container>
    </AppLayout>
  )
}`}
            />
          </div>
        </section>

        {/* Layout Categories */}
        <div className="space-y-12">
          {layoutCategories.map((category) => (
            <section key={category.name}>
              <div className="flex items-center space-x-3 mb-6">
                <div className="text-primary">{category.icon}</div>
                <div>
                  <h2 className="text-2xl font-semibold">{category.name}</h2>
                  <p className="text-muted-foreground">{category.description}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {category.layouts.map((layout) => (
                  <LayoutCard
                    key={layout.name}
                    name={layout.name}
                    description={layout.description}
                    example={layout.example}
                    href={`/layouts/${layout.name.toLowerCase()}`}
                  />
                ))}
              </div>
            </section>
          ))}
        </div>

        {/* Layout Examples */}
        <section className="mt-16">
          <h2 className="text-2xl font-semibold mb-6">Layout Examples</h2>
          
          <div className="space-y-8">
            {/* Dashboard Layout Example */}
            <div>
              <h3 className="text-lg font-medium mb-4">Dashboard Layout</h3>
              <div className="border border-border rounded-lg p-4 bg-muted/20">
                <div className="h-12 bg-primary/20 rounded mb-4 flex items-center px-4">
                  <div className="w-24 h-6 bg-primary/40 rounded"></div>
                  <div className="ml-auto flex space-x-2">
                    <div className="w-8 h-8 bg-primary/40 rounded"></div>
                    <div className="w-8 h-8 bg-primary/40 rounded"></div>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-48 bg-muted/50 rounded p-4">
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded"></div>
                      <div className="h-4 bg-muted rounded"></div>
                      <div className="h-4 bg-muted rounded"></div>
                    </div>
                  </div>
                  <div className="flex-1 bg-background rounded p-4 border border-border">
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="h-20 bg-muted/50 rounded"></div>
                      <div className="h-20 bg-muted/50 rounded"></div>
                      <div className="h-20 bg-muted/50 rounded"></div>
                    </div>
                    <div className="h-32 bg-muted/50 rounded"></div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<DashboardLayout>
  <DashboardHeader>
    <Logo />
    <UserMenu />
  </DashboardHeader>
  
  <DashboardSidebar>
    <Navigation items={navItems} />
  </DashboardSidebar>
  
  <DashboardContent>
    <Grid cols={3} gap={6}>
      <MetricCard title="Users" value="1,234" />
      <MetricCard title="Revenue" value="$12,345" />
      <MetricCard title="Orders" value="567" />
    </Grid>
    
    <Chart data={chartData} />
  </DashboardContent>
</DashboardLayout>`}
              />
            </div>

            {/* Article Layout Example */}
            <div>
              <h3 className="text-lg font-medium mb-4">Article Layout</h3>
              <div className="border border-border rounded-lg p-4 bg-muted/20">
                <div className="max-w-2xl mx-auto">
                  <div className="h-8 bg-primary/40 rounded mb-2"></div>
                  <div className="h-4 bg-muted rounded mb-4 w-1/3"></div>
                  <div className="space-y-2 mb-6">
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded w-3/4"></div>
                  </div>
                  <div className="h-32 bg-muted/50 rounded mb-6"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded w-5/6"></div>
                  </div>
                </div>
              </div>
              <CodeBlock 
                code={`<ArticleLayout
  meta={{
    title: "Getting Started with Design Systems",
    author: "John Doe",
    publishedAt: "2023-12-01",
    readTime: "5 min read"
  }}
  tableOfContents={toc}
>
  <ArticleHeader />
  
  <ArticleContent>
    <p>Your article content goes here...</p>
    <Image src="/article-image.jpg" alt="Article illustration" />
    <p>More content...</p>
  </ArticleContent>
  
  <ArticleFooter>
    <ShareButtons />
    <RelatedArticles />
  </ArticleFooter>
</ArticleLayout>`}
              />
            </div>
          </div>
        </section>

        {/* Responsive Design */}
        <section className="mt-16">
          <h2 className="text-2xl font-semibold mb-6">Responsive Design</h2>
          <div className="bg-muted/50 rounded-lg p-6">
            <p className="mb-4">All layout components are built with responsive design in mind:</p>
            <CodeBlock 
              code={`// Responsive grid
<Grid 
  cols={{ xs: 1, sm: 2, md: 3, lg: 4 }}
  gap={{ xs: 4, md: 6 }}
>
  {items}
</Grid>

// Responsive container
<Container 
  size={{ xs: 'full', sm: 'md', lg: 'xl' }}
  padding={{ xs: 4, md: 8 }}
>
  {content}
</Container>

// Responsive stack
<Stack 
  direction={{ xs: 'vertical', md: 'horizontal' }}
  spacing={{ xs: 4, md: 8 }}
>
  {stackItems}
</Stack>`}
            />
          </div>
        </section>

        {/* Best Practices */}
        <section className="mt-16">
          <h2 className="text-2xl font-semibold mb-6">Best Practices</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-6 border border-border rounded-lg">
              <h3 className="font-semibold mb-3 text-green-600">✅ Do</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>Use semantic layout components</li>
                <li>Plan for mobile-first responsive design</li>
                <li>Maintain consistent spacing and alignment</li>
                <li>Use appropriate layout for content type</li>
                <li>Test layouts across different screen sizes</li>
                <li>Consider accessibility in layout design</li>
              </ul>
            </div>
            <div className="p-6 border border-border rounded-lg">
              <h3 className="font-semibold mb-3 text-red-600">❌ Don't</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>Nest too many layout components</li>
                <li>Use fixed dimensions without responsive alternatives</li>
                <li>Ignore content overflow scenarios</li>
                <li>Create layouts that break on small screens</li>
                <li>Use layout components for styling purposes only</li>
                <li>Forget to test with real content</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Related Links */}
        <section className="mt-16 p-8 bg-muted/50 rounded-lg">
          <h3 className="text-xl font-semibold mb-4">Related</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/components"
              className="flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors"
            >
              <span>UI Components</span>
              <ArrowRight className="h-4 w-4" />
            </Link>
            <Link
              href="/styles"
              className="flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors"
            >
              <span>Styles & Theming</span>
              <ArrowRight className="h-4 w-4" />
            </Link>
            <Link
              href="/utils"
              className="flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors"
            >
              <span>Utilities</span>
              <ArrowRight className="h-4 w-4" />
            </Link>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}

interface LayoutCardProps {
  name: string
  description: string
  example: string
  href: string
}

function LayoutCard({ name, description, example, href }: LayoutCardProps) {
  return (
    <Link
      href={href}
      className="group block p-4 border border-border rounded-lg hover:border-primary/50 transition-colors"
    >
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-mono text-sm font-medium group-hover:text-primary transition-colors">{name}</h3>
        <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
      </div>
      <p className="text-sm text-muted-foreground mb-3 leading-relaxed">{description}</p>
      <code className="text-xs bg-muted px-2 py-1 rounded text-muted-foreground block overflow-x-auto">{example}</code>
    </Link>
  )
}
