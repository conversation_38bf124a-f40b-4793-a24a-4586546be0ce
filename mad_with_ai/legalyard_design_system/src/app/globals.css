@import "tailwindcss";

:root {
  /* Legalyard Brand Colors */
  --legalyard-orange: 18 75% 55%; /* #E27735 */
  --legalyard-gold: 39 78% 61%; /* #EDA44C */
  --legalyard-blue: 218 58% 55%; /* #4978D2 */
  --legalyard-sky: 200 73% 67%; /* #65BBE9 */

  /* Light Theme */
  --background: 0 0% 100%;
  --foreground: 222 47% 11%;
  --card: 0 0% 100%;
  --card-foreground: 222 47% 11%;
  --popover: 0 0% 100%;
  --popover-foreground: 222 47% 11%;
  --primary: var(--legalyard-orange);
  --primary-foreground: 0 0% 98%;
  --secondary: var(--legalyard-gold);
  --secondary-foreground: 222 47% 11%;
  --accent: var(--legalyard-sky);
  --accent-foreground: 222 47% 11%;
  --muted: 210 40% 96%;
  --muted-foreground: 215 16% 47%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 98%;
  --border: 214 32% 91%;
  --input: 214 32% 91%;
  --ring: var(--legalyard-orange);
  --radius: 0.75rem;

  /* Additional brand colors */
  --brand-gradient: linear-gradient(135deg, hsl(var(--legalyard-orange)) 0%, hsl(var(--legalyard-gold)) 100%);
  --brand-gradient-blue: linear-gradient(135deg, hsl(var(--legalyard-blue)) 0%, hsl(var(--legalyard-sky)) 100%);
}

.dark {
  /* Dark Theme */
  --background: 222 47% 5%;
  --foreground: 210 40% 98%;
  --card: 222 47% 7%;
  --card-foreground: 210 40% 98%;
  --popover: 222 47% 7%;
  --popover-foreground: 210 40% 98%;
  --primary: var(--legalyard-orange);
  --primary-foreground: 0 0% 98%;
  --secondary: var(--legalyard-gold);
  --secondary-foreground: 0 0% 98%;
  --accent: var(--legalyard-sky);
  --accent-foreground: 222 47% 11%;
  --muted: 217 33% 15%;
  --muted-foreground: 215 20% 65%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 210 40% 98%;
  --border: 217 33% 15%;
  --input: 217 33% 15%;
  --ring: var(--legalyard-orange);
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

* {
  border-color: hsl(var(--border));
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-geist-sans), system-ui, sans-serif;
}

/* Legalyard Custom Styles */
.legalyard-gradient {
  background: var(--brand-gradient);
}

.legalyard-gradient-blue {
  background: var(--brand-gradient-blue);
}

.legalyard-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) + 2px);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  transition: all 0.2s ease-in-out;
}

.legalyard-card:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  transform: translateY(-2px);
}

.legalyard-button {
  background: var(--brand-gradient);
  border: none;
  border-radius: var(--radius);
  color: hsl(var(--primary-foreground));
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.legalyard-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.legalyard-button:hover::before {
  left: 100%;
}

.legalyard-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -8px hsl(var(--legalyard-orange) / 0.5);
}

.legalyard-nav {
  backdrop-filter: blur(20px);
  background: hsl(var(--background) / 0.8);
  border-bottom: 1px solid hsl(var(--border));
}

.legalyard-hero {
  background: linear-gradient(135deg,
    hsl(var(--background)) 0%,
    hsl(var(--legalyard-orange) / 0.05) 50%,
    hsl(var(--legalyard-blue) / 0.05) 100%);
}

.legalyard-code-block {
  background: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  position: relative;
}

.legalyard-code-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--brand-gradient);
  border-radius: var(--radius) var(--radius) 0 0;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.5);
}

/* Focus styles */
.legalyard-focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Animation classes */
.legalyard-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.legalyard-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
