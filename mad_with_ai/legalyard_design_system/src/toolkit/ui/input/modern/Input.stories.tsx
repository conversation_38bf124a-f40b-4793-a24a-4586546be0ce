import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Search, Mail, Lock, User, Phone, CreditCard, Eye, EyeOff } from 'lucide-react';
import { Input } from './index';

const meta = {
  title: 'Components/Input',
  component: Input,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A flexible input component with multiple variants, states, and built-in validation support.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'filled', 'outline', 'ghost'],
      description: 'The visual style variant of the input',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'The size of the input',
    },
    state: {
      control: 'select',
      options: ['default', 'error', 'success', 'warning'],
      description: 'The validation state of the input',
    },
    type: {
      control: 'select',
      options: ['text', 'email', 'password', 'number', 'tel', 'url', 'search'],
      description: 'The input type',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the input is disabled',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the input is in loading state',
    },
    clearable: {
      control: 'boolean',
      description: 'Whether to show a clear button',
    },
    fullWidth: {
      control: 'boolean',
      description: 'Whether the input should take full width',
    },
    onChange: { action: 'changed' },
    onFocus: { action: 'focused' },
    onBlur: { action: 'blurred' },
  },
  args: {
    onChange: fn(),
    onFocus: fn(),
    onBlur: fn(),
  },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic variants
export const Default: Story = {
  args: {
    placeholder: 'Enter text...',
    label: 'Default Input',
  },
};

export const Filled: Story = {
  args: {
    variant: 'filled',
    placeholder: 'Enter text...',
    label: 'Filled Input',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    placeholder: 'Enter text...',
    label: 'Outline Input',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    placeholder: 'Enter text...',
    label: 'Ghost Input',
  },
};

// Sizes
export const Small: Story = {
  args: {
    size: 'sm',
    placeholder: 'Small input',
    label: 'Small Size',
  },
};

export const Medium: Story = {
  args: {
    size: 'md',
    placeholder: 'Medium input',
    label: 'Medium Size',
  },
};

export const Large: Story = {
  args: {
    size: 'lg',
    placeholder: 'Large input',
    label: 'Large Size',
  },
};

// States
export const ErrorState: Story = {
  args: {
    state: 'error',
    placeholder: 'Enter email',
    label: 'Email',
    error: 'Please enter a valid email address',
    value: 'invalid-email',
  },
};

export const SuccessState: Story = {
  args: {
    state: 'success',
    placeholder: 'Enter email',
    label: 'Email',
    success: 'Email is valid!',
    value: '<EMAIL>',
  },
};

export const WarningState: Story = {
  args: {
    state: 'warning',
    placeholder: 'Enter password',
    label: 'Password',
    warning: 'Password should be stronger',
    type: 'password',
    value: '123',
  },
};

// With Icons
export const WithLeftIcon: Story = {
  args: {
    leftIcon: <Search className="h-4 w-4" />,
    placeholder: 'Search...',
    label: 'Search',
  },
};

export const WithRightIcon: Story = {
  args: {
    rightIcon: <Mail className="h-4 w-4" />,
    placeholder: 'Enter email',
    label: 'Email',
    type: 'email',
  },
};

// Input Types
export const EmailInput: Story = {
  args: {
    type: 'email',
    leftIcon: <Mail className="h-4 w-4" />,
    placeholder: '<EMAIL>',
    label: 'Email Address',
  },
};

export const PasswordInput: Story = {
  args: {
    type: 'password',
    leftIcon: <Lock className="h-4 w-4" />,
    placeholder: 'Enter password',
    label: 'Password',
  },
};

export const PhoneInput: Story = {
  args: {
    type: 'tel',
    leftIcon: <Phone className="h-4 w-4" />,
    placeholder: '+****************',
    label: 'Phone Number',
  },
};

// Features
export const LoadingInput: Story = {
  args: {
    loading: true,
    placeholder: 'Loading...',
    label: 'Loading State',
  },
};

export const ClearableInput: Story = {
  args: {
    clearable: true,
    placeholder: 'Type something...',
    label: 'Clearable Input',
    value: 'Clear me!',
  },
};

export const DisabledInput: Story = {
  args: {
    disabled: true,
    placeholder: 'Disabled input',
    label: 'Disabled Input',
    value: 'Cannot edit this',
  },
};

// Interactive Examples
export const AllVariants: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input variant="default" placeholder="Default variant" label="Default" />
      <Input variant="filled" placeholder="Filled variant" label="Filled" />
      <Input variant="outline" placeholder="Outline variant" label="Outline" />
      <Input variant="ghost" placeholder="Ghost variant" label="Ghost" />
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'All available input variants displayed together.',
      },
    },
  },
};

export const AllSizes: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input size="sm" placeholder="Small input" label="Small" />
      <Input size="md" placeholder="Medium input" label="Medium" />
      <Input size="lg" placeholder="Large input" label="Large" />
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'All available input sizes displayed together.',
      },
    },
  },
};

export const ValidationStates: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input 
        placeholder="Default state" 
        label="Default" 
      />
      <Input 
        state="error" 
        placeholder="Error state" 
        label="Error" 
        error="This field is required"
        value="invalid"
      />
      <Input 
        state="success" 
        placeholder="Success state" 
        label="Success" 
        success="Looks good!"
        value="<EMAIL>"
      />
      <Input 
        state="warning" 
        placeholder="Warning state" 
        label="Warning" 
        warning="Consider a stronger password"
        value="weak"
      />
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'Different validation states of the input component.',
      },
    },
  },
};

export const WithIcons: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input 
        leftIcon={<Search className="h-4 w-4" />}
        placeholder="Search..." 
        label="Search" 
      />
      <Input 
        leftIcon={<Mail className="h-4 w-4" />}
        type="email"
        placeholder="<EMAIL>" 
        label="Email" 
      />
      <Input 
        leftIcon={<User className="h-4 w-4" />}
        placeholder="Full name" 
        label="Name" 
      />
      <Input 
        leftIcon={<CreditCard className="h-4 w-4" />}
        placeholder="1234 5678 9012 3456" 
        label="Card Number" 
      />
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'Inputs with various icons for different use cases.',
      },
    },
  },
};

// Real-world examples
export const LoginForm: Story = {
  render: () => (
    <div className="space-y-4 w-80 p-6 border border-border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Sign In</h3>
      <Input 
        leftIcon={<Mail className="h-4 w-4" />}
        type="email"
        placeholder="<EMAIL>" 
        label="Email Address" 
      />
      <Input 
        leftIcon={<Lock className="h-4 w-4" />}
        type="password"
        placeholder="Enter password" 
        label="Password" 
      />
      <button className="w-full bg-gradient-to-r from-orange-600 to-amber-500 text-white rounded-lg h-10 font-medium hover:from-orange-700 hover:to-amber-600 transition-all">
        Sign In
      </button>
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'A real-world login form using input components.',
      },
    },
  },
};

export const SearchWithFilters: Story = {
  render: () => (
    <div className="space-y-4 w-96 p-6 border border-border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Search & Filter</h3>
      <Input 
        leftIcon={<Search className="h-4 w-4" />}
        placeholder="Search products..." 
        label="Search" 
        clearable
      />
      <div className="grid grid-cols-2 gap-4">
        <Input 
          placeholder="Min price" 
          label="Min Price" 
          type="number"
        />
        <Input 
          placeholder="Max price" 
          label="Max Price" 
          type="number"
        />
      </div>
      <Input 
        placeholder="Category" 
        label="Category" 
        variant="filled"
      />
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'A search interface with filters using various input configurations.',
      },
    },
  },
};
