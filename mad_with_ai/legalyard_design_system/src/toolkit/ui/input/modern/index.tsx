import { forwardRef, useState } from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { Eye, EyeOff, X, Loader2 } from "lucide-react";
import { cn } from "../../../lib/utils";
import { InputProps } from "./types";

const inputVariants = cva(
  "flex w-full rounded-lg border transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "border-border bg-background hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20",
        filled: "border-transparent bg-muted hover:bg-muted/80 focus-visible:bg-background focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20",
        outline: "border-2 border-border bg-transparent hover:border-primary/50 focus-visible:border-primary focus-visible:ring-2 focus-visible:ring-primary/20",
        ghost: "border-transparent bg-transparent hover:bg-muted/50 focus-visible:bg-muted focus-visible:border-border",
      },
      size: {
        sm: "h-8 px-3 text-sm",
        md: "h-10 px-3 text-sm",
        lg: "h-12 px-4 text-base",
      },
      state: {
        default: "",
        error: "border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20",
        success: "border-green-500 focus-visible:border-green-500 focus-visible:ring-green-500/20",
        warning: "border-yellow-500 focus-visible:border-yellow-500 focus-visible:ring-yellow-500/20",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      state: "default",
    },
  }
);

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant,
      size,
      state,
      type = "text",
      label,
      description,
      error,
      success,
      warning,
      leftIcon,
      rightIcon,
      leftAddon,
      rightAddon,
      fullWidth = true,
      loading,
      clearable,
      onClear,
      containerClassName,
      labelClassName,
      helperClassName,
      value,
      onChange,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const [isFocused, setIsFocused] = useState(false);

    // Determine the actual input type
    const inputType = type === "password" && showPassword ? "text" : type;

    // Determine the current state
    const currentState = error ? "error" : success ? "success" : warning ? "warning" : state;

    // Helper text to display
    const helperText = error || success || warning || description;

    // Helper text color
    const helperColor = error 
      ? "text-red-600 dark:text-red-400" 
      : success 
      ? "text-green-600 dark:text-green-400" 
      : warning 
      ? "text-yellow-600 dark:text-yellow-400" 
      : "text-muted-foreground";

    const handleClear = () => {
      if (onClear) {
        onClear();
      } else if (onChange) {
        onChange({ target: { value: "" } } as any);
      }
    };

    return (
      <div className={cn("space-y-2", fullWidth && "w-full", containerClassName)}>
        {label && (
          <label className={cn("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70", labelClassName)}>
            {label}
          </label>
        )}
        
        <div className="relative">
          {/* Left addon */}
          {leftAddon && (
            <div className="absolute left-0 top-0 h-full flex items-center">
              {leftAddon}
            </div>
          )}
          
          {/* Left icon */}
          {leftIcon && !leftAddon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : leftIcon}
            </div>
          )}
          
          <input
            type={inputType}
            className={cn(
              inputVariants({ variant, size, state: currentState }),
              leftIcon && !leftAddon && "pl-10",
              (rightIcon || clearable || type === "password") && !rightAddon && "pr-10",
              leftAddon && "pl-12",
              rightAddon && "pr-12",
              className
            )}
            ref={ref}
            value={value}
            onChange={onChange}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            {...props}
          />
          
          {/* Right side icons */}
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-1">
            {/* Clear button */}
            {clearable && value && (
              <button
                type="button"
                onClick={handleClear}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            )}
            
            {/* Password toggle */}
            {type === "password" && (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            )}
            
            {/* Right icon */}
            {rightIcon && type !== "password" && !clearable && (
              <div className="text-muted-foreground">
                {rightIcon}
              </div>
            )}
          </div>
          
          {/* Right addon */}
          {rightAddon && (
            <div className="absolute right-0 top-0 h-full flex items-center">
              {rightAddon}
            </div>
          )}
        </div>
        
        {/* Helper text */}
        {helperText && (
          <p className={cn("text-sm", helperColor, helperClassName)}>
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export { Input, inputVariants };
export default Input;
