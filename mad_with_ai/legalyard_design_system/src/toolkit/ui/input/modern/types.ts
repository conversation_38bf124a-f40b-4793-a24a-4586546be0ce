import { InputHTMLAttributes, ReactNode } from "react";

export type InputVariant = "default" | "filled" | "outline" | "ghost";
export type InputSize = "sm" | "md" | "lg";
export type InputState = "default" | "error" | "success" | "warning";

export interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, "size"> {
  variant?: InputVariant;
  size?: InputSize;
  state?: InputState;
  label?: string;
  description?: string;
  error?: string;
  success?: string;
  warning?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  leftAddon?: ReactNode;
  rightAddon?: ReactNode;
  fullWidth?: boolean;
  loading?: boolean;
  clearable?: boolean;
  onClear?: () => void;
  containerClassName?: string;
  labelClassName?: string;
  helperClassName?: string;
}

// Legacy types for backward compatibility
export interface InputFieldTypes extends InputProps {
  label?: {
    text?: string;
    dynamic?: boolean;
    required?: boolean;
  };
  attributes?: {
    prefix?: {
      icon?: any;
    };
    suffix?: {
      icon?: any;
    };
  };
  passwordViewToggle?: boolean;
  countrySelector?: any;
  onFocus?: (focused: boolean) => void;
  onBlur?: (focused: boolean) => void;
}
