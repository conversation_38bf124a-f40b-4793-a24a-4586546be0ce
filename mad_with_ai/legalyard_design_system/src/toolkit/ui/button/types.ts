import { ButtonHTMLAttributes, ReactNode } from "react";
import { VariantProps } from "class-variance-authority";

export type ButtonVariant =
  | "primary"
  | "secondary"
  | "accent"
  | "success"
  | "warning"
  | "error"
  | "ghost"
  | "outline"
  | "link";

export type ButtonSize = "sm" | "md" | "lg" | "xl" | "full";

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children?: ReactNode;
  className?: string;
  loading?: boolean;
  disabled?: boolean;
  variant?: ButtonVariant;
  size?: ButtonSize;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  fullWidth?: boolean;
  asChild?: boolean;
}

// Legacy types for backward compatibility
export type AllowedVariantsType = ButtonVariant;
export type AllowedSize = "full" | "half";
export interface ButtonType extends ButtonProps {
  effect?: boolean;
  rightArrow?: boolean;
}
