import { forwardRef } from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2 } from "lucide-react";
import { cn } from "../../lib/utils";
import { ButtonProps, ButtonType } from "./types";

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden",
  {
    variants: {
      variant: {
        primary: "bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5",
        secondary: "bg-gradient-to-r from-amber-500 to-yellow-500 text-white hover:from-amber-600 hover:to-yellow-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5",
        accent: "bg-gradient-to-r from-blue-600 to-sky-500 text-white hover:from-blue-700 hover:to-sky-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5",
        success: "bg-gradient-to-r from-green-600 to-emerald-500 text-white hover:from-green-700 hover:to-emerald-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5",
        warning: "bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5",
        error: "bg-gradient-to-r from-red-600 to-rose-500 text-white hover:from-red-700 hover:to-rose-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5",
        outline: "border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline p-0 h-auto",
      },
      size: {
        sm: "h-8 px-3 text-xs",
        md: "h-10 px-4 py-2",
        lg: "h-12 px-6 text-base",
        xl: "h-14 px-8 text-lg",
        full: "w-full h-10 px-4 py-2",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
    },
  }
);

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      loading,
      disabled,
      leftIcon,
      rightIcon,
      fullWidth,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <button
        className={cn(
          buttonVariants({ variant, size: fullWidth ? "full" : size }),
          loading && "cursor-not-allowed",
          className
        )}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {/* Shimmer effect for gradient buttons */}
        {(variant === "primary" || variant === "secondary" || variant === "accent" ||
          variant === "success" || variant === "warning" || variant === "error") && (
          <div className="absolute inset-0 -top-px bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500 transform -skew-x-12 -translate-x-full hover:translate-x-full" />
        )}

        {loading && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}

        {leftIcon && !loading && (
          <span className="mr-2 flex-shrink-0">{leftIcon}</span>
        )}

        {children}

        {rightIcon && (
          <span className="ml-2 flex-shrink-0 transition-transform group-hover:translate-x-0.5">
            {rightIcon}
          </span>
        )}
      </button>
    );
  }
);

Button.displayName = "Button";

// Legacy Button component for backward compatibility
const LegacyButton = forwardRef<HTMLButtonElement, ButtonType>(
  (
    {
      loading,
      variant,
      size,
      className,
      children,
      rightArrow,
      effect,
      ...rest
    },
    ref
  ) => {
    // Map legacy props to new props
    const newVariant = variant === "dark" ? "outline" :
                      variant === "action" ? "ghost" :
                      variant || "primary";

    const newSize = size === "full" ? "full" :
                   size === "half" ? "md" : "md";

    return (
      <Button
        ref={ref}
        variant={newVariant as any}
        size={newSize as any}
        loading={loading}
        className={className}
        rightIcon={rightArrow ? <span>→</span> : undefined}
        {...rest}
      >
        {children}
      </Button>
    );
  }
);

LegacyButton.displayName = "LegacyButton";

export { buttonVariants, Button, LegacyButton };
export default Button;
