import type { <PERSON>a, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Play, Download, Heart, ArrowRight, Plus, Loader2 } from 'lucide-react';
import { Button } from './index';

const meta = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A versatile button component with multiple variants, sizes, and states built with the Legalyard design system.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'accent', 'success', 'warning', 'error', 'outline', 'ghost', 'link'],
      description: 'The visual style variant of the button',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl', 'full'],
      description: 'The size of the button',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the button is in loading state',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the button is disabled',
    },
    fullWidth: {
      control: 'boolean',
      description: 'Whether the button should take full width',
    },
    onClick: { action: 'clicked' },
  },
  args: {
    onClick: fn(),
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic variants
export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button',
  },
};

export const Accent: Story = {
  args: {
    variant: 'accent',
    children: 'Accent Button',
  },
};

export const Success: Story = {
  args: {
    variant: 'success',
    children: 'Success Button',
  },
};

export const Warning: Story = {
  args: {
    variant: 'warning',
    children: 'Warning Button',
  },
};

export const Error: Story = {
  args: {
    variant: 'error',
    children: 'Error Button',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Outline Button',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Ghost Button',
  },
};

export const Link: Story = {
  args: {
    variant: 'link',
    children: 'Link Button',
  },
};

// Sizes
export const Small: Story = {
  args: {
    size: 'sm',
    children: 'Small Button',
  },
};

export const Medium: Story = {
  args: {
    size: 'md',
    children: 'Medium Button',
  },
};

export const Large: Story = {
  args: {
    size: 'lg',
    children: 'Large Button',
  },
};

export const ExtraLarge: Story = {
  args: {
    size: 'xl',
    children: 'Extra Large Button',
  },
};

export const FullWidth: Story = {
  args: {
    size: 'full',
    children: 'Full Width Button',
  },
  parameters: {
    layout: 'padded',
  },
};

// States
export const Loading: Story = {
  args: {
    loading: true,
    children: 'Loading...',
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: 'Disabled Button',
  },
};

// With Icons
export const WithLeftIcon: Story = {
  args: {
    leftIcon: <Play className="h-4 w-4" />,
    children: 'Play Video',
  },
};

export const WithRightIcon: Story = {
  args: {
    rightIcon: <Download className="h-4 w-4" />,
    children: 'Download',
    variant: 'accent',
  },
};

export const IconOnly: Story = {
  args: {
    children: <Plus className="h-4 w-4" />,
    variant: 'ghost',
    size: 'md',
    className: 'w-10 h-10 p-0',
  },
};

export const LoadingWithIcon: Story = {
  args: {
    loading: true,
    leftIcon: <Download className="h-4 w-4" />,
    children: 'Downloading...',
    variant: 'primary',
  },
};

// Interactive Examples
export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="accent">Accent</Button>
      <Button variant="success">Success</Button>
      <Button variant="warning">Warning</Button>
      <Button variant="error">Error</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="link">Link</Button>
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'All available button variants displayed together.',
      },
    },
  },
};

export const AllSizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="sm">Small</Button>
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
      <Button size="xl">Extra Large</Button>
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'All available button sizes displayed together.',
      },
    },
  },
};

export const WithIcons: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button leftIcon={<Play className="h-4 w-4" />}>
        Play Video
      </Button>
      <Button variant="accent" rightIcon={<Download className="h-4 w-4" />}>
        Download
      </Button>
      <Button variant="outline" leftIcon={<Heart className="h-4 w-4" />}>
        Like
      </Button>
      <Button variant="ghost" className="w-10 h-10 p-0">
        <Plus className="h-4 w-4" />
      </Button>
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'Buttons with various icon configurations.',
      },
    },
  },
};

export const ButtonStates: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button>Normal</Button>
      <Button loading>Loading</Button>
      <Button disabled>Disabled</Button>
      <Button loading disabled>
        Loading & Disabled
      </Button>
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'Different button states including loading and disabled.',
      },
    },
  },
};

// Real-world examples
export const CallToAction: Story = {
  render: () => (
    <div className="text-center space-y-4 p-8 bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-950/20 dark:to-amber-950/20 rounded-lg">
      <h2 className="text-2xl font-bold">Ready to get started?</h2>
      <p className="text-muted-foreground">Join thousands of developers using our design system.</p>
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button size="lg" rightIcon={<ArrowRight className="h-4 w-4" />}>
          Get Started Free
        </Button>
        <Button variant="outline" size="lg">
          View Documentation
        </Button>
      </div>
    </div>
  ),
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        story: 'A real-world call-to-action section using buttons.',
      },
    },
  },
};
