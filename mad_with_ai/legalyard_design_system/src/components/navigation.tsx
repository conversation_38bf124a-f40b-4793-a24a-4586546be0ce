"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { ThemeToggle } from "./theme-toggle"
import { MasterSearch } from "./search"
import { MobileNav } from "./mobile-nav"
import { 
  Home, 
  Download, 
  Palette, 
  Wrench, 
  Zap, 
  Shield, 
  HelpCircle,
  Layout,
  Paintbrush
} from "lucide-react"

const navigation = [
  {
    name: "Home",
    href: "/",
    icon: Home,
  },
  {
    name: "Installation",
    href: "/installation",
    icon: Download,
  },
  {
    name: "UI Components",
    href: "/components",
    icon: Palette,
  },
  {
    name: "Layouts",
    href: "/layouts",
    icon: Layout,
  },
  {
    name: "Styles",
    href: "/styles",
    icon: Paintbrush,
  },
  {
    name: "Utils",
    href: "/utils",
    icon: Wrench,
  },
  {
    name: "Hooks",
    href: "/hooks",
    icon: Zap,
  },
  {
    name: "Validators",
    href: "/validators",
    icon: Shield,
  },
  {
    name: "Helpers",
    href: "/helpers",
    icon: HelpCircle,
  },
]

export function Navigation() {
  const pathname = usePathname()

  return (
    <nav className="legalyard-nav sticky top-0 z-50 flex items-center justify-between p-4">
      <div className="flex items-center space-x-8">
        <Link href="/" className="flex items-center space-x-3 group">
          <div className="h-10 w-10 legalyard-gradient rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
            <span className="text-white font-bold text-lg">L</span>
          </div>
          <div className="flex flex-col">
            <span className="font-bold text-xl bg-gradient-to-r from-orange-600 to-amber-500 bg-clip-text text-transparent">
              Legalyard
            </span>
            <span className="text-xs text-muted-foreground -mt-1">Design System</span>
          </div>
        </Link>
        
        <div className="hidden md:flex items-center space-x-6">
          {navigation.map((item) => {
            const Icon = item.icon
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-accent/50",
                  pathname === item.href
                    ? "text-primary bg-accent/30 shadow-sm"
                    : "text-muted-foreground hover:text-foreground"
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{item.name}</span>
              </Link>
            )
          })}
        </div>
      </div>
      
      <div className="flex items-center space-x-4">
        <MasterSearch />
        <ThemeToggle />
        <MobileNav />
      </div>
    </nav>
  )
}
