"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { ThemeToggle } from "./theme-toggle"
import { MasterSearch } from "./search"
import { MobileNav } from "./mobile-nav"
import { 
  Home, 
  Download, 
  Palette, 
  Wrench, 
  Zap, 
  Shield, 
  HelpCircle,
  Layout,
  Paintbrush
} from "lucide-react"

const navigation = [
  {
    name: "Home",
    href: "/",
    icon: Home,
  },
  {
    name: "Installation",
    href: "/installation",
    icon: Download,
  },
  {
    name: "UI Components",
    href: "/components",
    icon: Palette,
  },
  {
    name: "Layouts",
    href: "/layouts",
    icon: Layout,
  },
  {
    name: "Styles",
    href: "/styles",
    icon: Paintbrush,
  },
  {
    name: "Utils",
    href: "/utils",
    icon: Wrench,
  },
  {
    name: "Hooks",
    href: "/hooks",
    icon: Zap,
  },
  {
    name: "Valida<PERSON>",
    href: "/validators",
    icon: Shield,
  },
  {
    name: "Helpers",
    href: "/helpers",
    icon: HelpCircle,
  },
]

export function Navigation() {
  const pathname = usePathname()

  return (
    <nav className="flex items-center justify-between p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center space-x-8">
        <Link href="/" className="flex items-center space-x-2">
          <div className="h-8 w-8 bg-primary rounded-md flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">LDS</span>
          </div>
          <span className="font-bold text-lg">Legalyard Design System</span>
        </Link>
        
        <div className="hidden md:flex items-center space-x-6">
          {navigation.map((item) => {
            const Icon = item.icon
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center space-x-2 text-sm font-medium transition-colors hover:text-primary",
                  pathname === item.href
                    ? "text-primary"
                    : "text-muted-foreground"
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{item.name}</span>
              </Link>
            )
          })}
        </div>
      </div>
      
      <div className="flex items-center space-x-4">
        <MasterSearch />
        <ThemeToggle />
        <MobileNav />
      </div>
    </nav>
  )
}
