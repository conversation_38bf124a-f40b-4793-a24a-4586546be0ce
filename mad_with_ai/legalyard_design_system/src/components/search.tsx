"use client"

import { useState, useRef, useEffect } from "react"
import { Search as SearchIcon, X } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface SearchResult {
  title: string
  description: string
  href: string
  category: string
}

const searchData: SearchResult[] = [
  // Components
  { title: "Button", description: "Customizable button component with multiple variants", href: "/components/button", category: "Components" },
  { title: "Input", description: "Text input with validation and styling options", href: "/components/input", category: "Components" },
  { title: "Modal", description: "Modal dialog component", href: "/components/modal", category: "Components" },
  { title: "Card", description: "Card container component", href: "/components/card", category: "Components" },
  { title: "Table", description: "Data table component", href: "/components/table", category: "Components" },
  
  // Utils
  { title: "formatCurrency", description: "Format numbers as currency with locale support", href: "/utils/formatcurrency", category: "Utils" },
  { title: "formatDate", description: "Format dates with various patterns", href: "/utils/formatdate", category: "Utils" },
  { title: "apiClient", description: "Configured HTTP client with interceptors", href: "/utils/apiclient", category: "Utils" },
  { title: "copyToClipboard", description: "Copy text to clipboard", href: "/utils/copytoclipboard", category: "Utils" },
  
  // Hooks
  { title: "useLocalStorage", description: "Sync state with localStorage", href: "/hooks/uselocalstorage", category: "Hooks" },
  { title: "useDebounce", description: "Debounce a value with configurable delay", href: "/hooks/usedebounce", category: "Hooks" },
  { title: "useClickOutside", description: "Detect clicks outside an element", href: "/hooks/useclickoutside", category: "Hooks" },
  { title: "useFetch", description: "Fetch data with loading and error states", href: "/hooks/usefetch", category: "Hooks" },
  
  // Validators
  { title: "isEmail", description: "Validate email address format", href: "/validators/isemail", category: "Validators" },
  { title: "isPhoneNumber", description: "Validate phone number format", href: "/validators/isphonenumber", category: "Validators" },
  { title: "isStrongPassword", description: "Validate password strength", href: "/validators/isstrongpassword", category: "Validators" },
  
  // Helpers
  { title: "clamp", description: "Clamp a number between min and max values", href: "/helpers/clamp", category: "Helpers" },
  { title: "chunk", description: "Split array into chunks of specified size", href: "/helpers/chunk", category: "Helpers" },
  { title: "capitalize", description: "Capitalize first letter of string", href: "/helpers/capitalize", category: "Helpers" },
  
  // Layouts
  { title: "AppLayout", description: "Main application layout with header, sidebar, and content area", href: "/layouts/applayout", category: "Layouts" },
  { title: "Grid", description: "Responsive grid system with customizable columns", href: "/layouts/grid", category: "Layouts" },
  { title: "Container", description: "Responsive container with max-width constraints", href: "/layouts/container", category: "Layouts" },
  
  // Pages
  { title: "Installation", description: "Get started with Legalyard Design System", href: "/installation", category: "Getting Started" },
  { title: "Styles & Theming", description: "Design tokens, color palettes, typography", href: "/styles", category: "Design" },
]

export function MasterSearch() {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<SearchResult[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const resultsRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (query.trim()) {
      const filtered = searchData.filter(item =>
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase()) ||
        item.category.toLowerCase().includes(query.toLowerCase())
      ).slice(0, 8)
      setResults(filtered)
      setSelectedIndex(-1)
    } else {
      setResults([])
      setSelectedIndex(-1)
    }
  }, [query])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setIsOpen(true)
        setTimeout(() => inputRef.current?.focus(), 100)
      }
      
      if (e.key === "Escape") {
        setIsOpen(false)
        setQuery("")
      }
      
      if (isOpen && results.length > 0) {
        if (e.key === "ArrowDown") {
          e.preventDefault()
          setSelectedIndex(prev => (prev + 1) % results.length)
        }
        
        if (e.key === "ArrowUp") {
          e.preventDefault()
          setSelectedIndex(prev => prev <= 0 ? results.length - 1 : prev - 1)
        }
        
        if (e.key === "Enter" && selectedIndex >= 0) {
          e.preventDefault()
          window.location.href = results[selectedIndex].href
        }
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [isOpen, results, selectedIndex])

  const handleClose = () => {
    setIsOpen(false)
    setQuery("")
    setSelectedIndex(-1)
  }

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center space-x-2 px-3 py-2 text-sm text-muted-foreground border border-border rounded-md hover:border-primary/50 transition-colors"
      >
        <SearchIcon className="h-4 w-4" />
        <span>Search...</span>
        <kbd className="hidden sm:inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground">
          <span className="text-xs">⌘</span>K
        </kbd>
      </button>
    )
  }

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
      <div className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg">
        <div className="flex items-center space-x-2">
          <SearchIcon className="h-4 w-4 text-muted-foreground" />
          <input
            ref={inputRef}
            type="text"
            placeholder="Search documentation..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="flex-1 bg-transparent text-sm outline-none placeholder:text-muted-foreground"
            autoFocus
          />
          <button
            onClick={handleClose}
            className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
        
        {results.length > 0 && (
          <div ref={resultsRef} className="max-h-80 overflow-y-auto">
            <div className="space-y-1">
              {results.map((result, index) => (
                <Link
                  key={result.href}
                  href={result.href}
                  onClick={handleClose}
                  className={cn(
                    "block rounded-md p-3 text-sm transition-colors",
                    index === selectedIndex
                      ? "bg-accent text-accent-foreground"
                      : "hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <div className="flex items-center justify-between">
                    <div className="font-medium">{result.title}</div>
                    <div className="text-xs text-muted-foreground">{result.category}</div>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {result.description}
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
        
        {query && results.length === 0 && (
          <div className="py-6 text-center text-sm text-muted-foreground">
            No results found for "{query}"
          </div>
        )}
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div>
            {results.length > 0 && (
              <span>Use ↑↓ to navigate, Enter to select</span>
            )}
          </div>
          <div>
            <kbd className="rounded border bg-muted px-1.5 py-0.5">Esc</kbd> to close
          </div>
        </div>
      </div>
    </div>
  )
}
