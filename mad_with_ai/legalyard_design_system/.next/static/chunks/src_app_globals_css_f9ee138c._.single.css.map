{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.8 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --color-red-50: oklch(97.1% 0.013 17.38);\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-400: oklch(70.4% 0.191 22.216);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-700: oklch(50.5% 0.213 27.518);\n    --color-red-800: oklch(44.4% 0.177 26.899);\n    --color-red-900: oklch(39.6% 0.141 25.723);\n    --color-red-950: oklch(25.8% 0.092 26.042);\n    --color-orange-50: oklch(98% 0.016 73.684);\n    --color-orange-100: oklch(95.4% 0.038 75.164);\n    --color-orange-200: oklch(90.1% 0.076 70.697);\n    --color-orange-400: oklch(75% 0.183 55.934);\n    --color-orange-500: oklch(70.5% 0.213 47.604);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-orange-700: oklch(55.3% 0.195 38.402);\n    --color-orange-800: oklch(47% 0.157 37.304);\n    --color-orange-900: oklch(40.8% 0.123 38.172);\n    --color-orange-950: oklch(26.6% 0.079 36.259);\n    --color-amber-50: oklch(98.7% 0.022 95.277);\n    --color-amber-100: oklch(96.2% 0.059 95.617);\n    --color-amber-400: oklch(82.8% 0.189 84.429);\n    --color-amber-500: oklch(76.9% 0.188 70.08);\n    --color-amber-600: oklch(66.6% 0.179 58.318);\n    --color-amber-900: oklch(41.4% 0.112 45.904);\n    --color-amber-950: oklch(27.9% 0.077 45.635);\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\n    --color-yellow-950: oklch(28.6% 0.066 53.813);\n    --color-green-50: oklch(98.2% 0.018 155.826);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-200: oklch(92.5% 0.084 155.995);\n    --color-green-400: oklch(79.2% 0.209 151.711);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-700: oklch(52.7% 0.154 150.069);\n    --color-green-800: oklch(44.8% 0.119 151.328);\n    --color-green-900: oklch(39.3% 0.095 152.535);\n    --color-green-950: oklch(26.6% 0.065 152.934);\n    --color-emerald-100: oklch(95% 0.052 163.051);\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\n    --color-emerald-900: oklch(37.8% 0.077 168.94);\n    --color-sky-50: oklch(97.7% 0.013 236.62);\n    --color-sky-100: oklch(95.1% 0.026 236.824);\n    --color-sky-300: oklch(82.8% 0.111 230.318);\n    --color-sky-500: oklch(68.5% 0.169 237.323);\n    --color-sky-600: oklch(58.8% 0.158 241.966);\n    --color-sky-900: oklch(39.1% 0.09 240.876);\n    --color-blue-50: oklch(97% 0.014 254.604);\n    --color-blue-100: oklch(93.2% 0.032 255.585);\n    --color-blue-200: oklch(88.2% 0.059 254.128);\n    --color-blue-400: oklch(70.7% 0.165 254.624);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-blue-800: oklch(42.4% 0.199 265.638);\n    --color-blue-900: oklch(37.9% 0.146 265.522);\n    --color-blue-950: oklch(28.2% 0.091 267.935);\n    --color-purple-100: oklch(94.6% 0.033 307.174);\n    --color-purple-400: oklch(71.4% 0.203 305.504);\n    --color-purple-800: oklch(43.8% 0.218 303.724);\n    --color-purple-900: oklch(38.1% 0.176 304.987);\n    --color-rose-50: oklch(96.9% 0.015 12.422);\n    --color-rose-500: oklch(64.5% 0.246 16.439);\n    --color-rose-600: oklch(58.6% 0.253 17.585);\n    --color-rose-700: oklch(51.4% 0.222 16.935);\n    --color-slate-50: oklch(98.4% 0.003 247.858);\n    --color-slate-100: oklch(96.8% 0.007 247.896);\n    --color-slate-200: oklch(92.9% 0.013 255.508);\n    --color-slate-300: oklch(86.9% 0.022 252.894);\n    --color-slate-400: oklch(70.4% 0.04 256.788);\n    --color-slate-500: oklch(55.4% 0.046 257.417);\n    --color-slate-600: oklch(44.6% 0.043 257.281);\n    --color-slate-700: oklch(37.2% 0.044 257.287);\n    --color-slate-900: oklch(20.8% 0.042 265.755);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-zinc-100: oklch(96.7% 0.001 286.375);\n    --color-zinc-200: oklch(92% 0.004 286.32);\n    --color-zinc-300: oklch(87.1% 0.006 286.286);\n    --color-zinc-400: oklch(70.5% 0.015 286.067);\n    --color-zinc-500: oklch(55.2% 0.016 285.938);\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\n    --color-zinc-700: oklch(37% 0.013 285.805);\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --breakpoint-sm: 40rem;\n    --breakpoint-md: 48rem;\n    --breakpoint-lg: 64rem;\n    --breakpoint-xl: 80rem;\n    --breakpoint-2xl: 96rem;\n    --container-xs: 20rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-2xl: 42rem;\n    --container-4xl: 56rem;\n    --container-6xl: 72rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --text-7xl: 4.5rem;\n    --text-7xl--line-height: 1;\n    --font-weight-normal: 400;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --tracking-tight: -0.025em;\n    --tracking-wide: 0.025em;\n    --leading-tight: 1.25;\n    --leading-snug: 1.375;\n    --leading-normal: 1.5;\n    --leading-relaxed: 1.625;\n    --radius-sm: 0.25rem;\n    --radius-md: 0.375rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-sm: 8px;\n    --blur-md: 12px;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .\\!visible {\n    visibility: visible !important;\n  }\n  .collapse {\n    visibility: collapse;\n  }\n  .invisible {\n    visibility: hidden;\n  }\n  .visible {\n    visibility: visible;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .sticky {\n    position: sticky;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .\\!-top-\\[0\\.03rem\\] {\n    top: calc(0.03rem * -1) !important;\n  }\n  .\\!-top-\\[30\\%\\] {\n    top: calc(30% * -1) !important;\n  }\n  .-top-1 {\n    top: calc(var(--spacing) * -1);\n  }\n  .-top-2 {\n    top: calc(var(--spacing) * -2);\n  }\n  .-top-4 {\n    top: calc(var(--spacing) * -4);\n  }\n  .-top-24 {\n    top: calc(var(--spacing) * -24);\n  }\n  .-top-px {\n    top: -1px;\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-2 {\n    top: calc(var(--spacing) * 2);\n  }\n  .top-2\\.5 {\n    top: calc(var(--spacing) * 2.5);\n  }\n  .top-3 {\n    top: calc(var(--spacing) * 3);\n  }\n  .top-5 {\n    top: calc(var(--spacing) * 5);\n  }\n  .top-\\[50\\%\\] {\n    top: 50%;\n  }\n  .top-full {\n    top: 100%;\n  }\n  .-right-1 {\n    right: calc(var(--spacing) * -1);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-2\\.5 {\n    right: calc(var(--spacing) * 2.5);\n  }\n  .right-3 {\n    right: calc(var(--spacing) * 3);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .right-full {\n    right: 100%;\n  }\n  .-bottom-4 {\n    bottom: calc(var(--spacing) * -4);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-2\\.5 {\n    bottom: calc(var(--spacing) * 2.5);\n  }\n  .bottom-full {\n    bottom: 100%;\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-2\\.5 {\n    left: calc(var(--spacing) * 2.5);\n  }\n  .left-3 {\n    left: calc(var(--spacing) * 3);\n  }\n  .left-\\[50\\%\\] {\n    left: 50%;\n  }\n  .left-full {\n    left: 100%;\n  }\n  .-z-10 {\n    z-index: calc(10 * -1);\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-30 {\n    z-index: 30;\n  }\n  .z-40 {\n    z-index: 40;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-\\[2\\] {\n    z-index: 2;\n  }\n  .z-\\[5\\] {\n    z-index: 5;\n  }\n  .z-\\[7\\] {\n    z-index: 7;\n  }\n  .z-\\[9\\] {\n    z-index: 9;\n  }\n  .z-\\[22\\] {\n    z-index: 22;\n  }\n  .z-\\[35\\] {\n    z-index: 35;\n  }\n  .z-\\[51\\] {\n    z-index: 51;\n  }\n  .z-\\[112\\] {\n    z-index: 112;\n  }\n  .z-\\[115\\] {\n    z-index: 115;\n  }\n  .order-1 {\n    order: 1;\n  }\n  .col-span-4 {\n    grid-column: span 4 / span 4;\n  }\n  .col-span-8 {\n    grid-column: span 8 / span 8;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .m-6 {\n    margin: calc(var(--spacing) * 6);\n  }\n  .m-auto {\n    margin: auto;\n  }\n  .mx-1 {\n    margin-inline: calc(var(--spacing) * 1);\n  }\n  .mx-2 {\n    margin-inline: calc(var(--spacing) * 2);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .\\!my-0 {\n    margin-block: calc(var(--spacing) * 0) !important;\n  }\n  .my-0 {\n    margin-block: calc(var(--spacing) * 0);\n  }\n  .my-0\\.5 {\n    margin-block: calc(var(--spacing) * 0.5);\n  }\n  .my-1 {\n    margin-block: calc(var(--spacing) * 1);\n  }\n  .my-2 {\n    margin-block: calc(var(--spacing) * 2);\n  }\n  .my-3 {\n    margin-block: calc(var(--spacing) * 3);\n  }\n  .my-4 {\n    margin-block: calc(var(--spacing) * 4);\n  }\n  .my-auto {\n    margin-block: auto;\n  }\n  .ms-4 {\n    margin-inline-start: calc(var(--spacing) * 4);\n  }\n  .ms-auto {\n    margin-inline-start: auto;\n  }\n  .me-1\\.5 {\n    margin-inline-end: calc(var(--spacing) * 1.5);\n  }\n  .me-auto {\n    margin-inline-end: auto;\n  }\n  .-mt-1 {\n    margin-top: calc(var(--spacing) * -1);\n  }\n  .mt-0 {\n    margin-top: calc(var(--spacing) * 0);\n  }\n  .mt-0\\.5 {\n    margin-top: calc(var(--spacing) * 0.5);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-5 {\n    margin-top: calc(var(--spacing) * 5);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mt-10 {\n    margin-top: calc(var(--spacing) * 10);\n  }\n  .mt-12 {\n    margin-top: calc(var(--spacing) * 12);\n  }\n  .mt-16 {\n    margin-top: calc(var(--spacing) * 16);\n  }\n  .mt-\\[0\\.04rem\\] {\n    margin-top: 0.04rem;\n  }\n  .mt-\\[0\\.15rem\\] {\n    margin-top: 0.15rem;\n  }\n  .mt-\\[1px\\] {\n    margin-top: 1px;\n  }\n  .mt-auto {\n    margin-top: auto;\n  }\n  .\\!mr-0\\.5 {\n    margin-right: calc(var(--spacing) * 0.5) !important;\n  }\n  .\\!mr-2 {\n    margin-right: calc(var(--spacing) * 2) !important;\n  }\n  .mr-1 {\n    margin-right: calc(var(--spacing) * 1);\n  }\n  .mr-1\\.5 {\n    margin-right: calc(var(--spacing) * 1.5);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mb-0 {\n    margin-bottom: calc(var(--spacing) * 0);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-5 {\n    margin-bottom: calc(var(--spacing) * 5);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-10 {\n    margin-bottom: calc(var(--spacing) * 10);\n  }\n  .mb-12 {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n  .mb-\\[0\\.1rem\\] {\n    margin-bottom: 0.1rem;\n  }\n  .mb-auto {\n    margin-bottom: auto;\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-1\\.5 {\n    margin-left: calc(var(--spacing) * 1.5);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-6 {\n    margin-left: calc(var(--spacing) * 6);\n  }\n  .ml-auto {\n    margin-left: auto;\n  }\n  .box-border {\n    box-sizing: border-box;\n  }\n  .line-clamp-2 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 2;\n  }\n  .block {\n    display: block;\n  }\n  .contents {\n    display: contents;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline {\n    display: inline;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .table {\n    display: table;\n  }\n  .aspect-auto {\n    aspect-ratio: auto;\n  }\n  .aspect-square {\n    aspect-ratio: 1 / 1;\n  }\n  .\\!h-4 {\n    height: calc(var(--spacing) * 4) !important;\n  }\n  .h-0\\.5 {\n    height: calc(var(--spacing) * 0.5);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-3\\.5 {\n    height: calc(var(--spacing) * 3.5);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-9 {\n    height: calc(var(--spacing) * 9);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-11 {\n    height: calc(var(--spacing) * 11);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-14 {\n    height: calc(var(--spacing) * 14);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-20 {\n    height: calc(var(--spacing) * 20);\n  }\n  .h-32 {\n    height: calc(var(--spacing) * 32);\n  }\n  .h-40 {\n    height: calc(var(--spacing) * 40);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-\\[1\\.2rem\\] {\n    height: 1.2rem;\n  }\n  .h-\\[1px\\] {\n    height: 1px;\n  }\n  .h-\\[25\\%\\] {\n    height: 25%;\n  }\n  .h-\\[350px\\] {\n    height: 350px;\n  }\n  .h-auto {\n    height: auto;\n  }\n  .h-fit {\n    height: fit-content;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-max {\n    height: max-content;\n  }\n  .h-min {\n    height: min-content;\n  }\n  .h-screen {\n    height: 100vh;\n  }\n  .\\!max-h-screen {\n    max-height: 100vh !important;\n  }\n  .max-h-0 {\n    max-height: calc(var(--spacing) * 0);\n  }\n  .max-h-8 {\n    max-height: calc(var(--spacing) * 8);\n  }\n  .max-h-32 {\n    max-height: calc(var(--spacing) * 32);\n  }\n  .max-h-36 {\n    max-height: calc(var(--spacing) * 36);\n  }\n  .max-h-40 {\n    max-height: calc(var(--spacing) * 40);\n  }\n  .max-h-48 {\n    max-height: calc(var(--spacing) * 48);\n  }\n  .max-h-52 {\n    max-height: calc(var(--spacing) * 52);\n  }\n  .max-h-64 {\n    max-height: calc(var(--spacing) * 64);\n  }\n  .max-h-80 {\n    max-height: calc(var(--spacing) * 80);\n  }\n  .max-h-\\[85vh\\] {\n    max-height: 85vh;\n  }\n  .max-h-\\[95vh\\] {\n    max-height: 95vh;\n  }\n  .max-h-\\[200px\\] {\n    max-height: 200px;\n  }\n  .max-h-full {\n    max-height: 100%;\n  }\n  .max-h-max {\n    max-height: max-content;\n  }\n  .min-h-9 {\n    min-height: calc(var(--spacing) * 9);\n  }\n  .min-h-10 {\n    min-height: calc(var(--spacing) * 10);\n  }\n  .min-h-12 {\n    min-height: calc(var(--spacing) * 12);\n  }\n  .min-h-56 {\n    min-height: calc(var(--spacing) * 56);\n  }\n  .min-h-\\[1em\\] {\n    min-height: 1em;\n  }\n  .min-h-\\[39px\\] {\n    min-height: 39px;\n  }\n  .min-h-\\[80px\\] {\n    min-height: 80px;\n  }\n  .min-h-\\[100px\\] {\n    min-height: 100px;\n  }\n  .min-h-full {\n    min-height: 100%;\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .\\!w-4 {\n    width: calc(var(--spacing) * 4) !important;\n  }\n  .\\!w-28 {\n    width: calc(var(--spacing) * 28) !important;\n  }\n  .\\!w-32 {\n    width: calc(var(--spacing) * 32) !important;\n  }\n  .w-0 {\n    width: calc(var(--spacing) * 0);\n  }\n  .w-1\\/2 {\n    width: calc(1/2 * 100%);\n  }\n  .w-1\\/3 {\n    width: calc(1/3 * 100%);\n  }\n  .w-1\\/4 {\n    width: calc(1/4 * 100%);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-2\\/3 {\n    width: calc(2/3 * 100%);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n  }\n  .w-3\\/4 {\n    width: calc(3/4 * 100%);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-5\\/6 {\n    width: calc(5/6 * 100%);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-9 {\n    width: calc(var(--spacing) * 9);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-14 {\n    width: calc(var(--spacing) * 14);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-20 {\n    width: calc(var(--spacing) * 20);\n  }\n  .w-24 {\n    width: calc(var(--spacing) * 24);\n  }\n  .w-48 {\n    width: calc(var(--spacing) * 48);\n  }\n  .w-72 {\n    width: calc(var(--spacing) * 72);\n  }\n  .w-80 {\n    width: calc(var(--spacing) * 80);\n  }\n  .w-96 {\n    width: calc(var(--spacing) * 96);\n  }\n  .w-\\[1\\.2rem\\] {\n    width: 1.2rem;\n  }\n  .w-\\[1px\\] {\n    width: 1px;\n  }\n  .w-\\[1rem\\] {\n    width: 1rem;\n  }\n  .w-\\[25\\%\\] {\n    width: 25%;\n  }\n  .w-\\[60vw\\] {\n    width: 60vw;\n  }\n  .w-\\[calc\\(100vw-10px\\)\\] {\n    width: calc(100vw - 10px);\n  }\n  .w-auto {\n    width: auto;\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .w-max {\n    width: max-content;\n  }\n  .w-min {\n    width: min-content;\n  }\n  .w-screen {\n    width: 100vw;\n  }\n  .\\!max-w-5 {\n    max-width: calc(var(--spacing) * 5) !important;\n  }\n  .\\!max-w-7 {\n    max-width: calc(var(--spacing) * 7) !important;\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-8 {\n    max-width: calc(var(--spacing) * 8);\n  }\n  .max-w-10 {\n    max-width: calc(var(--spacing) * 10);\n  }\n  .max-w-56 {\n    max-width: calc(var(--spacing) * 56);\n  }\n  .max-w-72 {\n    max-width: calc(var(--spacing) * 72);\n  }\n  .max-w-80 {\n    max-width: calc(var(--spacing) * 80);\n  }\n  .max-w-\\[95vw\\] {\n    max-width: 95vw;\n  }\n  .max-w-\\[100vw\\] {\n    max-width: 100vw;\n  }\n  .max-w-\\[150px\\] {\n    max-width: 150px;\n  }\n  .max-w-\\[300px\\] {\n    max-width: 300px;\n  }\n  .max-w-\\[800px\\] {\n    max-width: 800px;\n  }\n  .max-w-\\[calc\\(100vw-2rem\\)\\] {\n    max-width: calc(100vw - 2rem);\n  }\n  .max-w-full {\n    max-width: 100%;\n  }\n  .max-w-lg {\n    max-width: var(--container-lg);\n  }\n  .max-w-max {\n    max-width: max-content;\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-min {\n    max-width: min-content;\n  }\n  .max-w-screen-lg {\n    max-width: var(--breakpoint-lg);\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .max-w-xs {\n    max-width: var(--container-xs);\n  }\n  .min-w-1 {\n    min-width: calc(var(--spacing) * 1);\n  }\n  .min-w-5 {\n    min-width: calc(var(--spacing) * 5);\n  }\n  .min-w-14 {\n    min-width: calc(var(--spacing) * 14);\n  }\n  .min-w-32 {\n    min-width: calc(var(--spacing) * 32);\n  }\n  .min-w-44 {\n    min-width: calc(var(--spacing) * 44);\n  }\n  .min-w-\\[20vw\\] {\n    min-width: 20vw;\n  }\n  .min-w-\\[35vw\\] {\n    min-width: 35vw;\n  }\n  .min-w-full {\n    min-width: 100%;\n  }\n  .min-w-max {\n    min-width: max-content;\n  }\n  .min-w-min {\n    min-width: min-content;\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .grow {\n    flex-grow: 1;\n  }\n  .table-fixed {\n    table-layout: fixed;\n  }\n  .border-collapse {\n    border-collapse: collapse;\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-2\\.5 {\n    --tw-translate-x: calc(var(--spacing) * -2.5);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-full {\n    --tw-translate-x: -100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-0 {\n    --tw-translate-x: calc(var(--spacing) * 0);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-1\\/3 {\n    --tw-translate-x: calc(1/3 * 100%);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-8 {\n    --tw-translate-x: calc(var(--spacing) * 8);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-\\[-50\\%\\] {\n    --tw-translate-x: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-full {\n    --tw-translate-x: 100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .\\!translate-y-\\[0\\%\\] {\n    --tw-translate-y: 0% !important;\n    translate: var(--tw-translate-x) var(--tw-translate-y) !important;\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-\\[10\\%\\] {\n    --tw-translate-y: calc(10% * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-full {\n    --tw-translate-y: -100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-0 {\n    --tw-translate-y: calc(var(--spacing) * 0);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-1\\/3 {\n    --tw-translate-y: calc(1/3 * 100%);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-8 {\n    --tw-translate-y: calc(var(--spacing) * 8);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[-50\\%\\] {\n    --tw-translate-y: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-full {\n    --tw-translate-y: 100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .scale-0 {\n    --tw-scale-x: 0%;\n    --tw-scale-y: 0%;\n    --tw-scale-z: 0%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-95 {\n    --tw-scale-x: 95%;\n    --tw-scale-y: 95%;\n    --tw-scale-z: 95%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-100 {\n    --tw-scale-x: 100%;\n    --tw-scale-y: 100%;\n    --tw-scale-z: 100%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .rotate-0 {\n    rotate: 0deg;\n  }\n  .rotate-90 {\n    rotate: 90deg;\n  }\n  .-skew-x-12 {\n    --tw-skew-x: skewX(calc(12deg * -1));\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .\\!cursor-default {\n    cursor: default !important;\n  }\n  .cursor-auto {\n    cursor: auto;\n  }\n  .cursor-default {\n    cursor: default;\n  }\n  .cursor-not-allowed {\n    cursor: not-allowed;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .resize {\n    resize: both;\n  }\n  .resize-none {\n    resize: none;\n  }\n  .list-inside {\n    list-style-position: inside;\n  }\n  .list-disc {\n    list-style-type: disc;\n  }\n  .list-none {\n    list-style-type: none;\n  }\n  .appearance-auto {\n    appearance: auto;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n  .grid-cols-7 {\n    grid-template-columns: repeat(7, minmax(0, 1fr));\n  }\n  .\\!flex-row {\n    flex-direction: row !important;\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-col-reverse {\n    flex-direction: column-reverse;\n  }\n  .flex-row {\n    flex-direction: row;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .flex-wrap-reverse {\n    flex-wrap: wrap-reverse;\n  }\n  .place-items-center {\n    place-items: center;\n  }\n  .\\!items-center {\n    align-items: center !important;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .justify-around {\n    justify-content: space-around;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .gap-0 {\n    gap: calc(var(--spacing) * 0);\n  }\n  .gap-0\\.5 {\n    gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-2\\.5 {\n    gap: calc(var(--spacing) * 2.5);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-5 {\n    gap: calc(var(--spacing) * 5);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .gap-\\[1px\\] {\n    gap: 1px;\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-1\\.5 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-12 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-16 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 16) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .gap-x-0 {\n    column-gap: calc(var(--spacing) * 0);\n  }\n  .gap-x-0\\.5 {\n    column-gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-x-1 {\n    column-gap: calc(var(--spacing) * 1);\n  }\n  .gap-x-1\\.5 {\n    column-gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-x-2 {\n    column-gap: calc(var(--spacing) * 2);\n  }\n  .gap-x-4 {\n    column-gap: calc(var(--spacing) * 4);\n  }\n  .gap-x-\\[1px\\] {\n    column-gap: 1px;\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .gap-y-0\\.5 {\n    row-gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-y-1 {\n    row-gap: calc(var(--spacing) * 1);\n  }\n  .gap-y-2 {\n    row-gap: calc(var(--spacing) * 2);\n  }\n  .gap-y-3 {\n    row-gap: calc(var(--spacing) * 3);\n  }\n  .gap-y-4 {\n    row-gap: calc(var(--spacing) * 4);\n  }\n  .gap-y-6 {\n    row-gap: calc(var(--spacing) * 6);\n  }\n  .gap-y-10 {\n    row-gap: calc(var(--spacing) * 10);\n  }\n  .justify-self-end {\n    justify-self: flex-end;\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-auto {\n    overflow: auto;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-visible {\n    overflow: visible;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-x-hidden {\n    overflow-x: hidden;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .overscroll-x-none {\n    overscroll-behavior-x: none;\n  }\n  .\\!rounded-full {\n    border-radius: calc(infinity * 1px) !important;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-\\[4px\\] {\n    border-radius: 4px;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-md {\n    border-radius: var(--radius-md);\n  }\n  .rounded-sm {\n    border-radius: var(--radius-sm);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-\\[5px\\] {\n    border-style: var(--tw-border-style);\n    border-width: 5px;\n  }\n  .\\!border-t-0 {\n    border-top-style: var(--tw-border-style) !important;\n    border-top-width: 0px !important;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-r {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 1px;\n  }\n  .\\!border-b-0 {\n    border-bottom-style: var(--tw-border-style) !important;\n    border-bottom-width: 0px !important;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-b-2 {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 2px;\n  }\n  .border-l-2 {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 2px;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .border-none {\n    --tw-border-style: none;\n    border-style: none;\n  }\n  .border-solid {\n    --tw-border-style: solid;\n    border-style: solid;\n  }\n  .\\!border-transparent {\n    border-color: transparent !important;\n  }\n  .border-\\[rgba\\(195\\,195\\,195\\,0\\.6\\)\\] {\n    border-color: rgba(195,195,195,0.6);\n  }\n  .border-blue-200 {\n    border-color: var(--color-blue-200);\n  }\n  .border-border {\n    border-color: hsl(var(--border));\n  }\n  .border-gray-700 {\n    border-color: var(--color-gray-700);\n  }\n  .border-green-200 {\n    border-color: var(--color-green-200);\n  }\n  .border-green-500 {\n    border-color: var(--color-green-500);\n  }\n  .border-input {\n    border-color: hsl(var(--input));\n  }\n  .border-primary {\n    border-color: hsl(var(--primary));\n  }\n  .border-red-200 {\n    border-color: var(--color-red-200);\n  }\n  .border-red-500 {\n    border-color: var(--color-red-500);\n  }\n  .border-sky-300 {\n    border-color: var(--color-sky-300);\n  }\n  .border-slate-50 {\n    border-color: var(--color-slate-50);\n  }\n  .border-slate-100 {\n    border-color: var(--color-slate-100);\n  }\n  .border-slate-200 {\n    border-color: var(--color-slate-200);\n  }\n  .border-slate-300 {\n    border-color: var(--color-slate-300);\n  }\n  .border-slate-400 {\n    border-color: var(--color-slate-400);\n  }\n  .border-slate-600 {\n    border-color: var(--color-slate-600);\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .border-yellow-200 {\n    border-color: var(--color-yellow-200);\n  }\n  .border-yellow-500 {\n    border-color: var(--color-yellow-500);\n  }\n  .border-zinc-300 {\n    border-color: var(--color-zinc-300);\n  }\n  .border-x-transparent {\n    border-inline-color: transparent;\n  }\n  .border-t-transparent {\n    border-top-color: transparent;\n  }\n  .border-t-white {\n    border-top-color: var(--color-white);\n  }\n  .border-b-slate-300 {\n    border-bottom-color: var(--color-slate-300);\n  }\n  .\\!bg-sky-100 {\n    background-color: var(--color-sky-100) !important;\n  }\n  .\\!bg-slate-50 {\n    background-color: var(--color-slate-50) !important;\n  }\n  .\\!bg-slate-100 {\n    background-color: var(--color-slate-100) !important;\n  }\n  .\\!bg-white {\n    background-color: var(--color-white) !important;\n  }\n  .bg-\\[\\#dddbdd\\] {\n    background-color: #dddbdd;\n  }\n  .bg-accent {\n    background-color: hsl(var(--accent));\n  }\n  .bg-accent\\/30 {\n    background-color: hsl(var(--accent));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--accent)) 30%, transparent);\n    }\n  }\n  .bg-background {\n    background-color: hsl(var(--background));\n  }\n  .bg-background\\/80 {\n    background-color: hsl(var(--background));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--background)) 80%, transparent);\n    }\n  }\n  .bg-blue-50 {\n    background-color: var(--color-blue-50);\n  }\n  .bg-blue-100 {\n    background-color: var(--color-blue-100);\n  }\n  .bg-blue-500 {\n    background-color: var(--color-blue-500);\n  }\n  .bg-card {\n    background-color: hsl(var(--card));\n  }\n  .bg-destructive\\/10 {\n    background-color: hsl(var(--destructive));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--destructive)) 10%, transparent);\n    }\n  }\n  .bg-foreground {\n    background-color: hsl(var(--foreground));\n  }\n  .bg-gray-200 {\n    background-color: var(--color-gray-200);\n  }\n  .bg-gray-800 {\n    background-color: var(--color-gray-800);\n  }\n  .bg-gray-900 {\n    background-color: var(--color-gray-900);\n  }\n  .bg-green-50 {\n    background-color: var(--color-green-50);\n  }\n  .bg-green-100 {\n    background-color: var(--color-green-100);\n  }\n  .bg-green-500 {\n    background-color: var(--color-green-500);\n  }\n  .bg-muted {\n    background-color: hsl(var(--muted));\n  }\n  .bg-muted\\/20 {\n    background-color: hsl(var(--muted));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--muted)) 20%, transparent);\n    }\n  }\n  .bg-muted\\/30 {\n    background-color: hsl(var(--muted));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--muted)) 30%, transparent);\n    }\n  }\n  .bg-muted\\/50 {\n    background-color: hsl(var(--muted));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--muted)) 50%, transparent);\n    }\n  }\n  .bg-orange-100 {\n    background-color: var(--color-orange-100);\n  }\n  .bg-orange-500 {\n    background-color: var(--color-orange-500);\n  }\n  .bg-primary {\n    background-color: hsl(var(--primary));\n  }\n  .bg-primary\\/10 {\n    background-color: hsl(var(--primary));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--primary)) 10%, transparent);\n    }\n  }\n  .bg-primary\\/20 {\n    background-color: hsl(var(--primary));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--primary)) 20%, transparent);\n    }\n  }\n  .bg-primary\\/40 {\n    background-color: hsl(var(--primary));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--primary)) 40%, transparent);\n    }\n  }\n  .bg-purple-100 {\n    background-color: var(--color-purple-100);\n  }\n  .bg-red-50 {\n    background-color: var(--color-red-50);\n  }\n  .bg-red-100 {\n    background-color: var(--color-red-100);\n  }\n  .bg-red-500 {\n    background-color: var(--color-red-500);\n  }\n  .bg-secondary\\/10 {\n    background-color: hsl(var(--secondary));\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, hsl(var(--secondary)) 10%, transparent);\n    }\n  }\n  .bg-sky-100 {\n    background-color: var(--color-sky-100);\n  }\n  .bg-slate-50 {\n    background-color: var(--color-slate-50);\n  }\n  .bg-slate-100 {\n    background-color: var(--color-slate-100);\n  }\n  .bg-slate-200 {\n    background-color: var(--color-slate-200);\n  }\n  .bg-slate-300 {\n    background-color: var(--color-slate-300);\n  }\n  .bg-slate-400 {\n    background-color: var(--color-slate-400);\n  }\n  .bg-slate-500 {\n    background-color: var(--color-slate-500);\n  }\n  .bg-slate-600 {\n    background-color: var(--color-slate-600);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-white\\/25 {\n    background-color: color-mix(in srgb, #fff 25%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 25%, transparent);\n    }\n  }\n  .bg-yellow-50 {\n    background-color: var(--color-yellow-50);\n  }\n  .bg-yellow-100 {\n    background-color: var(--color-yellow-100);\n  }\n  .bg-yellow-500 {\n    background-color: var(--color-yellow-500);\n  }\n  .bg-zinc-200 {\n    background-color: var(--color-zinc-200);\n  }\n  .bg-zinc-700 {\n    background-color: var(--color-zinc-700);\n  }\n  .bg-gradient-to-br {\n    --tw-gradient-position: to bottom right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .from-amber-500 {\n    --tw-gradient-from: var(--color-amber-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-background {\n    --tw-gradient-from: hsl(var(--background));\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-100 {\n    --tw-gradient-from: var(--color-blue-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-600 {\n    --tw-gradient-from: var(--color-blue-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-100 {\n    --tw-gradient-from: var(--color-green-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-600 {\n    --tw-gradient-from: var(--color-green-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-50 {\n    --tw-gradient-from: var(--color-orange-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-100 {\n    --tw-gradient-from: var(--color-orange-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-400 {\n    --tw-gradient-from: var(--color-orange-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-600 {\n    --tw-gradient-from: var(--color-orange-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-600 {\n    --tw-gradient-from: var(--color-red-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-transparent {\n    --tw-gradient-from: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-500 {\n    --tw-gradient-from: var(--color-yellow-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .via-amber-500 {\n    --tw-gradient-via: var(--color-amber-500);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-white\\/20 {\n    --tw-gradient-via: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .to-amber-50 {\n    --tw-gradient-to: var(--color-amber-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-amber-100 {\n    --tw-gradient-to: var(--color-amber-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-amber-400 {\n    --tw-gradient-to: var(--color-amber-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-amber-500 {\n    --tw-gradient-to: var(--color-amber-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-600 {\n    --tw-gradient-to: var(--color-blue-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-emerald-100 {\n    --tw-gradient-to: var(--color-emerald-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-emerald-500 {\n    --tw-gradient-to: var(--color-emerald-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-muted\\/30 {\n    --tw-gradient-to: hsl(var(--muted));\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, hsl(var(--muted)) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-500 {\n    --tw-gradient-to: var(--color-orange-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-rose-500 {\n    --tw-gradient-to: var(--color-rose-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-rose-700 {\n    --tw-gradient-to: var(--color-rose-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-sky-100 {\n    --tw-gradient-to: var(--color-sky-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-sky-500 {\n    --tw-gradient-to: var(--color-sky-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-sky-600 {\n    --tw-gradient-to: var(--color-sky-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-transparent {\n    --tw-gradient-to: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-yellow-500 {\n    --tw-gradient-to: var(--color-yellow-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .bg-clip-border {\n    background-clip: border-box;\n  }\n  .bg-clip-text {\n    background-clip: text;\n  }\n  .fill-white {\n    fill: var(--color-white);\n  }\n  .fill-zinc-400 {\n    fill: var(--color-zinc-400);\n  }\n  .stroke-2 {\n    stroke-width: 2;\n  }\n  .stroke-\\[3\\] {\n    stroke-width: 3;\n  }\n  .object-contain {\n    object-fit: contain;\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-0\\.5 {\n    padding: calc(var(--spacing) * 0.5);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-1\\.5 {\n    padding: calc(var(--spacing) * 1.5);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-5 {\n    padding: calc(var(--spacing) * 5);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .px-0\\.5 {\n    padding-inline: calc(var(--spacing) * 0.5);\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-1\\.5 {\n    padding-inline: calc(var(--spacing) * 1.5);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-2\\.5 {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-5 {\n    padding-inline: calc(var(--spacing) * 5);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-8 {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n  .px-\\[12px\\] {\n    padding-inline: 12px;\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-2\\.5 {\n    padding-block: calc(var(--spacing) * 2.5);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-5 {\n    padding-block: calc(var(--spacing) * 5);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-7 {\n    padding-block: calc(var(--spacing) * 7);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .py-20 {\n    padding-block: calc(var(--spacing) * 20);\n  }\n  .py-24 {\n    padding-block: calc(var(--spacing) * 24);\n  }\n  .py-\\[1px\\] {\n    padding-block: 1px;\n  }\n  .py-\\[6px\\] {\n    padding-block: 6px;\n  }\n  .ps-1\\.5 {\n    padding-inline-start: calc(var(--spacing) * 1.5);\n  }\n  .ps-4 {\n    padding-inline-start: calc(var(--spacing) * 4);\n  }\n  .pe-2 {\n    padding-inline-end: calc(var(--spacing) * 2);\n  }\n  .pe-4 {\n    padding-inline-end: calc(var(--spacing) * 4);\n  }\n  .pt-0 {\n    padding-top: calc(var(--spacing) * 0);\n  }\n  .pt-1 {\n    padding-top: calc(var(--spacing) * 1);\n  }\n  .pt-2 {\n    padding-top: calc(var(--spacing) * 2);\n  }\n  .pt-4 {\n    padding-top: calc(var(--spacing) * 4);\n  }\n  .pt-5 {\n    padding-top: calc(var(--spacing) * 5);\n  }\n  .pt-8 {\n    padding-top: calc(var(--spacing) * 8);\n  }\n  .\\!pr-6 {\n    padding-right: calc(var(--spacing) * 6) !important;\n  }\n  .pr-1 {\n    padding-right: calc(var(--spacing) * 1);\n  }\n  .pr-3 {\n    padding-right: calc(var(--spacing) * 3);\n  }\n  .pr-10 {\n    padding-right: calc(var(--spacing) * 10);\n  }\n  .pr-12 {\n    padding-right: calc(var(--spacing) * 12);\n  }\n  .pb-1 {\n    padding-bottom: calc(var(--spacing) * 1);\n  }\n  .pb-2 {\n    padding-bottom: calc(var(--spacing) * 2);\n  }\n  .pb-2\\.5 {\n    padding-bottom: calc(var(--spacing) * 2.5);\n  }\n  .pb-4 {\n    padding-bottom: calc(var(--spacing) * 4);\n  }\n  .pb-6 {\n    padding-bottom: calc(var(--spacing) * 6);\n  }\n  .pl-0 {\n    padding-left: calc(var(--spacing) * 0);\n  }\n  .pl-2 {\n    padding-left: calc(var(--spacing) * 2);\n  }\n  .pl-10 {\n    padding-left: calc(var(--spacing) * 10);\n  }\n  .pl-12 {\n    padding-left: calc(var(--spacing) * 12);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .text-start {\n    text-align: start;\n  }\n  .align-middle {\n    vertical-align: middle;\n  }\n  .font-mono {\n    font-family: var(--font-geist-mono);\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-5xl {\n    font-size: var(--text-5xl);\n    line-height: var(--tw-leading, var(--text-5xl--line-height));\n  }\n  .text-6xl {\n    font-size: var(--text-6xl);\n    line-height: var(--tw-leading, var(--text-6xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .text-\\[0\\.9rem\\] {\n    font-size: 0.9rem;\n  }\n  .text-\\[0\\.65rem\\] {\n    font-size: 0.65rem;\n  }\n  .text-\\[10px\\] {\n    font-size: 10px;\n  }\n  .text-\\[clamp\\(0\\.65rem\\,50cqw\\,10rem\\)\\] {\n    font-size: clamp(0.65rem, 50cqw, 10rem);\n  }\n  .\\!leading-\\[1\\.15\\] {\n    --tw-leading: 1.15 !important;\n    line-height: 1.15 !important;\n  }\n  .leading-3 {\n    --tw-leading: calc(var(--spacing) * 3);\n    line-height: calc(var(--spacing) * 3);\n  }\n  .leading-4 {\n    --tw-leading: calc(var(--spacing) * 4);\n    line-height: calc(var(--spacing) * 4);\n  }\n  .leading-5 {\n    --tw-leading: calc(var(--spacing) * 5);\n    line-height: calc(var(--spacing) * 5);\n  }\n  .leading-7 {\n    --tw-leading: calc(var(--spacing) * 7);\n    line-height: calc(var(--spacing) * 7);\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .leading-normal {\n    --tw-leading: var(--leading-normal);\n    line-height: var(--leading-normal);\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .leading-snug {\n    --tw-leading: var(--leading-snug);\n    line-height: var(--leading-snug);\n  }\n  .leading-tight {\n    --tw-leading: var(--leading-tight);\n    line-height: var(--leading-tight);\n  }\n  .font-\\[400\\] {\n    --tw-font-weight: 400;\n    font-weight: 400;\n  }\n  .font-\\[450\\] {\n    --tw-font-weight: 450;\n    font-weight: 450;\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-normal {\n    --tw-font-weight: var(--font-weight-normal);\n    font-weight: var(--font-weight-normal);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-tight {\n    --tw-tracking: var(--tracking-tight);\n    letter-spacing: var(--tracking-tight);\n  }\n  .tracking-wide {\n    --tw-tracking: var(--tracking-wide);\n    letter-spacing: var(--tracking-wide);\n  }\n  .text-ellipsis {\n    text-overflow: ellipsis;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .\\!text-slate-400 {\n    color: var(--color-slate-400) !important;\n  }\n  .\\!text-white {\n    color: var(--color-white) !important;\n  }\n  .text-accent {\n    color: hsl(var(--accent));\n  }\n  .text-accent-foreground {\n    color: hsl(var(--accent-foreground));\n  }\n  .text-blue-600 {\n    color: var(--color-blue-600);\n  }\n  .text-blue-800 {\n    color: var(--color-blue-800);\n  }\n  .text-blue-900 {\n    color: var(--color-blue-900);\n  }\n  .text-card-foreground {\n    color: hsl(var(--card-foreground));\n  }\n  .text-destructive {\n    color: hsl(var(--destructive));\n  }\n  .text-foreground {\n    color: hsl(var(--foreground));\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-gray-900 {\n    color: var(--color-gray-900);\n  }\n  .text-green-600 {\n    color: var(--color-green-600);\n  }\n  .text-green-800 {\n    color: var(--color-green-800);\n  }\n  .text-green-900 {\n    color: var(--color-green-900);\n  }\n  .text-inherit {\n    color: inherit;\n  }\n  .text-muted-foreground {\n    color: hsl(var(--muted-foreground));\n  }\n  .text-muted-foreground\\/50 {\n    color: hsl(var(--muted-foreground));\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, hsl(var(--muted-foreground)) 50%, transparent);\n    }\n  }\n  .text-orange-800 {\n    color: var(--color-orange-800);\n  }\n  .text-primary {\n    color: hsl(var(--primary));\n  }\n  .text-primary-foreground {\n    color: hsl(var(--primary-foreground));\n  }\n  .text-primary\\/60 {\n    color: hsl(var(--primary));\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, hsl(var(--primary)) 60%, transparent);\n    }\n  }\n  .text-purple-800 {\n    color: var(--color-purple-800);\n  }\n  .text-red-600 {\n    color: var(--color-red-600);\n  }\n  .text-red-800 {\n    color: var(--color-red-800);\n  }\n  .text-red-900 {\n    color: var(--color-red-900);\n  }\n  .text-secondary {\n    color: hsl(var(--secondary));\n  }\n  .text-secondary-foreground {\n    color: hsl(var(--secondary-foreground));\n  }\n  .text-slate-400 {\n    color: var(--color-slate-400);\n  }\n  .text-slate-500 {\n    color: var(--color-slate-500);\n  }\n  .text-slate-700 {\n    color: var(--color-slate-700);\n  }\n  .text-transparent {\n    color: transparent;\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-yellow-500 {\n    color: var(--color-yellow-500);\n  }\n  .text-yellow-600 {\n    color: var(--color-yellow-600);\n  }\n  .text-yellow-800 {\n    color: var(--color-yellow-800);\n  }\n  .text-yellow-900 {\n    color: var(--color-yellow-900);\n  }\n  .text-zinc-400 {\n    color: var(--color-zinc-400);\n  }\n  .text-zinc-500 {\n    color: var(--color-zinc-500);\n  }\n  .capitalize {\n    text-transform: capitalize;\n  }\n  .lowercase {\n    text-transform: lowercase;\n  }\n  .normal-case {\n    text-transform: none;\n  }\n  .uppercase {\n    text-transform: uppercase;\n  }\n  .italic {\n    font-style: italic;\n  }\n  .no-underline {\n    text-decoration-line: none;\n  }\n  .underline {\n    text-decoration-line: underline;\n  }\n  .underline-offset-4 {\n    text-underline-offset: 4px;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .opacity-100 {\n    opacity: 100%;\n  }\n  .shadow {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-2xl {\n    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-inner {\n    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-none {\n    --tw-shadow: 0 0 #0000;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[rgb\\(213\\,213\\,213\\,05\\)_0px_0px_10px_0px\\] {\n    --tw-shadow-color: rgb(213,213,213,05);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, rgb(213,213,213,05) 0px 0px 10px 0px var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-\\[rgb\\(213\\,213\\,213\\,05\\)_0px_8px_10px_0px\\] {\n    --tw-shadow-color: rgb(213,213,213,05);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, rgb(213,213,213,05) 0px 8px 10px 0px var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .ring-offset-background {\n    --tw-ring-offset-color: hsl(var(--background));\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .outline-0 {\n    outline-style: var(--tw-outline-style);\n    outline-width: 0px;\n  }\n  .drop-shadow-md {\n    --tw-drop-shadow-size: drop-shadow(0 3px 3px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.12)));\n    --tw-drop-shadow: drop-shadow(var(--drop-shadow-md));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur-md {\n    --tw-backdrop-blur: blur(var(--blur-md));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-filter {\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[opacity\\] {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[transform\\] {\n    transition-property: transform;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\] {\n    transition-property: width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .delay-100 {\n    transition-delay: 100ms;\n  }\n  .delay-200 {\n    transition-delay: 200ms;\n  }\n  .duration-100 {\n    --tw-duration: 100ms;\n    transition-duration: 100ms;\n  }\n  .duration-150 {\n    --tw-duration: 150ms;\n    transition-duration: 150ms;\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .duration-500 {\n    --tw-duration: 500ms;\n    transition-duration: 500ms;\n  }\n  .duration-700 {\n    --tw-duration: 700ms;\n    transition-duration: 700ms;\n  }\n  .duration-1000 {\n    --tw-duration: 1000ms;\n    transition-duration: 1000ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .\\[animation-duration\\:_1s\\] {\n    animation-duration:  1s;\n  }\n  .\\[animation-iteration-count\\:_infinite\\] {\n    animation-iteration-count:  infinite;\n  }\n  .\\[animation-timing-function\\:_ease-in-out\\] {\n    animation-timing-function:  ease-in-out;\n  }\n  .group-hover\\:visible {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        visibility: visible;\n      }\n    }\n  }\n  .group-hover\\:block {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        display: block;\n      }\n    }\n  }\n  .group-hover\\:flex {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        display: flex;\n      }\n    }\n  }\n  .group-hover\\:hidden {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        display: none;\n      }\n    }\n  }\n  .group-hover\\:translate-x-0\\.5 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-translate-x: calc(var(--spacing) * 0.5);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .group-hover\\:translate-x-1 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-translate-x: calc(var(--spacing) * 1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .group-hover\\:bg-slate-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: var(--color-slate-100);\n      }\n    }\n  }\n  .group-hover\\:bg-white {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: var(--color-white);\n      }\n    }\n  }\n  .group-hover\\:font-\\[500\\] {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-font-weight: 500;\n        font-weight: 500;\n      }\n    }\n  }\n  .group-hover\\:text-primary {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        color: hsl(var(--primary));\n      }\n    }\n  }\n  .group-hover\\:opacity-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-hover\\:shadow-xl {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .group-disabled\\:text-slate-400 {\n    &:is(:where(.group):disabled *) {\n      color: var(--color-slate-400);\n    }\n  }\n  .peer-checked\\:opacity-100 {\n    &:is(:where(.peer):checked ~ *) {\n      opacity: 100%;\n    }\n  }\n  .peer-disabled\\:cursor-not-allowed {\n    &:is(:where(.peer):disabled ~ *) {\n      cursor: not-allowed;\n    }\n  }\n  .peer-disabled\\:opacity-70 {\n    &:is(:where(.peer):disabled ~ *) {\n      opacity: 70%;\n    }\n  }\n  .file\\:border-0 {\n    &::file-selector-button {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .file\\:bg-transparent {\n    &::file-selector-button {\n      background-color: transparent;\n    }\n  }\n  .file\\:text-sm {\n    &::file-selector-button {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .file\\:font-medium {\n    &::file-selector-button {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .placeholder\\:text-sm {\n    &::placeholder {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .placeholder\\:text-muted-foreground {\n    &::placeholder {\n      color: hsl(var(--muted-foreground));\n    }\n  }\n  .placeholder\\:capitalize {\n    &::placeholder {\n      text-transform: capitalize;\n    }\n  }\n  .before\\:absolute {\n    &::before {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .before\\:fixed {\n    &::before {\n      content: var(--tw-content);\n      position: fixed;\n    }\n  }\n  .before\\:top-0 {\n    &::before {\n      content: var(--tw-content);\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .before\\:\\!right-\\[-1rem\\] {\n    &::before {\n      content: var(--tw-content);\n      right: -1rem !important;\n    }\n  }\n  .before\\:right-0 {\n    &::before {\n      content: var(--tw-content);\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .before\\:right-\\[-1rem\\] {\n    &::before {\n      content: var(--tw-content);\n      right: -1rem;\n    }\n  }\n  .before\\:bottom-0 {\n    &::before {\n      content: var(--tw-content);\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .before\\:left-0 {\n    &::before {\n      content: var(--tw-content);\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .before\\:z-10 {\n    &::before {\n      content: var(--tw-content);\n      z-index: 10;\n    }\n  }\n  .before\\:h-4 {\n    &::before {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .before\\:h-screen {\n    &::before {\n      content: var(--tw-content);\n      height: 100vh;\n    }\n  }\n  .before\\:w-4 {\n    &::before {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 4);\n    }\n  }\n  .before\\:w-screen {\n    &::before {\n      content: var(--tw-content);\n      width: 100vw;\n    }\n  }\n  .before\\:rounded-tl-\\[50\\%\\] {\n    &::before {\n      content: var(--tw-content);\n      border-top-left-radius: 50%;\n    }\n  }\n  .before\\:\\!opacity-100 {\n    &::before {\n      content: var(--tw-content);\n      opacity: 100% !important;\n    }\n  }\n  .before\\:opacity-0 {\n    &::before {\n      content: var(--tw-content);\n      opacity: 0%;\n    }\n  }\n  .before\\:shadow-\\[rgb\\(48\\,48\\,48\\)_-9px_-1px_0px_0px\\] {\n    &::before {\n      content: var(--tw-content);\n      --tw-shadow-color: rgb(48,48,48);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, rgb(48,48,48) -9px -1px 0px 0px var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .before\\:outline-0 {\n    &::before {\n      content: var(--tw-content);\n      outline-style: var(--tw-outline-style);\n      outline-width: 0px;\n    }\n  }\n  .before\\:backdrop-blur-sm {\n    &::before {\n      content: var(--tw-content);\n      --tw-backdrop-blur: blur(var(--blur-sm));\n      -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n      backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    }\n  }\n  .before\\:transition-\\[opacity\\] {\n    &::before {\n      content: var(--tw-content);\n      transition-property: opacity;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .before\\:transition-\\[right\\] {\n    &::before {\n      content: var(--tw-content);\n      transition-property: right;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .before\\:duration-150 {\n    &::before {\n      content: var(--tw-content);\n      --tw-duration: 150ms;\n      transition-duration: 150ms;\n    }\n  }\n  .before\\:ease-in-out {\n    &::before {\n      content: var(--tw-content);\n      --tw-ease: var(--ease-in-out);\n      transition-timing-function: var(--ease-in-out);\n    }\n  }\n  .after\\:absolute {\n    &::after {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .after\\:top-0 {\n    &::after {\n      content: var(--tw-content);\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:top-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      top: calc(1/2 * 100%);\n    }\n  }\n  .after\\:-right-\\[1px\\] {\n    &::after {\n      content: var(--tw-content);\n      right: calc(1px * -1);\n    }\n  }\n  .after\\:right-0 {\n    &::after {\n      content: var(--tw-content);\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:bottom-0 {\n    &::after {\n      content: var(--tw-content);\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:bottom-2 {\n    &::after {\n      content: var(--tw-content);\n      bottom: calc(var(--spacing) * 2);\n    }\n  }\n  .after\\:left-0 {\n    &::after {\n      content: var(--tw-content);\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:block {\n    &::after {\n      content: var(--tw-content);\n      display: block;\n    }\n  }\n  .after\\:h-2\\/3 {\n    &::after {\n      content: var(--tw-content);\n      height: calc(2/3 * 100%);\n    }\n  }\n  .after\\:w-\\[1px\\] {\n    &::after {\n      content: var(--tw-content);\n      width: 1px;\n    }\n  }\n  .after\\:-translate-x-full {\n    &::after {\n      content: var(--tw-content);\n      --tw-translate-x: -100%;\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .after\\:-translate-y-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .after\\:transform {\n    &::after {\n      content: var(--tw-content);\n      transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n    }\n  }\n  .after\\:bg-gradient-to-r {\n    &::after {\n      content: var(--tw-content);\n      --tw-gradient-position: to right in oklab;\n      background-image: linear-gradient(var(--tw-gradient-stops));\n    }\n  }\n  .after\\:from-transparent {\n    &::after {\n      content: var(--tw-content);\n      --tw-gradient-from: transparent;\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .after\\:via-\\[rgba\\(255\\,255\\,255\\,0\\.75\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      --tw-gradient-via: rgba(255,255,255,0.75);\n      --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n      --tw-gradient-stops: var(--tw-gradient-via-stops);\n    }\n  }\n  .after\\:to-transparent {\n    &::after {\n      content: var(--tw-content);\n      --tw-gradient-to: transparent;\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .after\\:content-\\[\\'\\'\\] {\n    &::after {\n      content: var(--tw-content);\n      --tw-content: '';\n      content: var(--tw-content);\n    }\n  }\n  .after\\:\\[animation-duration\\:_2s\\] {\n    &::after {\n      content: var(--tw-content);\n      animation-duration:  2s;\n    }\n  }\n  .after\\:\\[animation-iteration-count\\:_infinite\\] {\n    &::after {\n      content: var(--tw-content);\n      animation-iteration-count:  infinite;\n    }\n  }\n  .last\\:border-0 {\n    &:last-child {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .first-of-type\\:pt-2 {\n    &:first-of-type {\n      padding-top: calc(var(--spacing) * 2);\n    }\n  }\n  .last-of-type\\:justify-self-stretch {\n    &:last-of-type {\n      justify-self: stretch;\n    }\n  }\n  .last-of-type\\:pb-2 {\n    &:last-of-type {\n      padding-bottom: calc(var(--spacing) * 2);\n    }\n  }\n  .hover\\:translate-x-full {\n    &:hover {\n      @media (hover: hover) {\n        --tw-translate-x: 100%;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .hover\\:-translate-y-0\\.5 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-translate-y: calc(var(--spacing) * -0.5);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .hover\\:-translate-y-1 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-translate-y: calc(var(--spacing) * -1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .hover\\:border-accent {\n    &:hover {\n      @media (hover: hover) {\n        border-color: hsl(var(--accent));\n      }\n    }\n  }\n  .hover\\:border-green-600 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--color-green-600);\n      }\n    }\n  }\n  .hover\\:border-primary\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: hsl(var(--primary));\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, hsl(var(--primary)) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-red-600 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--color-red-600);\n      }\n    }\n  }\n  .hover\\:\\!bg-slate-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-slate-100) !important;\n      }\n    }\n  }\n  .hover\\:\\!bg-slate-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-slate-200) !important;\n      }\n    }\n  }\n  .hover\\:bg-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--accent));\n      }\n    }\n  }\n  .hover\\:bg-accent\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--accent));\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-background {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--background));\n      }\n    }\n  }\n  .hover\\:bg-muted\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--muted));\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--muted)) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-muted\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--muted));\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--muted)) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--primary));\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--primary)) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: hsl(var(--primary));\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, hsl(var(--primary)) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-rose-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-rose-50);\n      }\n    }\n  }\n  .hover\\:bg-sky-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-sky-50);\n      }\n    }\n  }\n  .hover\\:bg-slate-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-slate-100);\n      }\n    }\n  }\n  .hover\\:bg-slate-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-slate-200);\n      }\n    }\n  }\n  .hover\\:bg-slate-300 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-slate-300);\n      }\n    }\n  }\n  .hover\\:bg-white {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:bg-zinc-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-zinc-100);\n      }\n    }\n  }\n  .hover\\:bg-zinc-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-zinc-700);\n      }\n    }\n  }\n  .hover\\:from-amber-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-amber-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-blue-700 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-blue-700);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-green-700 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-green-700);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-orange-700 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-orange-700);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-red-700 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-red-700);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-yellow-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-yellow-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-amber-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-amber-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-emerald-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-emerald-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-orange-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-orange-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-rose-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-rose-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-sky-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-sky-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-yellow-600 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-yellow-600);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:text-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--accent-foreground));\n      }\n    }\n  }\n  .hover\\:text-blue-800 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-blue-800);\n      }\n    }\n  }\n  .hover\\:text-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--foreground));\n      }\n    }\n  }\n  .hover\\:text-green-800 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-green-800);\n      }\n    }\n  }\n  .hover\\:text-primary {\n    &:hover {\n      @media (hover: hover) {\n        color: hsl(var(--primary));\n      }\n    }\n  }\n  .hover\\:text-white {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:text-zinc-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-zinc-600);\n      }\n    }\n  }\n  .hover\\:underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:shadow {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-md {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:before\\:right-\\[-11\\.96rem\\] {\n    &:hover {\n      @media (hover: hover) {\n        &::before {\n          content: var(--tw-content);\n          right: -11.96rem;\n        }\n      }\n    }\n  }\n  .hover\\:placeholder-shown\\:bg-slate-100 {\n    &:hover {\n      @media (hover: hover) {\n        &:placeholder-shown {\n          background-color: var(--color-slate-100);\n        }\n      }\n    }\n  }\n  .focus\\:\\!bg-transparent {\n    &:focus {\n      background-color: transparent !important;\n    }\n  }\n  .focus\\:bg-white {\n    &:focus {\n      background-color: var(--color-white);\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-ring {\n    &:focus {\n      --tw-ring-color: hsl(var(--ring));\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-0 {\n    &:focus {\n      outline-style: var(--tw-outline-style);\n      outline-width: 0px;\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .focus-visible\\:border-border {\n    &:focus-visible {\n      border-color: hsl(var(--border));\n    }\n  }\n  .focus-visible\\:border-green-500 {\n    &:focus-visible {\n      border-color: var(--color-green-500);\n    }\n  }\n  .focus-visible\\:border-primary {\n    &:focus-visible {\n      border-color: hsl(var(--primary));\n    }\n  }\n  .focus-visible\\:border-red-500 {\n    &:focus-visible {\n      border-color: var(--color-red-500);\n    }\n  }\n  .focus-visible\\:border-yellow-500 {\n    &:focus-visible {\n      border-color: var(--color-yellow-500);\n    }\n  }\n  .focus-visible\\:bg-background {\n    &:focus-visible {\n      background-color: hsl(var(--background));\n    }\n  }\n  .focus-visible\\:bg-muted {\n    &:focus-visible {\n      background-color: hsl(var(--muted));\n    }\n  }\n  .focus-visible\\:ring-2 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-green-500\\/20 {\n    &:focus-visible {\n      --tw-ring-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-primary\\/20 {\n    &:focus-visible {\n      --tw-ring-color: hsl(var(--primary));\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, hsl(var(--primary)) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-red-500\\/20 {\n    &:focus-visible {\n      --tw-ring-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-ring {\n    &:focus-visible {\n      --tw-ring-color: hsl(var(--ring));\n    }\n  }\n  .focus-visible\\:ring-yellow-500\\/20 {\n    &:focus-visible {\n      --tw-ring-color: color-mix(in srgb, oklch(79.5% 0.184 86.047) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--color-yellow-500) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-offset-2 {\n    &:focus-visible {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:outline-none {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .active\\:translate-y-0 {\n    &:active {\n      --tw-translate-y: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .active\\:bg-slate-100 {\n    &:active {\n      background-color: var(--color-slate-100);\n    }\n  }\n  .active\\:bg-zinc-200 {\n    &:active {\n      background-color: var(--color-zinc-200);\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:border-0 {\n    &:disabled {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .disabled\\:bg-transparent {\n    &:disabled {\n      background-color: transparent;\n    }\n  }\n  .disabled\\:opacity-45 {\n    &:disabled {\n      opacity: 45%;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .disabled\\:shadow-inner {\n    &:disabled {\n      --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:bg-primary {\n    &[data-state=\"checked\"] {\n      background-color: hsl(var(--primary));\n    }\n  }\n  .data-\\[state\\=checked\\]\\:text-primary-foreground {\n    &[data-state=\"checked\"] {\n      color: hsl(var(--primary-foreground));\n    }\n  }\n  .sm\\:inline-flex {\n    @media (width >= 40rem) {\n      display: inline-flex;\n    }\n  }\n  .sm\\:w-3\\/4 {\n    @media (width >= 40rem) {\n      width: calc(3/4 * 100%);\n    }\n  }\n  .sm\\:w-4\\/12 {\n    @media (width >= 40rem) {\n      width: calc(4/12 * 100%);\n    }\n  }\n  .sm\\:w-\\[60vw\\] {\n    @media (width >= 40rem) {\n      width: 60vw;\n    }\n  }\n  .sm\\:w-\\[150px\\] {\n    @media (width >= 40rem) {\n      width: 150px;\n    }\n  }\n  .sm\\:w-\\[300px\\] {\n    @media (width >= 40rem) {\n      width: 300px;\n    }\n  }\n  .sm\\:max-w-96 {\n    @media (width >= 40rem) {\n      max-width: calc(var(--spacing) * 96);\n    }\n  }\n  .sm\\:max-w-\\[50\\%\\] {\n    @media (width >= 40rem) {\n      max-width: 50%;\n    }\n  }\n  .sm\\:max-w-\\[75\\%\\] {\n    @media (width >= 40rem) {\n      max-width: 75%;\n    }\n  }\n  .sm\\:max-w-\\[80vw\\] {\n    @media (width >= 40rem) {\n      max-width: 80vw;\n    }\n  }\n  .sm\\:max-w-screen-sm {\n    @media (width >= 40rem) {\n      max-width: var(--breakpoint-sm);\n    }\n  }\n  .sm\\:flex-1 {\n    @media (width >= 40rem) {\n      flex: 1;\n    }\n  }\n  .sm\\:flex-\\[2\\] {\n    @media (width >= 40rem) {\n      flex: 2;\n    }\n  }\n  .sm\\:grid-cols-2 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:gap-4 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 4);\n    }\n  }\n  .sm\\:rounded-lg {\n    @media (width >= 40rem) {\n      border-radius: var(--radius-lg);\n    }\n  }\n  .sm\\:py-4 {\n    @media (width >= 40rem) {\n      padding-block: calc(var(--spacing) * 4);\n    }\n  }\n  .sm\\:py-32 {\n    @media (width >= 40rem) {\n      padding-block: calc(var(--spacing) * 32);\n    }\n  }\n  .sm\\:text-5xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .md\\:relative {\n    @media (width >= 48rem) {\n      position: relative;\n    }\n  }\n  .md\\:mb-0 {\n    @media (width >= 48rem) {\n      margin-bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .md\\:block {\n    @media (width >= 48rem) {\n      display: block;\n    }\n  }\n  .md\\:flex {\n    @media (width >= 48rem) {\n      display: flex;\n    }\n  }\n  .md\\:hidden {\n    @media (width >= 48rem) {\n      display: none;\n    }\n  }\n  .md\\:w-1\\/2 {\n    @media (width >= 48rem) {\n      width: calc(1/2 * 100%);\n    }\n  }\n  .md\\:w-\\[60px\\] {\n    @media (width >= 48rem) {\n      width: 60px;\n    }\n  }\n  .md\\:w-\\[235px\\] {\n    @media (width >= 48rem) {\n      width: 235px;\n    }\n  }\n  .md\\:max-w-\\[40vw\\] {\n    @media (width >= 48rem) {\n      max-width: 40vw;\n    }\n  }\n  .md\\:max-w-\\[50\\%\\] {\n    @media (width >= 48rem) {\n      max-width: 50%;\n    }\n  }\n  .md\\:max-w-\\[75\\%\\] {\n    @media (width >= 48rem) {\n      max-width: 75%;\n    }\n  }\n  .md\\:max-w-\\[80\\%\\] {\n    @media (width >= 48rem) {\n      max-width: 80%;\n    }\n  }\n  .md\\:max-w-\\[85\\%\\] {\n    @media (width >= 48rem) {\n      max-width: 85%;\n    }\n  }\n  .md\\:max-w-\\[90\\%\\] {\n    @media (width >= 48rem) {\n      max-width: 90%;\n    }\n  }\n  .md\\:max-w-\\[235px\\] {\n    @media (width >= 48rem) {\n      max-width: 235px;\n    }\n  }\n  .md\\:max-w-screen-md {\n    @media (width >= 48rem) {\n      max-width: var(--breakpoint-md);\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-4 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-10 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(10, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-\\[2fr\\,1fr\\] {\n    @media (width >= 48rem) {\n      grid-template-columns: 2fr,1fr;\n    }\n  }\n  .md\\:flex-row {\n    @media (width >= 48rem) {\n      flex-direction: row;\n    }\n  }\n  .md\\:py-8 {\n    @media (width >= 48rem) {\n      padding-block: calc(var(--spacing) * 8);\n    }\n  }\n  .md\\:text-5xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .md\\:text-7xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-7xl);\n      line-height: var(--tw-leading, var(--text-7xl--line-height));\n    }\n  }\n  .md\\:text-base {\n    @media (width >= 48rem) {\n      font-size: var(--text-base);\n      line-height: var(--tw-leading, var(--text-base--line-height));\n    }\n  }\n  .group-hover\\:md\\:w-\\[235px\\] {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        @media (width >= 48rem) {\n          width: 235px;\n        }\n      }\n    }\n  }\n  .lg\\:col-span-1 {\n    @media (width >= 64rem) {\n      grid-column: span 1 / span 1;\n    }\n  }\n  .lg\\:w-1\\/3 {\n    @media (width >= 64rem) {\n      width: calc(1/3 * 100%);\n    }\n  }\n  .lg\\:max-w-\\[60\\%\\] {\n    @media (width >= 64rem) {\n      max-width: 60%;\n    }\n  }\n  .lg\\:max-w-\\[75\\%\\] {\n    @media (width >= 64rem) {\n      max-width: 75%;\n    }\n  }\n  .lg\\:max-w-\\[95\\%\\] {\n    @media (width >= 64rem) {\n      max-width: 95%;\n    }\n  }\n  .lg\\:max-w-screen-lg {\n    @media (width >= 64rem) {\n      max-width: var(--breakpoint-lg);\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-4 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-5 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(5, minmax(0, 1fr));\n    }\n  }\n  .lg\\:px-8 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .xl\\:max-w-\\[85\\%\\] {\n    @media (width >= 80rem) {\n      max-width: 85%;\n    }\n  }\n  .xl\\:max-w-screen-xl {\n    @media (width >= 80rem) {\n      max-width: var(--breakpoint-xl);\n    }\n  }\n  .\\32 xl\\:max-w-screen-2xl {\n    @media (width >= 96rem) {\n      max-width: var(--breakpoint-2xl);\n    }\n  }\n  .dark\\:scale-0 {\n    @media (prefers-color-scheme: dark) {\n      --tw-scale-x: 0%;\n      --tw-scale-y: 0%;\n      --tw-scale-z: 0%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .dark\\:scale-100 {\n    @media (prefers-color-scheme: dark) {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .dark\\:-rotate-90 {\n    @media (prefers-color-scheme: dark) {\n      rotate: calc(90deg * -1);\n    }\n  }\n  .dark\\:rotate-0 {\n    @media (prefers-color-scheme: dark) {\n      rotate: 0deg;\n    }\n  }\n  .dark\\:border-blue-800 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-blue-800);\n    }\n  }\n  .dark\\:border-green-800 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-green-800);\n    }\n  }\n  .dark\\:border-red-800 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-red-800);\n    }\n  }\n  .dark\\:border-yellow-800 {\n    @media (prefers-color-scheme: dark) {\n      border-color: var(--color-yellow-800);\n    }\n  }\n  .dark\\:bg-blue-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(37.9% 0.146 265.522) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-blue-950\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(28.2% 0.091 267.935) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-blue-950) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-green-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(39.3% 0.095 152.535) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-green-950\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(26.6% 0.065 152.934) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-green-950) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-orange-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(40.8% 0.123 38.172) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-orange-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-purple-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(38.1% 0.176 304.987) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-purple-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-red-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-red-950\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(25.8% 0.092 26.042) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-red-950) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-slate-900 {\n    @media (prefers-color-scheme: dark) {\n      background-color: var(--color-slate-900);\n    }\n  }\n  .dark\\:bg-yellow-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(42.1% 0.095 57.708) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-yellow-950\\/20 {\n    @media (prefers-color-scheme: dark) {\n      background-color: color-mix(in srgb, oklch(28.6% 0.066 53.813) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-yellow-950) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:from-blue-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      --tw-gradient-from: color-mix(in srgb, oklch(37.9% 0.146 265.522) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-blue-900) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-green-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      --tw-gradient-from: color-mix(in srgb, oklch(39.3% 0.095 152.535) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-green-900) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-orange-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      --tw-gradient-from: color-mix(in srgb, oklch(40.8% 0.123 38.172) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-orange-900) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-orange-950\\/20 {\n    @media (prefers-color-scheme: dark) {\n      --tw-gradient-from: color-mix(in srgb, oklch(26.6% 0.079 36.259) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-orange-950) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-amber-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      --tw-gradient-to: color-mix(in srgb, oklch(41.4% 0.112 45.904) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-amber-900) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-amber-950\\/20 {\n    @media (prefers-color-scheme: dark) {\n      --tw-gradient-to: color-mix(in srgb, oklch(27.9% 0.077 45.635) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-amber-950) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-emerald-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      --tw-gradient-to: color-mix(in srgb, oklch(37.8% 0.077 168.94) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-emerald-900) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-sky-900\\/20 {\n    @media (prefers-color-scheme: dark) {\n      --tw-gradient-to: color-mix(in srgb, oklch(39.1% 0.09 240.876) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-sky-900) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:text-blue-100 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-blue-100);\n    }\n  }\n  .dark\\:text-blue-200 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-blue-200);\n    }\n  }\n  .dark\\:text-blue-400 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-blue-400);\n    }\n  }\n  .dark\\:text-green-100 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-green-100);\n    }\n  }\n  .dark\\:text-green-200 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-green-200);\n    }\n  }\n  .dark\\:text-green-400 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-green-400);\n    }\n  }\n  .dark\\:text-orange-200 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-orange-200);\n    }\n  }\n  .dark\\:text-orange-400 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-orange-400);\n    }\n  }\n  .dark\\:text-purple-400 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-purple-400);\n    }\n  }\n  .dark\\:text-red-100 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-red-100);\n    }\n  }\n  .dark\\:text-red-200 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-red-200);\n    }\n  }\n  .dark\\:text-red-400 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-red-400);\n    }\n  }\n  .dark\\:text-yellow-100 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-yellow-100);\n    }\n  }\n  .dark\\:text-yellow-200 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-yellow-200);\n    }\n  }\n  .dark\\:text-yellow-400 {\n    @media (prefers-color-scheme: dark) {\n      color: var(--color-yellow-400);\n    }\n  }\n  .dark\\:hover\\:text-blue-200 {\n    @media (prefers-color-scheme: dark) {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--color-blue-200);\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:text-green-200 {\n    @media (prefers-color-scheme: dark) {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--color-green-200);\n        }\n      }\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:absolute {\n    & .column-resize-handle {\n      position: absolute;\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:top-0 {\n    & .column-resize-handle {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:right-\\[-3px\\] {\n    & .column-resize-handle {\n      right: -3px;\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:bottom-0 {\n    & .column-resize-handle {\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:z-\\[100\\] {\n    & .column-resize-handle {\n      z-index: 100;\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:w-\\[4px\\] {\n    & .column-resize-handle {\n      width: 4px;\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:cursor-col-resize {\n    & .column-resize-handle {\n      cursor: col-resize;\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:bg-transparent {\n    & .column-resize-handle {\n      background-color: transparent;\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:transition-colors {\n    & .column-resize-handle {\n      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .\\[\\&_\\.column-resize-handle\\]\\:duration-200 {\n    & .column-resize-handle {\n      --tw-duration: 200ms;\n      transition-duration: 200ms;\n    }\n  }\n  .\\[\\&_\\.input-wrapper\\]\\:\\!border-none {\n    & .input-wrapper {\n      --tw-border-style: none !important;\n      border-style: none !important;\n    }\n  }\n  .\\[\\&_\\.input-wrapper\\]\\:font-medium {\n    & .input-wrapper {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .\\[\\&_\\.remirror-editor\\]\\:relative {\n    & .remirror-editor {\n      position: relative;\n    }\n  }\n  .\\[\\&_\\.remirror-editor\\]\\:my-2 {\n    & .remirror-editor {\n      margin-block: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\.remirror-editor\\]\\:min-h-60 {\n    & .remirror-editor {\n      min-height: calc(var(--spacing) * 60);\n    }\n  }\n  .\\[\\&_\\.remirror-editor\\]\\:overflow-y-auto {\n    & .remirror-editor {\n      overflow-y: auto;\n    }\n  }\n  .\\[\\&_\\.remirror-editor\\]\\:p-4 {\n    & .remirror-editor {\n      padding: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_\\.remirror-editor\\]\\:break-words {\n    & .remirror-editor {\n      overflow-wrap: break-word;\n    }\n  }\n  .\\[\\&_\\.remirror-editor\\]\\:whitespace-break-spaces {\n    & .remirror-editor {\n      white-space: break-spaces;\n    }\n  }\n  .\\[\\&_\\.remirror-editor\\]\\:shadow-sm {\n    & .remirror-editor {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .\\[\\&_\\.remirror-editor\\]\\:outline-0 {\n    & .remirror-editor {\n      outline-style: var(--tw-outline-style);\n      outline-width: 0px;\n    }\n  }\n  .before\\:\\[\\&_\\.remirror-editor_\\.remirror-is-empty\\]\\:pointer-events-none {\n    &::before {\n      content: var(--tw-content);\n      & .remirror-editor .remirror-is-empty {\n        pointer-events: none;\n      }\n    }\n  }\n  .before\\:\\[\\&_\\.remirror-editor_\\.remirror-is-empty\\]\\:absolute {\n    &::before {\n      content: var(--tw-content);\n      & .remirror-editor .remirror-is-empty {\n        position: absolute;\n      }\n    }\n  }\n  .before\\:\\[\\&_\\.remirror-editor_\\.remirror-is-empty\\]\\:italic {\n    &::before {\n      content: var(--tw-content);\n      & .remirror-editor .remirror-is-empty {\n        font-style: italic;\n      }\n    }\n  }\n  .before\\:\\[\\&_\\.remirror-editor_\\.remirror-is-empty\\]\\:content-\\[attr\\(data-placeholder\\)\\] {\n    &::before {\n      content: var(--tw-content);\n      & .remirror-editor .remirror-is-empty {\n        --tw-content: attr(data-placeholder);\n        content: var(--tw-content);\n      }\n    }\n  }\n  .\\[\\&_\\.remirror-list-item-marker-container\\]\\:absolute {\n    & .remirror-list-item-marker-container {\n      position: absolute;\n    }\n  }\n  .\\[\\&_\\.remirror-list-item-marker-container\\]\\:-left-5 {\n    & .remirror-list-item-marker-container {\n      left: calc(var(--spacing) * -5);\n    }\n  }\n  .\\[\\&_\\.remirror-list-item-marker-container\\]\\:inline-block {\n    & .remirror-list-item-marker-container {\n      display: inline-block;\n    }\n  }\n  .\\[\\&_\\.remirror-list-item-marker-container\\]\\:text-center {\n    & .remirror-list-item-marker-container {\n      text-align: center;\n    }\n  }\n  .\\[\\&_\\.remirror-list-item-marker-container\\]\\:select-none {\n    & .remirror-list-item-marker-container {\n      -webkit-user-select: none;\n      user-select: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:h-3\\.5 {\n    & svg {\n      height: calc(var(--spacing) * 3.5);\n    }\n  }\n  .\\[\\&_svg\\]\\:w-3\\.5 {\n    & svg {\n      width: calc(var(--spacing) * 3.5);\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar\\]\\:w-0 {\n    &::-webkit-scrollbar {\n      width: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&\\:hover\\>\\.child-dropdown\\]\\:block {\n    &:hover>.child-dropdown {\n      display: block;\n    }\n  }\n  .\\[\\&\\:not\\(button\\)\\]\\:px-3 {\n    &:not(button) {\n      padding-inline: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\:not\\(button\\)\\]\\:py-\\[0\\.55rem\\] {\n    &:not(button) {\n      padding-block: 0.55rem;\n    }\n  }\n  .\\[\\&\\>\\*\\]\\:w-full {\n    &>* {\n      width: 100%;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-3 {\n    &>svg {\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-4 {\n    &>svg {\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-5 {\n    &>svg {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-full {\n    &>svg {\n      height: 100%;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-4 {\n    &>svg {\n      width: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-5 {\n    &>svg {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-full {\n    &>svg {\n      width: 100%;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:fill-inherit {\n    &>svg {\n      fill: inherit;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:stroke-1 {\n    &>svg {\n      stroke-width: 1;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:transition-colors {\n    &>svg {\n      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n      transition-duration: var(--tw-duration, var(--default-transition-duration));\n    }\n  }\n  .\\[\\&\\>tr\\>th\\]\\:sticky {\n    &>tr>th {\n      position: sticky;\n    }\n  }\n  .\\[\\&\\>tr\\>th\\]\\:top-0 {\n    &>tr>th {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&\\>tr\\>th\\]\\:h-10 {\n    &>tr>th {\n      height: calc(var(--spacing) * 10);\n    }\n  }\n  .\\[\\&\\>tr\\>th\\]\\:border-b {\n    &>tr>th {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .\\[\\&\\>tr\\>th\\]\\:border-solid {\n    &>tr>th {\n      --tw-border-style: solid;\n      border-style: solid;\n    }\n  }\n  .\\[\\&\\>tr\\>th\\]\\:border-slate-100 {\n    &>tr>th {\n      border-color: var(--color-slate-100);\n    }\n  }\n  .\\[\\&\\>tr\\>th\\]\\:leading-normal {\n    &>tr>th {\n      --tw-leading: var(--leading-normal);\n      line-height: var(--leading-normal);\n    }\n  }\n}\n:root {\n  --legalyard-orange: 18 75% 55%;\n  --legalyard-gold: 39 78% 61%;\n  --legalyard-blue: 218 58% 55%;\n  --legalyard-sky: 200 73% 67%;\n  --background: 0 0% 100%;\n  --foreground: 222 47% 11%;\n  --card: 0 0% 100%;\n  --card-foreground: 222 47% 11%;\n  --popover: 0 0% 100%;\n  --popover-foreground: 222 47% 11%;\n  --primary: var(--legalyard-orange);\n  --primary-foreground: 0 0% 98%;\n  --secondary: var(--legalyard-gold);\n  --secondary-foreground: 222 47% 11%;\n  --accent: var(--legalyard-sky);\n  --accent-foreground: 222 47% 11%;\n  --muted: 210 40% 96%;\n  --muted-foreground: 215 16% 47%;\n  --destructive: 0 84% 60%;\n  --destructive-foreground: 0 0% 98%;\n  --border: 214 32% 91%;\n  --input: 214 32% 91%;\n  --ring: var(--legalyard-orange);\n  --radius: 0.75rem;\n  --brand-gradient: linear-gradient(135deg, hsl(var(--legalyard-orange)) 0%, hsl(var(--legalyard-gold)) 100%);\n  --brand-gradient-blue: linear-gradient(135deg, hsl(var(--legalyard-blue)) 0%, hsl(var(--legalyard-sky)) 100%);\n}\n.dark {\n  --background: 222 47% 5%;\n  --foreground: 210 40% 98%;\n  --card: 222 47% 7%;\n  --card-foreground: 210 40% 98%;\n  --popover: 222 47% 7%;\n  --popover-foreground: 210 40% 98%;\n  --primary: var(--legalyard-orange);\n  --primary-foreground: 0 0% 98%;\n  --secondary: var(--legalyard-gold);\n  --secondary-foreground: 0 0% 98%;\n  --accent: var(--legalyard-sky);\n  --accent-foreground: 222 47% 11%;\n  --muted: 217 33% 15%;\n  --muted-foreground: 215 20% 65%;\n  --destructive: 0 63% 31%;\n  --destructive-foreground: 210 40% 98%;\n  --border: 217 33% 15%;\n  --input: 217 33% 15%;\n  --ring: var(--legalyard-orange);\n}\n* {\n  border-color: hsl(var(--border));\n}\nbody {\n  background: hsl(var(--background));\n  color: hsl(var(--foreground));\n  font-family: var(--font-geist-sans), system-ui, sans-serif;\n}\n.legalyard-gradient {\n  background: var(--brand-gradient);\n}\n.legalyard-gradient-blue {\n  background: var(--brand-gradient-blue);\n}\n.legalyard-card {\n  background: hsl(var(--card));\n  border: 1px solid hsl(var(--border));\n  border-radius: calc(var(--radius) + 2px);\n  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  transition: all 0.2s ease-in-out;\n}\n.legalyard-card:hover {\n  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  transform: translateY(-2px);\n}\n.legalyard-button {\n  background: var(--brand-gradient);\n  border: none;\n  border-radius: var(--radius);\n  color: hsl(var(--primary-foreground));\n  font-weight: 600;\n  padding: 0.75rem 1.5rem;\n  transition: all 0.2s ease-in-out;\n  position: relative;\n  overflow: hidden;\n}\n.legalyard-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);\n  transition: left 0.5s;\n}\n.legalyard-button:hover::before {\n  left: 100%;\n}\n.legalyard-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 8px 25px -8px hsl(var(--legalyard-orange) / 0.5);\n}\n.legalyard-nav {\n  backdrop-filter: blur(20px);\n  background: hsl(var(--background) / 0.8);\n  border-bottom: 1px solid hsl(var(--border));\n}\n.legalyard-hero {\n  background: linear-gradient(135deg,\n    hsl(var(--background)) 0%,\n    hsl(var(--legalyard-orange) / 0.05) 50%,\n    hsl(var(--legalyard-blue) / 0.05) 100%);\n}\n.legalyard-code-block {\n  background: hsl(var(--muted));\n  border: 1px solid hsl(var(--border));\n  border-radius: var(--radius);\n  position: relative;\n}\n.legalyard-code-block::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: var(--brand-gradient);\n  border-radius: var(--radius) var(--radius) 0 0;\n}\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n::-webkit-scrollbar-track {\n  background: hsl(var(--muted));\n  border-radius: 4px;\n}\n::-webkit-scrollbar-thumb {\n  background: hsl(var(--muted-foreground) / 0.3);\n  border-radius: 4px;\n}\n::-webkit-scrollbar-thumb:hover {\n  background: hsl(var(--primary) / 0.5);\n}\n.legalyard-focus {\n  outline: 2px solid hsl(var(--ring));\n  outline-offset: 2px;\n}\n.legalyard-fade-in {\n  animation: fadeIn 0.5s ease-in-out;\n}\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.legalyard-slide-up {\n  animation: slideUp 0.3s ease-out;\n}\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-content {\n  syntax: \"*\";\n  initial-value: \"\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-content: \"\";\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EAshKE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAthKJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EAiKE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AA7SF;;AAAA;EAkTE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAKI;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAOzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;;EAOA;;;;;;EAQA;;;;;;;EAQA;;;;;;;EAQA;;;;;;EAOA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;;EAOA;;;;;EAMA;;;;;;EAOA;;;;;;EAOA;;;;;;;EAQA;;;;;;EAOA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IACE;;;;;;EASF;IACE;;;;;EAOJ;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAOvB;IACE;MAAyB;;;;;;EAO7B;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAqC;;;;;;;;EAQrC;IAAqC;;;;;;;;EAQrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;;EAKrC;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;IAEnC;MAAgD;;;;;;EAMlD;IAAqC;;;;;IAEnC;MAAgD;;;;;;EAOlD;IAAqC;;;;;IAEnC;MAAgD;;;;;;EAOlD;IAAqC;;;;;IAEnC;MAAgD;;;;;;EAOlD;IAAqC;;;;;IAEnC;MAAgD;;;;;;EAOlD;IAAqC;;;;;IAEnC;MAAgD;;;;;;EAOlD;IAAqC;;;;;IAEnC;MAAgD;;;;;;EAOlD;IAAqC;;;;;IAEnC;MAAgD;;;;;;EAOlD;IAAqC;;;;;IAEnC;MAAgD;;;;;;EAOlD;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAAqC;;;;;EAKrC;IAEI;MAAuB;;;;;;EAO3B;IAEI;MAAuB;;;;;;EAO3B;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAEE;;;;EAMF;;;;EAEE;;;;EAMF;;;;EAEE;;;;EAMF;;;;EAEE;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;;EAOA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;;;AAMJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;;AAOA;;;;;AAIA;;;;;;;;;;;;AAWA;;;;;;;;;;;AAUA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAMA;;;;;;;AAMA;;;;;;;;;;;AAUA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA"}}]}