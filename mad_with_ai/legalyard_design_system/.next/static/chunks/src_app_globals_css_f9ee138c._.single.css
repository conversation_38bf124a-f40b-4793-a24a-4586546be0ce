/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-red-950: oklch(25.8% .092 26.042);
    --color-orange-50: oklch(98% .016 73.684);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-200: oklch(90.1% .076 70.697);
    --color-orange-400: oklch(75% .183 55.934);
    --color-orange-500: oklch(70.5% .213 47.604);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-700: oklch(55.3% .195 38.402);
    --color-orange-800: oklch(47% .157 37.304);
    --color-orange-900: oklch(40.8% .123 38.172);
    --color-orange-950: oklch(26.6% .079 36.259);
    --color-amber-50: oklch(98.7% .022 95.277);
    --color-amber-100: oklch(96.2% .059 95.617);
    --color-amber-400: oklch(82.8% .189 84.429);
    --color-amber-500: oklch(76.9% .188 70.08);
    --color-amber-600: oklch(66.6% .179 58.318);
    --color-amber-900: oklch(41.4% .112 45.904);
    --color-amber-950: oklch(27.9% .077 45.635);
    --color-yellow-50: oklch(98.7% .026 102.212);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-yellow-900: oklch(42.1% .095 57.708);
    --color-yellow-950: oklch(28.6% .066 53.813);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-green-950: oklch(26.6% .065 152.934);
    --color-emerald-100: oklch(95% .052 163.051);
    --color-emerald-500: oklch(69.6% .17 162.48);
    --color-emerald-600: oklch(59.6% .145 163.225);
    --color-emerald-900: oklch(37.8% .077 168.94);
    --color-sky-50: oklch(97.7% .013 236.62);
    --color-sky-100: oklch(95.1% .026 236.824);
    --color-sky-300: oklch(82.8% .111 230.318);
    --color-sky-500: oklch(68.5% .169 237.323);
    --color-sky-600: oklch(58.8% .158 241.966);
    --color-sky-900: oklch(39.1% .09 240.876);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-blue-950: oklch(28.2% .091 267.935);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-400: oklch(71.4% .203 305.504);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-rose-50: oklch(96.9% .015 12.422);
    --color-rose-500: oklch(64.5% .246 16.439);
    --color-rose-600: oklch(58.6% .253 17.585);
    --color-rose-700: oklch(51.4% .222 16.935);
    --color-slate-50: oklch(98.4% .003 247.858);
    --color-slate-100: oklch(96.8% .007 247.896);
    --color-slate-200: oklch(92.9% .013 255.508);
    --color-slate-300: oklch(86.9% .022 252.894);
    --color-slate-400: oklch(70.4% .04 256.788);
    --color-slate-500: oklch(55.4% .046 257.417);
    --color-slate-600: oklch(44.6% .043 257.281);
    --color-slate-700: oklch(37.2% .044 257.287);
    --color-slate-900: oklch(20.8% .042 265.755);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-zinc-100: oklch(96.7% .001 286.375);
    --color-zinc-200: oklch(92% .004 286.32);
    --color-zinc-300: oklch(87.1% .006 286.286);
    --color-zinc-400: oklch(70.5% .015 286.067);
    --color-zinc-500: oklch(55.2% .016 285.938);
    --color-zinc-600: oklch(44.2% .017 285.786);
    --color-zinc-700: oklch(37% .013 285.805);
    --color-white: #fff;
    --spacing: .25rem;
    --breakpoint-sm: 40rem;
    --breakpoint-md: 48rem;
    --breakpoint-lg: 64rem;
    --breakpoint-xl: 80rem;
    --breakpoint-2xl: 96rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -.025em;
    --tracking-wide: .025em;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --drop-shadow-md: 0 3px 3px #0000001f;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .\!visible {
    visibility: visible !important;
  }

  .collapse {
    visibility: collapse;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .\!-top-\[0\.03rem\] {
    top: -.03rem !important;
  }

  .\!-top-\[30\%\] {
    top: -30% !important;
  }

  .-top-1 {
    top: calc(var(--spacing) * -1);
  }

  .-top-2 {
    top: calc(var(--spacing) * -2);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .-top-24 {
    top: calc(var(--spacing) * -24);
  }

  .-top-px {
    top: -1px;
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-2\.5 {
    top: calc(var(--spacing) * 2.5);
  }

  .top-3 {
    top: calc(var(--spacing) * 3);
  }

  .top-5 {
    top: calc(var(--spacing) * 5);
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .top-full {
    top: 100%;
  }

  .-right-1 {
    right: calc(var(--spacing) * -1);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-2\.5 {
    right: calc(var(--spacing) * 2.5);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-full {
    right: 100%;
  }

  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-2\.5 {
    bottom: calc(var(--spacing) * 2.5);
  }

  .bottom-full {
    bottom: 100%;
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-2\.5 {
    left: calc(var(--spacing) * 2.5);
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .left-full {
    left: 100%;
  }

  .-z-10 {
    z-index: calc(10 * -1);
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-30 {
    z-index: 30;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[2\] {
    z-index: 2;
  }

  .z-\[5\] {
    z-index: 5;
  }

  .z-\[7\] {
    z-index: 7;
  }

  .z-\[9\] {
    z-index: 9;
  }

  .z-\[22\] {
    z-index: 22;
  }

  .z-\[35\] {
    z-index: 35;
  }

  .z-\[51\] {
    z-index: 51;
  }

  .z-\[112\] {
    z-index: 112;
  }

  .z-\[115\] {
    z-index: 115;
  }

  .order-1 {
    order: 1;
  }

  .col-span-4 {
    grid-column: span 4 / span 4;
  }

  .col-span-8 {
    grid-column: span 8 / span 8;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .m-6 {
    margin: calc(var(--spacing) * 6);
  }

  .m-auto {
    margin: auto;
  }

  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .\!my-0 {
    margin-block: calc(var(--spacing) * 0) !important;
  }

  .my-0 {
    margin-block: calc(var(--spacing) * 0);
  }

  .my-0\.5 {
    margin-block: calc(var(--spacing) * .5);
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .my-auto {
    margin-block: auto;
  }

  .ms-4 {
    margin-inline-start: calc(var(--spacing) * 4);
  }

  .ms-auto {
    margin-inline-start: auto;
  }

  .me-1\.5 {
    margin-inline-end: calc(var(--spacing) * 1.5);
  }

  .me-auto {
    margin-inline-end: auto;
  }

  .-mt-1 {
    margin-top: calc(var(--spacing) * -1);
  }

  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }

  .mt-\[0\.04rem\] {
    margin-top: .04rem;
  }

  .mt-\[0\.15rem\] {
    margin-top: .15rem;
  }

  .mt-\[1px\] {
    margin-top: 1px;
  }

  .mt-auto {
    margin-top: auto;
  }

  .\!mr-0\.5 {
    margin-right: calc(var(--spacing) * .5) !important;
  }

  .\!mr-2 {
    margin-right: calc(var(--spacing) * 2) !important;
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-\[0\.1rem\] {
    margin-bottom: .1rem;
  }

  .mb-auto {
    margin-bottom: auto;
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-1\.5 {
    margin-left: calc(var(--spacing) * 1.5);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }

  .ml-auto {
    margin-left: auto;
  }

  .box-border {
    box-sizing: border-box;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .aspect-auto {
    aspect-ratio: auto;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .\!h-4 {
    height: calc(var(--spacing) * 4) !important;
  }

  .h-0\.5 {
    height: calc(var(--spacing) * .5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-11 {
    height: calc(var(--spacing) * 11);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-40 {
    height: calc(var(--spacing) * 40);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-\[1\.2rem\] {
    height: 1.2rem;
  }

  .h-\[1px\] {
    height: 1px;
  }

  .h-\[25\%\] {
    height: 25%;
  }

  .h-\[350px\] {
    height: 350px;
  }

  .h-auto {
    height: auto;
  }

  .h-fit {
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-max {
    height: max-content;
  }

  .h-min {
    height: min-content;
  }

  .h-screen {
    height: 100vh;
  }

  .\!max-h-screen {
    max-height: 100vh !important;
  }

  .max-h-0 {
    max-height: calc(var(--spacing) * 0);
  }

  .max-h-8 {
    max-height: calc(var(--spacing) * 8);
  }

  .max-h-32 {
    max-height: calc(var(--spacing) * 32);
  }

  .max-h-36 {
    max-height: calc(var(--spacing) * 36);
  }

  .max-h-40 {
    max-height: calc(var(--spacing) * 40);
  }

  .max-h-48 {
    max-height: calc(var(--spacing) * 48);
  }

  .max-h-52 {
    max-height: calc(var(--spacing) * 52);
  }

  .max-h-64 {
    max-height: calc(var(--spacing) * 64);
  }

  .max-h-80 {
    max-height: calc(var(--spacing) * 80);
  }

  .max-h-\[85vh\] {
    max-height: 85vh;
  }

  .max-h-\[95vh\] {
    max-height: 95vh;
  }

  .max-h-\[200px\] {
    max-height: 200px;
  }

  .max-h-full {
    max-height: 100%;
  }

  .max-h-max {
    max-height: max-content;
  }

  .min-h-9 {
    min-height: calc(var(--spacing) * 9);
  }

  .min-h-10 {
    min-height: calc(var(--spacing) * 10);
  }

  .min-h-12 {
    min-height: calc(var(--spacing) * 12);
  }

  .min-h-56 {
    min-height: calc(var(--spacing) * 56);
  }

  .min-h-\[1em\] {
    min-height: 1em;
  }

  .min-h-\[39px\] {
    min-height: 39px;
  }

  .min-h-\[80px\] {
    min-height: 80px;
  }

  .min-h-\[100px\] {
    min-height: 100px;
  }

  .min-h-full {
    min-height: 100%;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .\!w-4 {
    width: calc(var(--spacing) * 4) !important;
  }

  .\!w-28 {
    width: calc(var(--spacing) * 28) !important;
  }

  .\!w-32 {
    width: calc(var(--spacing) * 32) !important;
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-1\/3 {
    width: 33.3333%;
  }

  .w-1\/4 {
    width: 25%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-5\/6 {
    width: 83.3333%;
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-72 {
    width: calc(var(--spacing) * 72);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-96 {
    width: calc(var(--spacing) * 96);
  }

  .w-\[1\.2rem\] {
    width: 1.2rem;
  }

  .w-\[1px\] {
    width: 1px;
  }

  .w-\[1rem\] {
    width: 1rem;
  }

  .w-\[25\%\] {
    width: 25%;
  }

  .w-\[60vw\] {
    width: 60vw;
  }

  .w-\[calc\(100vw-10px\)\] {
    width: calc(100vw - 10px);
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-max {
    width: max-content;
  }

  .w-min {
    width: min-content;
  }

  .w-screen {
    width: 100vw;
  }

  .\!max-w-5 {
    max-width: calc(var(--spacing) * 5) !important;
  }

  .\!max-w-7 {
    max-width: calc(var(--spacing) * 7) !important;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-8 {
    max-width: calc(var(--spacing) * 8);
  }

  .max-w-10 {
    max-width: calc(var(--spacing) * 10);
  }

  .max-w-56 {
    max-width: calc(var(--spacing) * 56);
  }

  .max-w-72 {
    max-width: calc(var(--spacing) * 72);
  }

  .max-w-80 {
    max-width: calc(var(--spacing) * 80);
  }

  .max-w-\[95vw\] {
    max-width: 95vw;
  }

  .max-w-\[100vw\] {
    max-width: 100vw;
  }

  .max-w-\[150px\] {
    max-width: 150px;
  }

  .max-w-\[300px\] {
    max-width: 300px;
  }

  .max-w-\[800px\] {
    max-width: 800px;
  }

  .max-w-\[calc\(100vw-2rem\)\] {
    max-width: calc(100vw - 2rem);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-max {
    max-width: max-content;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-min {
    max-width: min-content;
  }

  .max-w-screen-lg {
    max-width: var(--breakpoint-lg);
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-1 {
    min-width: calc(var(--spacing) * 1);
  }

  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }

  .min-w-14 {
    min-width: calc(var(--spacing) * 14);
  }

  .min-w-32 {
    min-width: calc(var(--spacing) * 32);
  }

  .min-w-44 {
    min-width: calc(var(--spacing) * 44);
  }

  .min-w-\[20vw\] {
    min-width: 20vw;
  }

  .min-w-\[35vw\] {
    min-width: 35vw;
  }

  .min-w-full {
    min-width: 100%;
  }

  .min-w-max {
    min-width: max-content;
  }

  .min-w-min {
    min-width: min-content;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .grow {
    flex-grow: 1;
  }

  .table-fixed {
    table-layout: fixed;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-2\.5 {
    --tw-translate-x: calc(var(--spacing) * -2.5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-1\/3 {
    --tw-translate-x: calc(1 / 3 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-8 {
    --tw-translate-x: calc(var(--spacing) * 8);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\!translate-y-\[0\%\] {
    --tw-translate-y: 0% !important;
    translate: var(--tw-translate-x) var(--tw-translate-y) !important;
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-\[10\%\] {
    --tw-translate-y: calc(10% * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-full {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-1\/3 {
    --tw-translate-y: calc(1 / 3 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-8 {
    --tw-translate-y: calc(var(--spacing) * 8);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-full {
    --tw-translate-y: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-95 {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-0 {
    rotate: none;
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .-skew-x-12 {
    --tw-skew-x: skewX(calc(12deg * -1));
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .\!cursor-default {
    cursor: default !important;
  }

  .cursor-auto {
    cursor: auto;
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize {
    resize: both;
  }

  .resize-none {
    resize: none;
  }

  .list-inside {
    list-style-position: inside;
  }

  .list-disc {
    list-style-type: disc;
  }

  .list-none {
    list-style-type: none;
  }

  .appearance-auto {
    appearance: auto;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .\!flex-row {
    flex-direction: row !important;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .flex-wrap-reverse {
    flex-wrap: wrap-reverse;
  }

  .place-items-center {
    place-items: center;
  }

  .\!items-center {
    align-items: center !important;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-around {
    justify-content: space-around;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-0\.5 {
    gap: calc(var(--spacing) * .5);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-\[1px\] {
    gap: 1px;
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-12 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-16 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 16) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-y-reverse)));
  }

  .gap-x-0 {
    column-gap: calc(var(--spacing) * 0);
  }

  .gap-x-0\.5 {
    column-gap: calc(var(--spacing) * .5);
  }

  .gap-x-1 {
    column-gap: calc(var(--spacing) * 1);
  }

  .gap-x-1\.5 {
    column-gap: calc(var(--spacing) * 1.5);
  }

  .gap-x-2 {
    column-gap: calc(var(--spacing) * 2);
  }

  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }

  .gap-x-\[1px\] {
    column-gap: 1px;
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  .gap-y-0\.5 {
    row-gap: calc(var(--spacing) * .5);
  }

  .gap-y-1 {
    row-gap: calc(var(--spacing) * 1);
  }

  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }

  .gap-y-3 {
    row-gap: calc(var(--spacing) * 3);
  }

  .gap-y-4 {
    row-gap: calc(var(--spacing) * 4);
  }

  .gap-y-6 {
    row-gap: calc(var(--spacing) * 6);
  }

  .gap-y-10 {
    row-gap: calc(var(--spacing) * 10);
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-visible {
    overflow: visible;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .overscroll-x-none {
    overscroll-behavior-x: none;
  }

  .\!rounded-full {
    border-radius: 3.40282e38px !important;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-\[4px\] {
    border-radius: 4px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-\[5px\] {
    border-style: var(--tw-border-style);
    border-width: 5px;
  }

  .\!border-t-0 {
    border-top-style: var(--tw-border-style) !important;
    border-top-width: 0 !important;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .\!border-b-0 {
    border-bottom-style: var(--tw-border-style) !important;
    border-bottom-width: 0 !important;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }

  .\!border-transparent {
    border-color: #0000 !important;
  }

  .border-\[rgba\(195\,195\,195\,0\.6\)\] {
    border-color: #c3c3c399;
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-border {
    border-color: hsl(var(--border));
  }

  .border-gray-700 {
    border-color: var(--color-gray-700);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-green-500 {
    border-color: var(--color-green-500);
  }

  .border-input {
    border-color: hsl(var(--input));
  }

  .border-primary {
    border-color: hsl(var(--primary));
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-sky-300 {
    border-color: var(--color-sky-300);
  }

  .border-slate-50 {
    border-color: var(--color-slate-50);
  }

  .border-slate-100 {
    border-color: var(--color-slate-100);
  }

  .border-slate-200 {
    border-color: var(--color-slate-200);
  }

  .border-slate-300 {
    border-color: var(--color-slate-300);
  }

  .border-slate-400 {
    border-color: var(--color-slate-400);
  }

  .border-slate-600 {
    border-color: var(--color-slate-600);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .border-yellow-500 {
    border-color: var(--color-yellow-500);
  }

  .border-zinc-300 {
    border-color: var(--color-zinc-300);
  }

  .border-x-transparent {
    border-inline-color: #0000;
  }

  .border-t-transparent {
    border-top-color: #0000;
  }

  .border-t-white {
    border-top-color: var(--color-white);
  }

  .border-b-slate-300 {
    border-bottom-color: var(--color-slate-300);
  }

  .\!bg-sky-100 {
    background-color: var(--color-sky-100) !important;
  }

  .\!bg-slate-50 {
    background-color: var(--color-slate-50) !important;
  }

  .\!bg-slate-100 {
    background-color: var(--color-slate-100) !important;
  }

  .\!bg-white {
    background-color: var(--color-white) !important;
  }

  .bg-\[\#dddbdd\] {
    background-color: #dddbdd;
  }

  .bg-accent {
    background-color: hsl(var(--accent));
  }

  .bg-accent\/30 {
    background-color: hsl(var(--accent));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-accent\/30 {
      background-color: color-mix(in oklab, hsl(var(--accent)) 30%, transparent);
    }
  }

  .bg-background {
    background-color: hsl(var(--background));
  }

  .bg-background\/80 {
    background-color: hsl(var(--background));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/80 {
      background-color: color-mix(in oklab, hsl(var(--background)) 80%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-card {
    background-color: hsl(var(--card));
  }

  .bg-destructive\/10 {
    background-color: hsl(var(--destructive));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-destructive\/10 {
      background-color: color-mix(in oklab, hsl(var(--destructive)) 10%, transparent);
    }
  }

  .bg-foreground {
    background-color: hsl(var(--foreground));
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-muted {
    background-color: hsl(var(--muted));
  }

  .bg-muted\/20 {
    background-color: hsl(var(--muted));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/20 {
      background-color: color-mix(in oklab, hsl(var(--muted)) 20%, transparent);
    }
  }

  .bg-muted\/30 {
    background-color: hsl(var(--muted));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/30 {
      background-color: color-mix(in oklab, hsl(var(--muted)) 30%, transparent);
    }
  }

  .bg-muted\/50 {
    background-color: hsl(var(--muted));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, hsl(var(--muted)) 50%, transparent);
    }
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }

  .bg-primary {
    background-color: hsl(var(--primary));
  }

  .bg-primary\/10 {
    background-color: hsl(var(--primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/10 {
      background-color: color-mix(in oklab, hsl(var(--primary)) 10%, transparent);
    }
  }

  .bg-primary\/20 {
    background-color: hsl(var(--primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/20 {
      background-color: color-mix(in oklab, hsl(var(--primary)) 20%, transparent);
    }
  }

  .bg-primary\/40 {
    background-color: hsl(var(--primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/40 {
      background-color: color-mix(in oklab, hsl(var(--primary)) 40%, transparent);
    }
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-secondary\/10 {
    background-color: hsl(var(--secondary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-secondary\/10 {
      background-color: color-mix(in oklab, hsl(var(--secondary)) 10%, transparent);
    }
  }

  .bg-sky-100 {
    background-color: var(--color-sky-100);
  }

  .bg-slate-50 {
    background-color: var(--color-slate-50);
  }

  .bg-slate-100 {
    background-color: var(--color-slate-100);
  }

  .bg-slate-200 {
    background-color: var(--color-slate-200);
  }

  .bg-slate-300 {
    background-color: var(--color-slate-300);
  }

  .bg-slate-400 {
    background-color: var(--color-slate-400);
  }

  .bg-slate-500 {
    background-color: var(--color-slate-500);
  }

  .bg-slate-600 {
    background-color: var(--color-slate-600);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/25 {
    background-color: #ffffff40;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/25 {
      background-color: color-mix(in oklab, var(--color-white) 25%, transparent);
    }
  }

  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-zinc-200 {
    background-color: var(--color-zinc-200);
  }

  .bg-zinc-700 {
    background-color: var(--color-zinc-700);
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-amber-500 {
    --tw-gradient-from: var(--color-amber-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-background {
    --tw-gradient-from: hsl(var(--background));
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-100 {
    --tw-gradient-from: var(--color-blue-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-600 {
    --tw-gradient-from: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-100 {
    --tw-gradient-from: var(--color-green-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-600 {
    --tw-gradient-from: var(--color-green-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-50 {
    --tw-gradient-from: var(--color-orange-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-100 {
    --tw-gradient-from: var(--color-orange-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-400 {
    --tw-gradient-from: var(--color-orange-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-600 {
    --tw-gradient-from: var(--color-orange-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-600 {
    --tw-gradient-from: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-yellow-500 {
    --tw-gradient-from: var(--color-yellow-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-amber-500 {
    --tw-gradient-via: var(--color-amber-500);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-white\/20 {
    --tw-gradient-via: #fff3;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-white\/20 {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .to-amber-50 {
    --tw-gradient-to: var(--color-amber-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-amber-100 {
    --tw-gradient-to: var(--color-amber-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-amber-400 {
    --tw-gradient-to: var(--color-amber-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-amber-500 {
    --tw-gradient-to: var(--color-amber-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-600 {
    --tw-gradient-to: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-100 {
    --tw-gradient-to: var(--color-emerald-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-500 {
    --tw-gradient-to: var(--color-emerald-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-muted\/30 {
    --tw-gradient-to: hsl(var(--muted));
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-muted\/30 {
      --tw-gradient-to: color-mix(in oklab, hsl(var(--muted)) 30%, transparent);
    }
  }

  .to-orange-500 {
    --tw-gradient-to: var(--color-orange-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-rose-500 {
    --tw-gradient-to: var(--color-rose-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-rose-700 {
    --tw-gradient-to: var(--color-rose-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-sky-100 {
    --tw-gradient-to: var(--color-sky-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-sky-500 {
    --tw-gradient-to: var(--color-sky-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-sky-600 {
    --tw-gradient-to: var(--color-sky-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-yellow-500 {
    --tw-gradient-to: var(--color-yellow-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-clip-border {
    background-clip: border-box;
  }

  .bg-clip-text {
    background-clip: text;
  }

  .fill-white {
    fill: var(--color-white);
  }

  .fill-zinc-400 {
    fill: var(--color-zinc-400);
  }

  .stroke-2 {
    stroke-width: 2px;
  }

  .stroke-\[3\] {
    stroke-width: 3px;
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-0\.5 {
    padding: calc(var(--spacing) * .5);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-0\.5 {
    padding-inline: calc(var(--spacing) * .5);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .px-\[12px\] {
    padding-inline: 12px;
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-7 {
    padding-block: calc(var(--spacing) * 7);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }

  .py-\[1px\] {
    padding-block: 1px;
  }

  .py-\[6px\] {
    padding-block: 6px;
  }

  .ps-1\.5 {
    padding-inline-start: calc(var(--spacing) * 1.5);
  }

  .ps-4 {
    padding-inline-start: calc(var(--spacing) * 4);
  }

  .pe-2 {
    padding-inline-end: calc(var(--spacing) * 2);
  }

  .pe-4 {
    padding-inline-end: calc(var(--spacing) * 4);
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .\!pr-6 {
    padding-right: calc(var(--spacing) * 6) !important;
  }

  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }

  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pr-12 {
    padding-right: calc(var(--spacing) * 12);
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-2\.5 {
    padding-bottom: calc(var(--spacing) * 2.5);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pl-0 {
    padding-left: calc(var(--spacing) * 0);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .pl-12 {
    padding-left: calc(var(--spacing) * 12);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-start {
    text-align: start;
  }

  .align-middle {
    vertical-align: middle;
  }

  .font-mono {
    font-family: var(--font-geist-mono);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[0\.9rem\] {
    font-size: .9rem;
  }

  .text-\[0\.65rem\] {
    font-size: .65rem;
  }

  .text-\[10px\] {
    font-size: 10px;
  }

  .text-\[clamp\(0\.65rem\,50cqw\,10rem\)\] {
    font-size: clamp(.65rem, 50cqw, 10rem);
  }

  .\!leading-\[1\.15\] {
    --tw-leading: 1.15 !important;
    line-height: 1.15 !important;
  }

  .leading-3 {
    --tw-leading: calc(var(--spacing) * 3);
    line-height: calc(var(--spacing) * 3);
  }

  .leading-4 {
    --tw-leading: calc(var(--spacing) * 4);
    line-height: calc(var(--spacing) * 4);
  }

  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }

  .leading-7 {
    --tw-leading: calc(var(--spacing) * 7);
    line-height: calc(var(--spacing) * 7);
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-snug {
    --tw-leading: var(--leading-snug);
    line-height: var(--leading-snug);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-\[400\] {
    --tw-font-weight: 400;
    font-weight: 400;
  }

  .font-\[450\] {
    --tw-font-weight: 450;
    font-weight: 450;
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .text-ellipsis {
    text-overflow: ellipsis;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .\!text-slate-400 {
    color: var(--color-slate-400) !important;
  }

  .\!text-white {
    color: var(--color-white) !important;
  }

  .text-accent {
    color: hsl(var(--accent));
  }

  .text-accent-foreground {
    color: hsl(var(--accent-foreground));
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-blue-900 {
    color: var(--color-blue-900);
  }

  .text-card-foreground {
    color: hsl(var(--card-foreground));
  }

  .text-destructive {
    color: hsl(var(--destructive));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-green-900 {
    color: var(--color-green-900);
  }

  .text-inherit {
    color: inherit;
  }

  .text-muted-foreground {
    color: hsl(var(--muted-foreground));
  }

  .text-muted-foreground\/50 {
    color: hsl(var(--muted-foreground));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-muted-foreground\/50 {
      color: color-mix(in oklab, hsl(var(--muted-foreground)) 50%, transparent);
    }
  }

  .text-orange-800 {
    color: var(--color-orange-800);
  }

  .text-primary {
    color: hsl(var(--primary));
  }

  .text-primary-foreground {
    color: hsl(var(--primary-foreground));
  }

  .text-primary\/60 {
    color: hsl(var(--primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary\/60 {
      color: color-mix(in oklab, hsl(var(--primary)) 60%, transparent);
    }
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-red-900 {
    color: var(--color-red-900);
  }

  .text-secondary {
    color: hsl(var(--secondary));
  }

  .text-secondary-foreground {
    color: hsl(var(--secondary-foreground));
  }

  .text-slate-400 {
    color: var(--color-slate-400);
  }

  .text-slate-500 {
    color: var(--color-slate-500);
  }

  .text-slate-700 {
    color: var(--color-slate-700);
  }

  .text-transparent {
    color: #0000;
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .text-yellow-900 {
    color: var(--color-yellow-900);
  }

  .text-zinc-400 {
    color: var(--color-zinc-400);
  }

  .text-zinc-500 {
    color: var(--color-zinc-500);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .lowercase {
    text-transform: lowercase;
  }

  .normal-case {
    text-transform: none;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .no-underline {
    text-decoration-line: none;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[rgb\(213\,213\,213\,05\)_0px_0px_10px_0px\] {
    --tw-shadow-color: #d5d5d5;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-\[rgb\(213\,213\,213\,05\)_0px_0px_10px_0px\] {
      --tw-shadow-color: color-mix(in oklab, #d5d5d5 0px 0px 10px 0px var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-\[rgb\(213\,213\,213\,05\)_0px_8px_10px_0px\] {
    --tw-shadow-color: #d5d5d5;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-\[rgb\(213\,213\,213\,05\)_0px_8px_10px_0px\] {
      --tw-shadow-color: color-mix(in oklab, #d5d5d5 0px 8px 10px 0px var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-offset-background {
    --tw-ring-offset-color: hsl(var(--background));
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .outline-0 {
    outline-style: var(--tw-outline-style);
    outline-width: 0;
  }

  .drop-shadow-md {
    --tw-drop-shadow-size: drop-shadow(0 3px 3px var(--tw-drop-shadow-color, #0000001f));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-md));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-filter {
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[opacity\] {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[transform\] {
    transition-property: transform;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .delay-100 {
    transition-delay: .1s;
  }

  .delay-200 {
    transition-delay: .2s;
  }

  .duration-100 {
    --tw-duration: .1s;
    transition-duration: .1s;
  }

  .duration-150 {
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-700 {
    --tw-duration: .7s;
    transition-duration: .7s;
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .\[animation-duration\:_1s\] {
    animation-duration: 1s;
  }

  .\[animation-iteration-count\:_infinite\] {
    animation-iteration-count: infinite;
  }

  .\[animation-timing-function\:_ease-in-out\] {
    animation-timing-function: ease-in-out;
  }

  @media (hover: hover) {
    .group-hover\:visible:is(:where(.group):hover *) {
      visibility: visible;
    }
  }

  @media (hover: hover) {
    .group-hover\:block:is(:where(.group):hover *) {
      display: block;
    }
  }

  @media (hover: hover) {
    .group-hover\:flex:is(:where(.group):hover *) {
      display: flex;
    }
  }

  @media (hover: hover) {
    .group-hover\:hidden:is(:where(.group):hover *) {
      display: none;
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-x-0\.5:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * .5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-x-1:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-slate-100:is(:where(.group):hover *) {
      background-color: var(--color-slate-100);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-white:is(:where(.group):hover *) {
      background-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .group-hover\:font-\[500\]:is(:where(.group):hover *) {
      --tw-font-weight: 500;
      font-weight: 500;
    }
  }

  @media (hover: hover) {
    .group-hover\:text-primary:is(:where(.group):hover *) {
      color: hsl(var(--primary));
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\:shadow-xl:is(:where(.group):hover *) {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .group-disabled\:text-slate-400:is(:where(.group):disabled *) {
    color: var(--color-slate-400);
  }

  .peer-checked\:opacity-100:is(:where(.peer):checked ~ *) {
    opacity: 1;
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-70:is(:where(.peer):disabled ~ *) {
    opacity: .7;
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .placeholder\:text-sm::placeholder {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: hsl(var(--muted-foreground));
  }

  .placeholder\:capitalize::placeholder {
    text-transform: capitalize;
  }

  .before\:absolute:before {
    content: var(--tw-content);
    position: absolute;
  }

  .before\:fixed:before {
    content: var(--tw-content);
    position: fixed;
  }

  .before\:top-0:before {
    content: var(--tw-content);
    top: calc(var(--spacing) * 0);
  }

  .before\:\!right-\[-1rem\]:before {
    content: var(--tw-content);
    right: -1rem !important;
  }

  .before\:right-0:before {
    content: var(--tw-content);
    right: calc(var(--spacing) * 0);
  }

  .before\:right-\[-1rem\]:before {
    content: var(--tw-content);
    right: -1rem;
  }

  .before\:bottom-0:before {
    content: var(--tw-content);
    bottom: calc(var(--spacing) * 0);
  }

  .before\:left-0:before {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
  }

  .before\:z-10:before {
    content: var(--tw-content);
    z-index: 10;
  }

  .before\:h-4:before {
    content: var(--tw-content);
    height: calc(var(--spacing) * 4);
  }

  .before\:h-screen:before {
    content: var(--tw-content);
    height: 100vh;
  }

  .before\:w-4:before {
    content: var(--tw-content);
    width: calc(var(--spacing) * 4);
  }

  .before\:w-screen:before {
    content: var(--tw-content);
    width: 100vw;
  }

  .before\:rounded-tl-\[50\%\]:before {
    content: var(--tw-content);
    border-top-left-radius: 50%;
  }

  .before\:\!opacity-100:before {
    content: var(--tw-content);
    opacity: 1 !important;
  }

  .before\:opacity-0:before {
    content: var(--tw-content);
    opacity: 0;
  }

  .before\:shadow-\[rgb\(48\,48\,48\)_-9px_-1px_0px_0px\]:before {
    content: var(--tw-content);
    --tw-shadow-color: #303030;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .before\:shadow-\[rgb\(48\,48\,48\)_-9px_-1px_0px_0px\]:before {
      --tw-shadow-color: color-mix(in oklab, #303030 -9px -1px 0px 0px var(--tw-shadow-alpha), transparent);
    }
  }

  .before\:outline-0:before {
    content: var(--tw-content);
    outline-style: var(--tw-outline-style);
    outline-width: 0;
  }

  .before\:backdrop-blur-sm:before {
    content: var(--tw-content);
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .before\:transition-\[opacity\]:before {
    content: var(--tw-content);
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .before\:transition-\[right\]:before {
    content: var(--tw-content);
    transition-property: right;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .before\:duration-150:before {
    content: var(--tw-content);
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .before\:ease-in-out:before {
    content: var(--tw-content);
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:top-0:after {
    content: var(--tw-content);
    top: calc(var(--spacing) * 0);
  }

  .after\:top-1\/2:after {
    content: var(--tw-content);
    top: 50%;
  }

  .after\:-right-\[1px\]:after {
    content: var(--tw-content);
    right: -1px;
  }

  .after\:right-0:after {
    content: var(--tw-content);
    right: calc(var(--spacing) * 0);
  }

  .after\:bottom-0:after {
    content: var(--tw-content);
    bottom: calc(var(--spacing) * 0);
  }

  .after\:bottom-2:after {
    content: var(--tw-content);
    bottom: calc(var(--spacing) * 2);
  }

  .after\:left-0:after {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
  }

  .after\:block:after {
    content: var(--tw-content);
    display: block;
  }

  .after\:h-2\/3:after {
    content: var(--tw-content);
    height: 66.6667%;
  }

  .after\:w-\[1px\]:after {
    content: var(--tw-content);
    width: 1px;
  }

  .after\:-translate-x-full:after {
    content: var(--tw-content);
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .after\:-translate-y-1\/2:after {
    content: var(--tw-content);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .after\:transform:after {
    content: var(--tw-content);
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .after\:bg-gradient-to-r:after {
    content: var(--tw-content);
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .after\:from-transparent:after {
    content: var(--tw-content);
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .after\:via-\[rgba\(255\,255\,255\,0\.75\)\]:after {
    content: var(--tw-content);
    --tw-gradient-via: #ffffffbf;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .after\:to-transparent:after {
    content: var(--tw-content);
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .after\:content-\[\'\'\]:after {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .after\:\[animation-duration\:_2s\]:after {
    content: var(--tw-content);
    animation-duration: 2s;
  }

  .after\:\[animation-iteration-count\:_infinite\]:after {
    content: var(--tw-content);
    animation-iteration-count: infinite;
  }

  .last\:border-0:last-child {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .first-of-type\:pt-2:first-of-type {
    padding-top: calc(var(--spacing) * 2);
  }

  .last-of-type\:justify-self-stretch:last-of-type {
    justify-self: stretch;
  }

  .last-of-type\:pb-2:last-of-type {
    padding-bottom: calc(var(--spacing) * 2);
  }

  @media (hover: hover) {
    .hover\:translate-x-full:hover {
      --tw-translate-x: 100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:-translate-y-0\.5:hover {
      --tw-translate-y: calc(var(--spacing) * -.5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:-translate-y-1:hover {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:border-accent:hover {
      border-color: hsl(var(--accent));
    }
  }

  @media (hover: hover) {
    .hover\:border-green-600:hover {
      border-color: var(--color-green-600);
    }
  }

  @media (hover: hover) {
    .hover\:border-primary\/50:hover {
      border-color: hsl(var(--primary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-primary\/50:hover {
        border-color: color-mix(in oklab, hsl(var(--primary)) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-red-600:hover {
      border-color: var(--color-red-600);
    }
  }

  @media (hover: hover) {
    .hover\:\!bg-slate-100:hover {
      background-color: var(--color-slate-100) !important;
    }
  }

  @media (hover: hover) {
    .hover\:\!bg-slate-200:hover {
      background-color: var(--color-slate-200) !important;
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: hsl(var(--accent));
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent\/50:hover {
      background-color: hsl(var(--accent));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-accent\/50:hover {
        background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-background:hover {
      background-color: hsl(var(--background));
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/50:hover {
      background-color: hsl(var(--muted));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/50:hover {
        background-color: color-mix(in oklab, hsl(var(--muted)) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/80:hover {
      background-color: hsl(var(--muted));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/80:hover {
        background-color: color-mix(in oklab, hsl(var(--muted)) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/10:hover {
      background-color: hsl(var(--primary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/10:hover {
        background-color: color-mix(in oklab, hsl(var(--primary)) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: hsl(var(--primary));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, hsl(var(--primary)) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-rose-50:hover {
      background-color: var(--color-rose-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-sky-50:hover {
      background-color: var(--color-sky-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-slate-100:hover {
      background-color: var(--color-slate-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-slate-200:hover {
      background-color: var(--color-slate-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-slate-300:hover {
      background-color: var(--color-slate-300);
    }
  }

  @media (hover: hover) {
    .hover\:bg-white:hover {
      background-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:bg-zinc-100:hover {
      background-color: var(--color-zinc-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-zinc-700:hover {
      background-color: var(--color-zinc-700);
    }
  }

  @media (hover: hover) {
    .hover\:from-amber-600:hover {
      --tw-gradient-from: var(--color-amber-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-blue-700:hover {
      --tw-gradient-from: var(--color-blue-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-green-700:hover {
      --tw-gradient-from: var(--color-green-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-orange-700:hover {
      --tw-gradient-from: var(--color-orange-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-red-700:hover {
      --tw-gradient-from: var(--color-red-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-yellow-600:hover {
      --tw-gradient-from: var(--color-yellow-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-amber-600:hover {
      --tw-gradient-to: var(--color-amber-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-emerald-600:hover {
      --tw-gradient-to: var(--color-emerald-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-orange-600:hover {
      --tw-gradient-to: var(--color-orange-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-rose-600:hover {
      --tw-gradient-to: var(--color-rose-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-sky-600:hover {
      --tw-gradient-to: var(--color-sky-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-yellow-600:hover {
      --tw-gradient-to: var(--color-yellow-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: hsl(var(--accent-foreground));
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-800:hover {
      color: var(--color-blue-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground:hover {
      color: hsl(var(--foreground));
    }
  }

  @media (hover: hover) {
    .hover\:text-green-800:hover {
      color: var(--color-green-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary:hover {
      color: hsl(var(--primary));
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:text-zinc-600:hover {
      color: var(--color-zinc-600);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow:hover {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:before\:right-\[-11\.96rem\]:hover:before {
      content: var(--tw-content);
      right: -11.96rem;
    }
  }

  @media (hover: hover) {
    .hover\:placeholder-shown\:bg-slate-100:hover:placeholder-shown {
      background-color: var(--color-slate-100);
    }
  }

  .focus\:\!bg-transparent:focus {
    background-color: #0000 !important;
  }

  .focus\:bg-white:focus {
    background-color: var(--color-white);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: hsl(var(--ring));
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-0:focus {
    outline-style: var(--tw-outline-style);
    outline-width: 0;
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:border-border:focus-visible {
    border-color: hsl(var(--border));
  }

  .focus-visible\:border-green-500:focus-visible {
    border-color: var(--color-green-500);
  }

  .focus-visible\:border-primary:focus-visible {
    border-color: hsl(var(--primary));
  }

  .focus-visible\:border-red-500:focus-visible {
    border-color: var(--color-red-500);
  }

  .focus-visible\:border-yellow-500:focus-visible {
    border-color: var(--color-yellow-500);
  }

  .focus-visible\:bg-background:focus-visible {
    background-color: hsl(var(--background));
  }

  .focus-visible\:bg-muted:focus-visible {
    background-color: hsl(var(--muted));
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-green-500\/20:focus-visible {
    --tw-ring-color: #00c75833;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-green-500\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }

  .focus-visible\:ring-primary\/20:focus-visible {
    --tw-ring-color: hsl(var(--primary));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-primary\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, hsl(var(--primary)) 20%, transparent);
    }
  }

  .focus-visible\:ring-red-500\/20:focus-visible {
    --tw-ring-color: #fb2c3633;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-red-500\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);
    }
  }

  .focus-visible\:ring-ring:focus-visible {
    --tw-ring-color: hsl(var(--ring));
  }

  .focus-visible\:ring-yellow-500\/20:focus-visible {
    --tw-ring-color: #edb20033;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-yellow-500\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-yellow-500) 20%, transparent);
    }
  }

  .focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .active\:translate-y-0:active {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .active\:bg-slate-100:active {
    background-color: var(--color-slate-100);
  }

  .active\:bg-zinc-200:active {
    background-color: var(--color-zinc-200);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:border-0:disabled {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .disabled\:bg-transparent:disabled {
    background-color: #0000;
  }

  .disabled\:opacity-45:disabled {
    opacity: .45;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .disabled\:shadow-inner:disabled {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=checked\]\:bg-primary[data-state="checked"] {
    background-color: hsl(var(--primary));
  }

  .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
    color: hsl(var(--primary-foreground));
  }

  @media (width >= 40rem) {
    .sm\:inline-flex {
      display: inline-flex;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-3\/4 {
      width: 75%;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-4\/12 {
      width: 33.3333%;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[60vw\] {
      width: 60vw;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[150px\] {
      width: 150px;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[300px\] {
      width: 300px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-96 {
      max-width: calc(var(--spacing) * 96);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[50\%\] {
      max-width: 50%;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[75\%\] {
      max-width: 75%;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[80vw\] {
      max-width: 80vw;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-screen-sm {
      max-width: var(--breakpoint-sm);
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-1 {
      flex: 1;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-\[2\] {
      flex: 2;
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:rounded-lg {
      border-radius: var(--radius-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-4 {
      padding-block: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-32 {
      padding-block: calc(var(--spacing) * 32);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:relative {
      position: relative;
    }
  }

  @media (width >= 48rem) {
    .md\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:block {
      display: block;
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (width >= 48rem) {
    .md\:w-1\/2 {
      width: 50%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[60px\] {
      width: 60px;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[235px\] {
      width: 235px;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[40vw\] {
      max-width: 40vw;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[50\%\] {
      max-width: 50%;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[75\%\] {
      max-width: 75%;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[80\%\] {
      max-width: 80%;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[85\%\] {
      max-width: 85%;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[90\%\] {
      max-width: 90%;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[235px\] {
      max-width: 235px;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-screen-md {
      max-width: var(--breakpoint-md);
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-10 {
      grid-template-columns: repeat(10, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-\[2fr\,1fr\] {
      grid-template-columns: 2fr, 1fr;
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:py-8 {
      padding-block: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-7xl {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (hover: hover) {
    @media (width >= 48rem) {
      .group-hover\:md\:w-\[235px\]:is(:where(.group):hover *) {
        width: 235px;
      }
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-1\/3 {
      width: 33.3333%;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-\[60\%\] {
      max-width: 60%;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-\[75\%\] {
      max-width: 75%;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-\[95\%\] {
      max-width: 95%;
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-screen-lg {
      max-width: var(--breakpoint-lg);
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 80rem) {
    .xl\:max-w-\[85\%\] {
      max-width: 85%;
    }
  }

  @media (width >= 80rem) {
    .xl\:max-w-screen-xl {
      max-width: var(--breakpoint-xl);
    }
  }

  @media (width >= 96rem) {
    .\32 xl\:max-w-screen-2xl {
      max-width: var(--breakpoint-2xl);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:scale-0 {
      --tw-scale-x: 0%;
      --tw-scale-y: 0%;
      --tw-scale-z: 0%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:scale-100 {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:-rotate-90 {
      rotate: -90deg;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:rotate-0 {
      rotate: none;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-blue-800 {
      border-color: var(--color-blue-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-green-800 {
      border-color: var(--color-green-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-red-800 {
      border-color: var(--color-red-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-yellow-800 {
      border-color: var(--color-yellow-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-900\/20 {
      background-color: #1c398e33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-blue-900\/20 {
        background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-950\/20 {
      background-color: #16245633;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-blue-950\/20 {
        background-color: color-mix(in oklab, var(--color-blue-950) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-900\/20 {
      background-color: #0d542b33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-green-900\/20 {
        background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-950\/20 {
      background-color: #032e1533;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-green-950\/20 {
        background-color: color-mix(in oklab, var(--color-green-950) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-orange-900\/20 {
      background-color: #7e2a0c33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-orange-900\/20 {
        background-color: color-mix(in oklab, var(--color-orange-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-purple-900\/20 {
      background-color: #59168b33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-purple-900\/20 {
        background-color: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-red-900\/20 {
      background-color: #82181a33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-red-900\/20 {
        background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-red-950\/20 {
      background-color: #46080933;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-red-950\/20 {
        background-color: color-mix(in oklab, var(--color-red-950) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-slate-900 {
      background-color: var(--color-slate-900);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-yellow-900\/20 {
      background-color: #733e0a33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-yellow-900\/20 {
        background-color: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-yellow-950\/20 {
      background-color: #43200433;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-yellow-950\/20 {
        background-color: color-mix(in oklab, var(--color-yellow-950) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-blue-900\/20 {
      --tw-gradient-from: #1c398e33;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-blue-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-green-900\/20 {
      --tw-gradient-from: #0d542b33;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-green-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-orange-900\/20 {
      --tw-gradient-from: #7e2a0c33;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-orange-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-orange-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-orange-950\/20 {
      --tw-gradient-from: #44130633;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-orange-950\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-orange-950) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-amber-900\/20 {
      --tw-gradient-to: #7b330633;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-amber-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-amber-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-amber-950\/20 {
      --tw-gradient-to: #46190133;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-amber-950\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-amber-950) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-emerald-900\/20 {
      --tw-gradient-to: #004e3b33;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-emerald-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-emerald-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-sky-900\/20 {
      --tw-gradient-to: #024a7033;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-sky-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-sky-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-100 {
      color: var(--color-blue-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-200 {
      color: var(--color-blue-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-400 {
      color: var(--color-blue-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-100 {
      color: var(--color-green-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-200 {
      color: var(--color-green-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-400 {
      color: var(--color-green-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-orange-200 {
      color: var(--color-orange-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-orange-400 {
      color: var(--color-orange-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-purple-400 {
      color: var(--color-purple-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-100 {
      color: var(--color-red-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-200 {
      color: var(--color-red-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-400 {
      color: var(--color-red-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-100 {
      color: var(--color-yellow-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-200 {
      color: var(--color-yellow-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-400 {
      color: var(--color-yellow-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-blue-200:hover {
        color: var(--color-blue-200);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-green-200:hover {
        color: var(--color-green-200);
      }
    }
  }

  .\[\&_\.column-resize-handle\]\:absolute .column-resize-handle {
    position: absolute;
  }

  .\[\&_\.column-resize-handle\]\:top-0 .column-resize-handle {
    top: calc(var(--spacing) * 0);
  }

  .\[\&_\.column-resize-handle\]\:right-\[-3px\] .column-resize-handle {
    right: -3px;
  }

  .\[\&_\.column-resize-handle\]\:bottom-0 .column-resize-handle {
    bottom: calc(var(--spacing) * 0);
  }

  .\[\&_\.column-resize-handle\]\:z-\[100\] .column-resize-handle {
    z-index: 100;
  }

  .\[\&_\.column-resize-handle\]\:w-\[4px\] .column-resize-handle {
    width: 4px;
  }

  .\[\&_\.column-resize-handle\]\:cursor-col-resize .column-resize-handle {
    cursor: col-resize;
  }

  .\[\&_\.column-resize-handle\]\:bg-transparent .column-resize-handle {
    background-color: #0000;
  }

  .\[\&_\.column-resize-handle\]\:transition-colors .column-resize-handle {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .\[\&_\.column-resize-handle\]\:duration-200 .column-resize-handle {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .\[\&_\.input-wrapper\]\:\!border-none .input-wrapper {
    --tw-border-style: none !important;
    border-style: none !important;
  }

  .\[\&_\.input-wrapper\]\:font-medium .input-wrapper {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .\[\&_\.remirror-editor\]\:relative .remirror-editor {
    position: relative;
  }

  .\[\&_\.remirror-editor\]\:my-2 .remirror-editor {
    margin-block: calc(var(--spacing) * 2);
  }

  .\[\&_\.remirror-editor\]\:min-h-60 .remirror-editor {
    min-height: calc(var(--spacing) * 60);
  }

  .\[\&_\.remirror-editor\]\:overflow-y-auto .remirror-editor {
    overflow-y: auto;
  }

  .\[\&_\.remirror-editor\]\:p-4 .remirror-editor {
    padding: calc(var(--spacing) * 4);
  }

  .\[\&_\.remirror-editor\]\:break-words .remirror-editor {
    overflow-wrap: break-word;
  }

  .\[\&_\.remirror-editor\]\:whitespace-break-spaces .remirror-editor {
    white-space: break-spaces;
  }

  .\[\&_\.remirror-editor\]\:shadow-sm .remirror-editor {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .\[\&_\.remirror-editor\]\:outline-0 .remirror-editor {
    outline-style: var(--tw-outline-style);
    outline-width: 0;
  }

  .before\:\[\&_\.remirror-editor_\.remirror-is-empty\]\:pointer-events-none:before {
    content: var(--tw-content);
  }

  .before\:\[\&_\.remirror-editor_\.remirror-is-empty\]\:pointer-events-none:before .remirror-editor .remirror-is-empty {
    pointer-events: none;
  }

  .before\:\[\&_\.remirror-editor_\.remirror-is-empty\]\:absolute:before {
    content: var(--tw-content);
  }

  .before\:\[\&_\.remirror-editor_\.remirror-is-empty\]\:absolute:before .remirror-editor .remirror-is-empty {
    position: absolute;
  }

  .before\:\[\&_\.remirror-editor_\.remirror-is-empty\]\:italic:before {
    content: var(--tw-content);
  }

  .before\:\[\&_\.remirror-editor_\.remirror-is-empty\]\:italic:before .remirror-editor .remirror-is-empty {
    font-style: italic;
  }

  .before\:\[\&_\.remirror-editor_\.remirror-is-empty\]\:content-\[attr\(data-placeholder\)\]:before {
    content: var(--tw-content);
  }

  .before\:\[\&_\.remirror-editor_\.remirror-is-empty\]\:content-\[attr\(data-placeholder\)\]:before .remirror-editor .remirror-is-empty {
    --tw-content: attr(data-placeholder);
    content: var(--tw-content);
  }

  .\[\&_\.remirror-list-item-marker-container\]\:absolute .remirror-list-item-marker-container {
    position: absolute;
  }

  .\[\&_\.remirror-list-item-marker-container\]\:-left-5 .remirror-list-item-marker-container {
    left: calc(var(--spacing) * -5);
  }

  .\[\&_\.remirror-list-item-marker-container\]\:inline-block .remirror-list-item-marker-container {
    display: inline-block;
  }

  .\[\&_\.remirror-list-item-marker-container\]\:text-center .remirror-list-item-marker-container {
    text-align: center;
  }

  .\[\&_\.remirror-list-item-marker-container\]\:select-none .remirror-list-item-marker-container {
    -webkit-user-select: none;
    user-select: none;
  }

  .\[\&_svg\]\:h-3\.5 svg {
    height: calc(var(--spacing) * 3.5);
  }

  .\[\&_svg\]\:w-3\.5 svg {
    width: calc(var(--spacing) * 3.5);
  }

  .\[\&\:\:-webkit-scrollbar\]\:w-0::-webkit-scrollbar {
    width: calc(var(--spacing) * 0);
  }

  .\[\&\:hover\>\.child-dropdown\]\:block:hover > .child-dropdown {
    display: block;
  }

  .\[\&\:not\(button\)\]\:px-3:not(button) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .\[\&\:not\(button\)\]\:py-\[0\.55rem\]:not(button) {
    padding-block: .55rem;
  }

  .\[\&\>\*\]\:w-full > * {
    width: 100%;
  }

  .\[\&\>svg\]\:h-3 > svg {
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:h-4 > svg {
    height: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:h-5 > svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&\>svg\]\:h-full > svg {
    height: 100%;
  }

  .\[\&\>svg\]\:w-3 > svg {
    width: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:w-4 > svg {
    width: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:w-5 > svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&\>svg\]\:w-full > svg {
    width: 100%;
  }

  .\[\&\>svg\]\:fill-inherit > svg {
    fill: inherit;
  }

  .\[\&\>svg\]\:stroke-1 > svg {
    stroke-width: 1px;
  }

  .\[\&\>svg\]\:transition-colors > svg {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .\[\&\>tr\>th\]\:sticky > tr > th {
    position: sticky;
  }

  .\[\&\>tr\>th\]\:top-0 > tr > th {
    top: calc(var(--spacing) * 0);
  }

  .\[\&\>tr\>th\]\:h-10 > tr > th {
    height: calc(var(--spacing) * 10);
  }

  .\[\&\>tr\>th\]\:border-b > tr > th {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .\[\&\>tr\>th\]\:border-solid > tr > th {
    --tw-border-style: solid;
    border-style: solid;
  }

  .\[\&\>tr\>th\]\:border-slate-100 > tr > th {
    border-color: var(--color-slate-100);
  }

  .\[\&\>tr\>th\]\:leading-normal > tr > th {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }
}

:root {
  --legalyard-orange: 18 75% 55%;
  --legalyard-gold: 39 78% 61%;
  --legalyard-blue: 218 58% 55%;
  --legalyard-sky: 200 73% 67%;
  --background: 0 0% 100%;
  --foreground: 222 47% 11%;
  --card: 0 0% 100%;
  --card-foreground: 222 47% 11%;
  --popover: 0 0% 100%;
  --popover-foreground: 222 47% 11%;
  --primary: var(--legalyard-orange);
  --primary-foreground: 0 0% 98%;
  --secondary: var(--legalyard-gold);
  --secondary-foreground: 222 47% 11%;
  --accent: var(--legalyard-sky);
  --accent-foreground: 222 47% 11%;
  --muted: 210 40% 96%;
  --muted-foreground: 215 16% 47%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 98%;
  --border: 214 32% 91%;
  --input: 214 32% 91%;
  --ring: var(--legalyard-orange);
  --radius: .75rem;
  --brand-gradient: linear-gradient(135deg, hsl(var(--legalyard-orange)) 0%, hsl(var(--legalyard-gold)) 100%);
  --brand-gradient-blue: linear-gradient(135deg, hsl(var(--legalyard-blue)) 0%, hsl(var(--legalyard-sky)) 100%);
}

.dark {
  --background: 222 47% 5%;
  --foreground: 210 40% 98%;
  --card: 222 47% 7%;
  --card-foreground: 210 40% 98%;
  --popover: 222 47% 7%;
  --popover-foreground: 210 40% 98%;
  --primary: var(--legalyard-orange);
  --primary-foreground: 0 0% 98%;
  --secondary: var(--legalyard-gold);
  --secondary-foreground: 0 0% 98%;
  --accent: var(--legalyard-sky);
  --accent-foreground: 222 47% 11%;
  --muted: 217 33% 15%;
  --muted-foreground: 215 20% 65%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 210 40% 98%;
  --border: 217 33% 15%;
  --input: 217 33% 15%;
  --ring: var(--legalyard-orange);
}

* {
  border-color: hsl(var(--border));
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-geist-sans), system-ui, sans-serif;
}

.legalyard-gradient {
  background: var(--brand-gradient);
}

.legalyard-gradient-blue {
  background: var(--brand-gradient-blue);
}

.legalyard-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius)  + 2px);
  transition: all .2s ease-in-out;
  box-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
}

.legalyard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
}

.legalyard-button {
  background: var(--brand-gradient);
  border-radius: var(--radius);
  color: hsl(var(--primary-foreground));
  border: none;
  padding: .75rem 1.5rem;
  font-weight: 600;
  transition: all .2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.legalyard-button:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.legalyard-button:hover:before {
  left: 100%;
}

.legalyard-button:hover {
  box-shadow: 0 8px 25px -8px hsl(var(--legalyard-orange) / .5);
  transform: translateY(-1px);
}

.legalyard-nav {
  backdrop-filter: blur(20px);
  background: hsl(var(--background) / .8);
  border-bottom: 1px solid hsl(var(--border));
}

.legalyard-hero {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--legalyard-orange) / .05) 50%, hsl(var(--legalyard-blue) / .05) 100%);
}

.legalyard-code-block {
  background: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  position: relative;
}

.legalyard-code-block:before {
  content: "";
  background: var(--brand-gradient);
  border-radius: var(--radius) var(--radius) 0 0;
  height: 2px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / .3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / .5);
}

.legalyard-focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

.legalyard-fade-in {
  animation: .5s ease-in-out fadeIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.legalyard-slide-up {
  animation: .3s ease-out slideUp;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/