{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/code-block.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CodeBlock = registerClientReference(\n    function() { throw new Error(\"Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/code-block.tsx <module evaluation>\",\n    \"CodeBlock\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/code-block.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CodeBlock = registerClientReference(\n    function() { throw new Error(\"Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/code-block.tsx\",\n    \"CodeBlock\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/app/installation/page.tsx"], "sourcesContent": ["import { PageLayout } from \"@/components/page-layout\"\nimport { CodeBlock } from \"@/components/code-block\"\n\nexport default function InstallationPage() {\n  return (\n    <PageLayout>\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold tracking-tight mb-4\">Installation</h1>\n          <p className=\"text-xl text-muted-foreground\">\n            Get started with Legalyard Design System in your React project.\n          </p>\n        </div>\n\n        <div className=\"space-y-8\">\n          {/* Prerequisites */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Prerequisites</h2>\n            <div className=\"bg-muted/50 rounded-lg p-6\">\n              <p className=\"mb-4\">Before installing, make sure you have:</p>\n              <ul className=\"list-disc list-inside space-y-2 text-muted-foreground\">\n                <li>Node.js 18.0 or later</li>\n                <li>React 18.0 or later</li>\n                <li>TypeScript (recommended)</li>\n                <li>Tailwind CSS configured in your project</li>\n              </ul>\n            </div>\n          </section>\n\n          {/* Installation Steps */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Installation Steps</h2>\n            \n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium mb-2\">1. Install Dependencies</h3>\n                <CodeBlock code=\"npm install @legalyard/design-system\" />\n                <p className=\"text-sm text-muted-foreground mt-2\">\n                  Or if you prefer yarn:\n                </p>\n                <CodeBlock code=\"yarn add @legalyard/design-system\" />\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium mb-2\">2. Install Peer Dependencies</h3>\n                <CodeBlock code=\"npm install react react-dom @types/react @types/react-dom\" />\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium mb-2\">3. Configure Tailwind CSS</h3>\n                <p className=\"text-sm text-muted-foreground mb-2\">\n                  Add the design system paths to your tailwind.config.js:\n                </p>\n                <CodeBlock \n                  code={`module.exports = {\n  content: [\n    \"./src/**/*.{js,ts,jsx,tsx}\",\n    \"./node_modules/@legalyard/design-system/**/*.{js,ts,jsx,tsx}\"\n  ],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n}`}\n                />\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium mb-2\">4. Import Styles</h3>\n                <p className=\"text-sm text-muted-foreground mb-2\">\n                  Import the CSS in your main application file:\n                </p>\n                <CodeBlock code={`import '@legalyard/design-system/dist/index.css'`} />\n              </div>\n            </div>\n          </section>\n\n          {/* Usage Example */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Basic Usage</h2>\n            <CodeBlock \n              code={`import { Button, Input, Modal } from '@legalyard/design-system'\n\nfunction App() {\n  return (\n    <div>\n      <Button variant=\"primary\">Click me</Button>\n      <Input placeholder=\"Enter text...\" />\n    </div>\n  )\n}`}\n            />\n          </section>\n\n          {/* Next Steps */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Next Steps</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"border border-border rounded-lg p-4\">\n                <h3 className=\"font-medium mb-2\">Explore Components</h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  Browse our comprehensive component library with examples and documentation.\n                </p>\n              </div>\n              <div className=\"border border-border rounded-lg p-4\">\n                <h3 className=\"font-medium mb-2\">Theming Guide</h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  Learn how to customize colors, typography, and spacing to match your brand.\n                </p>\n              </div>\n            </div>\n          </section>\n        </div>\n      </div>\n    </PageLayout>\n  )\n}\n\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,oIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAK/C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAO;;;;;;sDACpB,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAMV,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,8OAAC,mIAAA,CAAA,YAAS;oDAAC,MAAK;;;;;;8DAChB,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC,mIAAA,CAAA,YAAS;oDAAC,MAAK;;;;;;;;;;;;sDAGlB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,8OAAC,mIAAA,CAAA,YAAS;oDAAC,MAAK;;;;;;;;;;;;sDAGlB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC,mIAAA,CAAA,YAAS;oDACR,MAAM,CAAC;;;;;;;;;CASxB,CAAC;;;;;;;;;;;;sDAIY,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,8OAAC,mIAAA,CAAA,YAAS;oDAAC,MAAM,CAAC,gDAAgD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;sCAMzE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC,mIAAA,CAAA,YAAS;oCACR,MAAM,CAAC;;;;;;;;;CASpB,CAAC;;;;;;;;;;;;sCAKQ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAI/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7D", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;AAG9B,OAAO,MAAMG,eAAe;AAG5B,EAAC;;;;;;;;;;;;;;AAEuG,EAAC,uEAAA;AAEzG,UAAA,kDAA4D;AAC5D,MAAA,CAAO,MAAMK;IAAAA;IAAAA,SAAc,IAAIX,mBAAmB;YAChDY,QAAAA;YAAAA,GAAY;YAAA;wBACVC,IAAAA;oBAAAA,CAAMZ,UAAUa;oBAAAA,OAAQ;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA,GAAU;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;;uBACV,2CAA2C;;iBAC3CC,YAAY;sBACZC,IAAAA,CAAAA;YAAAA,CAAU;SAAA;;SACVC,UAAU,EAAE;UACd,QAAA;YAAA,MAAA;gBACAC,OAAU,QAAA;wBAAA;4BACRC,KAAAA,CAAAA,GAAAA,IAAYnB,wMAAZmB,CAAAA,sBAAYnB,EAAAA,MAAAA,MAAAA,MAAAA,MAAAA,EAAAA,iBAAAA,CAAAA,CAAAA,EAAAA,6SAAAA,CAAAA,UAAAA,CAAAA,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,MAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAAA,EAAAA;4BACd,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACA,MAAA,CAAA,YAAA,CAAA", "ignoreList": [0], "debugId": null}}]}