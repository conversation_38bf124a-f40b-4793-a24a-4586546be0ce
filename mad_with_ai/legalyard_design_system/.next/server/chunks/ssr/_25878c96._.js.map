{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/code-block.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CodeBlock = registerClientReference(\n    function() { throw new Error(\"Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/code-block.tsx <module evaluation>\",\n    \"CodeBlock\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/code-block.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CodeBlock = registerClientReference(\n    function() { throw new Error(\"Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/code-block.tsx\",\n    \"CodeBlock\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/app/styles/page.tsx"], "sourcesContent": ["import { PageLayout } from \"@/components/page-layout\"\nimport { CodeBlock } from \"@/components/code-block\"\nimport Link from \"next/link\"\nimport { <PERSON>R<PERSON>, Paintbrush, Palette, Type, Spacing } from \"lucide-react\"\n\nconst colorPalette = {\n  primary: {\n    50: \"#eff6ff\",\n    100: \"#dbeafe\", \n    200: \"#bfdbfe\",\n    300: \"#93c5fd\",\n    400: \"#60a5fa\",\n    500: \"#3b82f6\",\n    600: \"#2563eb\",\n    700: \"#1d4ed8\",\n    800: \"#1e40af\",\n    900: \"#1e3a8a\"\n  },\n  gray: {\n    50: \"#f9fafb\",\n    100: \"#f3f4f6\",\n    200: \"#e5e7eb\", \n    300: \"#d1d5db\",\n    400: \"#9ca3af\",\n    500: \"#6b7280\",\n    600: \"#4b5563\",\n    700: \"#374151\",\n    800: \"#1f2937\",\n    900: \"#111827\"\n  }\n}\n\nconst typography = {\n  fontSizes: {\n    xs: \"0.75rem\",\n    sm: \"0.875rem\", \n    base: \"1rem\",\n    lg: \"1.125rem\",\n    xl: \"1.25rem\",\n    \"2xl\": \"1.5rem\",\n    \"3xl\": \"1.875rem\",\n    \"4xl\": \"2.25rem\",\n    \"5xl\": \"3rem\",\n    \"6xl\": \"3.75rem\"\n  },\n  fontWeights: {\n    thin: \"100\",\n    light: \"300\",\n    normal: \"400\",\n    medium: \"500\",\n    semibold: \"600\",\n    bold: \"700\",\n    extrabold: \"800\",\n    black: \"900\"\n  },\n  lineHeights: {\n    none: \"1\",\n    tight: \"1.25\",\n    snug: \"1.375\",\n    normal: \"1.5\",\n    relaxed: \"1.625\",\n    loose: \"2\"\n  }\n}\n\nconst spacing = {\n  0: \"0px\",\n  1: \"0.25rem\",\n  2: \"0.5rem\", \n  3: \"0.75rem\",\n  4: \"1rem\",\n  5: \"1.25rem\",\n  6: \"1.5rem\",\n  8: \"2rem\",\n  10: \"2.5rem\",\n  12: \"3rem\",\n  16: \"4rem\",\n  20: \"5rem\",\n  24: \"6rem\",\n  32: \"8rem\"\n}\n\nexport default function StylesPage() {\n  return (\n    <PageLayout>\n      <div className=\"max-w-6xl mx-auto\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold tracking-tight mb-4\">Styles & Theming</h1>\n          <p className=\"text-xl text-muted-foreground\">\n            Design tokens, color palettes, typography, and theming system for consistent visual design.\n          </p>\n        </div>\n\n        {/* Color Palette */}\n        <section className=\"mb-12\">\n          <div className=\"flex items-center space-x-3 mb-6\">\n            <Palette className=\"h-6 w-6 text-primary\" />\n            <h2 className=\"text-2xl font-semibold\">Color Palette</h2>\n          </div>\n          \n          <div className=\"space-y-8\">\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Primary Colors</h3>\n              <div className=\"grid grid-cols-5 md:grid-cols-10 gap-2\">\n                {Object.entries(colorPalette.primary).map(([shade, color]) => (\n                  <div key={shade} className=\"text-center\">\n                    <div \n                      className=\"w-full h-16 rounded-lg border border-border mb-2\"\n                      style={{ backgroundColor: color }}\n                    ></div>\n                    <div className=\"text-xs font-mono\">{shade}</div>\n                    <div className=\"text-xs text-muted-foreground\">{color}</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Gray Scale</h3>\n              <div className=\"grid grid-cols-5 md:grid-cols-10 gap-2\">\n                {Object.entries(colorPalette.gray).map(([shade, color]) => (\n                  <div key={shade} className=\"text-center\">\n                    <div \n                      className=\"w-full h-16 rounded-lg border border-border mb-2\"\n                      style={{ backgroundColor: color }}\n                    ></div>\n                    <div className=\"text-xs font-mono\">{shade}</div>\n                    <div className=\"text-xs text-muted-foreground\">{color}</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Semantic Colors</h3>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"w-full h-16 bg-green-500 rounded-lg border border-border mb-2\"></div>\n                  <div className=\"text-sm font-medium\">Success</div>\n                  <div className=\"text-xs text-muted-foreground\">#10b981</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"w-full h-16 bg-yellow-500 rounded-lg border border-border mb-2\"></div>\n                  <div className=\"text-sm font-medium\">Warning</div>\n                  <div className=\"text-xs text-muted-foreground\">#f59e0b</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"w-full h-16 bg-red-500 rounded-lg border border-border mb-2\"></div>\n                  <div className=\"text-sm font-medium\">Error</div>\n                  <div className=\"text-xs text-muted-foreground\">#ef4444</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"w-full h-16 bg-blue-500 rounded-lg border border-border mb-2\"></div>\n                  <div className=\"text-sm font-medium\">Info</div>\n                  <div className=\"text-xs text-muted-foreground\">#3b82f6</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-6\">\n            <CodeBlock \n              code={`// Using colors in CSS\n.primary-button {\n  background-color: hsl(var(--primary));\n  color: hsl(var(--primary-foreground));\n}\n\n// Using colors in Tailwind\n<button className=\"bg-primary text-primary-foreground\">\n  Primary Button\n</button>\n\n// Using colors in JavaScript\nimport { colors } from '@legalyard/design-system/tokens'\n\nconst primaryColor = colors.primary[500]`}\n            />\n          </div>\n        </section>\n\n        {/* Typography */}\n        <section className=\"mb-12\">\n          <div className=\"flex items-center space-x-3 mb-6\">\n            <Type className=\"h-6 w-6 text-primary\" />\n            <h2 className=\"text-2xl font-semibold\">Typography</h2>\n          </div>\n\n          <div className=\"space-y-8\">\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Font Sizes</h3>\n              <div className=\"space-y-4\">\n                {Object.entries(typography.fontSizes).map(([size, value]) => (\n                  <div key={size} className=\"flex items-center space-x-4\">\n                    <div className=\"w-16 text-sm font-mono text-muted-foreground\">{size}</div>\n                    <div className=\"w-20 text-xs text-muted-foreground\">{value}</div>\n                    <div style={{ fontSize: value }}>The quick brown fox jumps over the lazy dog</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Font Weights</h3>\n              <div className=\"space-y-2\">\n                {Object.entries(typography.fontWeights).map(([weight, value]) => (\n                  <div key={weight} className=\"flex items-center space-x-4\">\n                    <div className=\"w-20 text-sm font-mono text-muted-foreground\">{weight}</div>\n                    <div className=\"w-16 text-xs text-muted-foreground\">{value}</div>\n                    <div style={{ fontWeight: value }}>The quick brown fox jumps over the lazy dog</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Heading Styles</h3>\n              <div className=\"space-y-4\">\n                <h1 className=\"text-4xl font-bold\">Heading 1 - Main page title</h1>\n                <h2 className=\"text-3xl font-semibold\">Heading 2 - Section title</h2>\n                <h3 className=\"text-2xl font-semibold\">Heading 3 - Subsection title</h3>\n                <h4 className=\"text-xl font-medium\">Heading 4 - Component title</h4>\n                <h5 className=\"text-lg font-medium\">Heading 5 - Small section</h5>\n                <h6 className=\"text-base font-medium\">Heading 6 - Label</h6>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-6\">\n            <CodeBlock \n              code={`// Typography classes\n<h1 className=\"text-4xl font-bold\">Main Title</h1>\n<h2 className=\"text-2xl font-semibold\">Section Title</h2>\n<p className=\"text-base leading-relaxed\">Body text with relaxed line height</p>\n<small className=\"text-sm text-muted-foreground\">Helper text</small>\n\n// Custom typography\n.heading-xl {\n  font-size: 3rem;\n  font-weight: 700;\n  line-height: 1.2;\n  letter-spacing: -0.025em;\n}`}\n            />\n          </div>\n        </section>\n\n        {/* Spacing */}\n        <section className=\"mb-12\">\n          <div className=\"flex items-center space-x-3 mb-6\">\n            <Spacing className=\"h-6 w-6 text-primary\" />\n            <h2 className=\"text-2xl font-semibold\">Spacing</h2>\n          </div>\n\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Spacing Scale</h3>\n              <div className=\"space-y-2\">\n                {Object.entries(spacing).map(([token, value]) => (\n                  <div key={token} className=\"flex items-center space-x-4\">\n                    <div className=\"w-8 text-sm font-mono text-muted-foreground\">{token}</div>\n                    <div className=\"w-20 text-xs text-muted-foreground\">{value}</div>\n                    <div \n                      className=\"bg-primary/20 border-l-2 border-primary\"\n                      style={{ height: '1rem', width: value }}\n                    ></div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Common Spacing Patterns</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"p-4 border border-border rounded-lg\">\n                  <h4 className=\"font-medium mb-2\">Component Spacing</h4>\n                  <ul className=\"text-sm text-muted-foreground space-y-1\">\n                    <li>• Small components: 2-4 (0.5rem - 1rem)</li>\n                    <li>• Medium components: 4-6 (1rem - 1.5rem)</li>\n                    <li>• Large components: 6-8 (1.5rem - 2rem)</li>\n                  </ul>\n                </div>\n                <div className=\"p-4 border border-border rounded-lg\">\n                  <h4 className=\"font-medium mb-2\">Layout Spacing</h4>\n                  <ul className=\"text-sm text-muted-foreground space-y-1\">\n                    <li>• Section gaps: 8-12 (2rem - 3rem)</li>\n                    <li>• Page margins: 4-8 (1rem - 2rem)</li>\n                    <li>• Grid gaps: 4-6 (1rem - 1.5rem)</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-6\">\n            <CodeBlock \n              code={`// Spacing utilities\n<div className=\"p-4\">Padding: 1rem</div>\n<div className=\"m-6\">Margin: 1.5rem</div>\n<div className=\"space-y-4\">Vertical spacing between children</div>\n<div className=\"gap-6\">Grid/flex gap: 1.5rem</div>\n\n// Custom spacing\n.card {\n  padding: var(--spacing-6);\n  margin-bottom: var(--spacing-4);\n}`}\n            />\n          </div>\n        </section>\n\n        {/* Dark Mode */}\n        <section className=\"mb-12\">\n          <div className=\"flex items-center space-x-3 mb-6\">\n            <Paintbrush className=\"h-6 w-6 text-primary\" />\n            <h2 className=\"text-2xl font-semibold\">Dark Mode</h2>\n          </div>\n\n          <div className=\"space-y-6\">\n            <p className=\"text-muted-foreground\">\n              The design system includes comprehensive dark mode support with automatic theme switching.\n            </p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"p-6 border border-border rounded-lg bg-background\">\n                <h3 className=\"font-medium mb-4\">Light Theme</h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-4 h-4 bg-background border border-border rounded\"></div>\n                    <span className=\"text-sm\">Background</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-4 h-4 bg-foreground rounded\"></div>\n                    <span className=\"text-sm\">Foreground</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-4 h-4 bg-muted rounded\"></div>\n                    <span className=\"text-sm\">Muted</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-4 h-4 bg-primary rounded\"></div>\n                    <span className=\"text-sm\">Primary</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"p-6 border border-border rounded-lg bg-gray-900 text-white\">\n                <h3 className=\"font-medium mb-4\">Dark Theme</h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-4 h-4 bg-gray-900 border border-gray-700 rounded\"></div>\n                    <span className=\"text-sm\">Background</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-4 h-4 bg-white rounded\"></div>\n                    <span className=\"text-sm\">Foreground</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-4 h-4 bg-gray-800 rounded\"></div>\n                    <span className=\"text-sm\">Muted</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-4 h-4 bg-blue-500 rounded\"></div>\n                    <span className=\"text-sm\">Primary</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <CodeBlock \n              code={`// Automatic dark mode with CSS variables\n:root {\n  --background: 0 0% 100%;\n  --foreground: 222.2 84% 4.9%;\n}\n\n.dark {\n  --background: 222.2 84% 4.9%;\n  --foreground: 210 40% 98%;\n}\n\n// Using theme-aware colors\n<div className=\"bg-background text-foreground\">\n  Content that adapts to theme\n</div>\n\n// Theme toggle component\nimport { useTheme } from 'next-themes'\n\nfunction ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  \n  return (\n    <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>\n      Toggle Theme\n    </button>\n  )\n}`}\n            />\n          </div>\n        </section>\n\n        {/* Customization */}\n        <section className=\"mb-12\">\n          <h2 className=\"text-2xl font-semibold mb-6\">Customization</h2>\n          \n          <div className=\"bg-muted/50 rounded-lg p-6\">\n            <p className=\"mb-4\">Customize the design system to match your brand:</p>\n            <CodeBlock \n              code={`// tailwind.config.js\nmodule.exports = {\n  theme: {\n    extend: {\n      colors: {\n        primary: {\n          50: '#your-color-50',\n          500: '#your-primary-color',\n          900: '#your-color-900',\n        },\n        brand: {\n          primary: '#your-brand-primary',\n          secondary: '#your-brand-secondary',\n        }\n      },\n      fontFamily: {\n        sans: ['Your Font', 'system-ui', 'sans-serif'],\n      },\n      spacing: {\n        '18': '4.5rem',\n        '88': '22rem',\n      }\n    }\n  }\n}\n\n// CSS custom properties\n:root {\n  --your-primary: 220 100% 50%;\n  --your-secondary: 280 100% 70%;\n  --your-radius: 0.75rem;\n}`}\n            />\n          </div>\n        </section>\n\n        {/* Related Links */}\n        <section className=\"mt-16 p-8 bg-muted/50 rounded-lg\">\n          <h3 className=\"text-xl font-semibold mb-4\">Related</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <Link\n              href=\"/components\"\n              className=\"flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors\"\n            >\n              <span>UI Components</span>\n              <ArrowRight className=\"h-4 w-4\" />\n            </Link>\n            <Link\n              href=\"/layouts\"\n              className=\"flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors\"\n            >\n              <span>Layouts</span>\n              <ArrowRight className=\"h-4 w-4\" />\n            </Link>\n            <Link\n              href=\"/installation\"\n              className=\"flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors\"\n            >\n              <span>Installation</span>\n              <ArrowRight className=\"h-4 w-4\" />\n            </Link>\n          </div>\n        </section>\n      </div>\n    </PageLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEA,MAAM,eAAe;IACnB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,MAAM;QACJ,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAEA,MAAM,aAAa;IACjB,WAAW;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,MAAM;QACN,WAAW;QACX,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,SAAS;QACT,OAAO;IACT;AACF;AAEA,MAAM,UAAU;IACd,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEe,SAAS;IACtB,qBACE,8OAAC,oIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAM/C,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;;;;;;;sCAGzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,aAAa,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACvD,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB;4DAAM;;;;;;sEAElC,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAI,WAAU;sEAAiC;;;;;;;mDANxC;;;;;;;;;;;;;;;;8CAYhB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,aAAa,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACpD,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB;4DAAM;;;;;;sEAElC,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAI,WAAU;sEAAiC;;;;;;;mDANxC;;;;;;;;;;;;;;;;8CAYhB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEACrC,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;;;;;;;8DAEjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEACrC,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;;;;;;;8DAEjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEACrC,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;;;;;;;8DAEjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;sEACrC,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMvD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mIAAA,CAAA,YAAS;gCACR,MAAM,CAAC;;;;;;;;;;;;;;wCAcmB,CAAC;;;;;;;;;;;;;;;;;8BAMjC,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;;;;;;;sCAGzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,WAAW,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,iBACtD,8OAAC;oDAAe,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,8OAAC;4DAAI,OAAO;gEAAE,UAAU;4DAAM;sEAAG;;;;;;;mDAHzB;;;;;;;;;;;;;;;;8CAShB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,WAAW,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,MAAM,iBAC1D,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,8OAAC;4DAAI,OAAO;gEAAE,YAAY;4DAAM;sEAAG;;;;;;;mDAH3B;;;;;;;;;;;;;;;;8CAShB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mIAAA,CAAA,YAAS;gCACR,MAAM,CAAC;;;;;;;;;;;;CAYpB,CAAC;;;;;;;;;;;;;;;;;8BAMM,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;;;;;;;sCAGzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBAC1C,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,QAAQ;gEAAQ,OAAO;4DAAM;;;;;;;mDALhC;;;;;;;;;;;;;;;;8CAYhB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;;;;;;;;;;;;;8DAGR,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mIAAA,CAAA,YAAS;gCACR,MAAM,CAAC;;;;;;;;;;CAUpB,CAAC;;;;;;;;;;;;;;;;;8BAMM,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;;;;;;;sCAGzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAIrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAKhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMlC,8OAAC,mIAAA,CAAA,YAAS;oCACR,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BpB,CAAC;;;;;;;;;;;;;;;;;;8BAMM,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAE5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,8OAAC,mIAAA,CAAA,YAAS;oCACR,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BpB,CAAC;;;;;;;;;;;;;;;;;;8BAMM,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}]}