{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n\n  return (\n    <button\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\n      className={cn(\n        \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors\",\n        \"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n        \"disabled:opacity-50 disabled:pointer-events-none ring-offset-background\",\n        \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        \"h-10 px-4 py-2\"\n      )}\n    >\n      <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n      <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n      <span className=\"sr-only\">Toggle theme</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AANA;;;;;AAQO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEnC,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA,uGACA,2EACA,kFACA;;0BAGF,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BACf,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/search.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useRef, useEffect } from \"react\"\nimport { Search, X } from \"lucide-react\"\nimport Link from \"next/link\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SearchResult {\n  title: string\n  description: string\n  href: string\n  category: string\n}\n\nconst searchData: SearchResult[] = [\n  // Components\n  { title: \"Button\", description: \"Customizable button component with multiple variants\", href: \"/components/button\", category: \"Components\" },\n  { title: \"Input\", description: \"Text input with validation and styling options\", href: \"/components/input\", category: \"Components\" },\n  { title: \"Modal\", description: \"Modal dialog component\", href: \"/components/modal\", category: \"Components\" },\n  { title: \"Card\", description: \"Card container component\", href: \"/components/card\", category: \"Components\" },\n  { title: \"Table\", description: \"Data table component\", href: \"/components/table\", category: \"Components\" },\n  \n  // Utils\n  { title: \"formatCurrency\", description: \"Format numbers as currency with locale support\", href: \"/utils/formatcurrency\", category: \"Utils\" },\n  { title: \"formatDate\", description: \"Format dates with various patterns\", href: \"/utils/formatdate\", category: \"Utils\" },\n  { title: \"apiClient\", description: \"Configured HTTP client with interceptors\", href: \"/utils/apiclient\", category: \"Utils\" },\n  { title: \"copyToClipboard\", description: \"Copy text to clipboard\", href: \"/utils/copytoclipboard\", category: \"Utils\" },\n  \n  // Hooks\n  { title: \"useLocalStorage\", description: \"Sync state with localStorage\", href: \"/hooks/uselocalstorage\", category: \"Hooks\" },\n  { title: \"useDebounce\", description: \"Debounce a value with configurable delay\", href: \"/hooks/usedebounce\", category: \"Hooks\" },\n  { title: \"useClickOutside\", description: \"Detect clicks outside an element\", href: \"/hooks/useclickoutside\", category: \"Hooks\" },\n  { title: \"useFetch\", description: \"Fetch data with loading and error states\", href: \"/hooks/usefetch\", category: \"Hooks\" },\n  \n  // Validators\n  { title: \"isEmail\", description: \"Validate email address format\", href: \"/validators/isemail\", category: \"Validators\" },\n  { title: \"isPhoneNumber\", description: \"Validate phone number format\", href: \"/validators/isphonenumber\", category: \"Validators\" },\n  { title: \"isStrongPassword\", description: \"Validate password strength\", href: \"/validators/isstrongpassword\", category: \"Validators\" },\n  \n  // Helpers\n  { title: \"clamp\", description: \"Clamp a number between min and max values\", href: \"/helpers/clamp\", category: \"Helpers\" },\n  { title: \"chunk\", description: \"Split array into chunks of specified size\", href: \"/helpers/chunk\", category: \"Helpers\" },\n  { title: \"capitalize\", description: \"Capitalize first letter of string\", href: \"/helpers/capitalize\", category: \"Helpers\" },\n  \n  // Layouts\n  { title: \"AppLayout\", description: \"Main application layout with header, sidebar, and content area\", href: \"/layouts/applayout\", category: \"Layouts\" },\n  { title: \"Grid\", description: \"Responsive grid system with customizable columns\", href: \"/layouts/grid\", category: \"Layouts\" },\n  { title: \"Container\", description: \"Responsive container with max-width constraints\", href: \"/layouts/container\", category: \"Layouts\" },\n  \n  // Pages\n  { title: \"Installation\", description: \"Get started with Legalyard Design System\", href: \"/installation\", category: \"Getting Started\" },\n  { title: \"Styles & Theming\", description: \"Design tokens, color palettes, typography\", href: \"/styles\", category: \"Design\" },\n]\n\nexport function MasterSearch() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [query, setQuery] = useState(\"\")\n  const [results, setResults] = useState<SearchResult[]>([])\n  const [selectedIndex, setSelectedIndex] = useState(-1)\n  const inputRef = useRef<HTMLInputElement>(null)\n  const resultsRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    if (query.trim()) {\n      const filtered = searchData.filter(item =>\n        item.title.toLowerCase().includes(query.toLowerCase()) ||\n        item.description.toLowerCase().includes(query.toLowerCase()) ||\n        item.category.toLowerCase().includes(query.toLowerCase())\n      ).slice(0, 8)\n      setResults(filtered)\n      setSelectedIndex(-1)\n    } else {\n      setResults([])\n      setSelectedIndex(-1)\n    }\n  }, [query])\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key === \"k\" && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault()\n        setIsOpen(true)\n        setTimeout(() => inputRef.current?.focus(), 100)\n      }\n      \n      if (e.key === \"Escape\") {\n        setIsOpen(false)\n        setQuery(\"\")\n      }\n      \n      if (isOpen && results.length > 0) {\n        if (e.key === \"ArrowDown\") {\n          e.preventDefault()\n          setSelectedIndex(prev => (prev + 1) % results.length)\n        }\n        \n        if (e.key === \"ArrowUp\") {\n          e.preventDefault()\n          setSelectedIndex(prev => prev <= 0 ? results.length - 1 : prev - 1)\n        }\n        \n        if (e.key === \"Enter\" && selectedIndex >= 0) {\n          e.preventDefault()\n          window.location.href = results[selectedIndex].href\n        }\n      }\n    }\n\n    document.addEventListener(\"keydown\", handleKeyDown)\n    return () => document.removeEventListener(\"keydown\", handleKeyDown)\n  }, [isOpen, results, selectedIndex])\n\n  const handleClose = () => {\n    setIsOpen(false)\n    setQuery(\"\")\n    setSelectedIndex(-1)\n  }\n\n  if (!isOpen) {\n    return (\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm text-muted-foreground border border-border rounded-md hover:border-primary/50 transition-colors\"\n      >\n        <Search className=\"h-4 w-4\" />\n        <span>Search...</span>\n        <kbd className=\"hidden sm:inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground\">\n          <span className=\"text-xs\">⌘</span>K\n        </kbd>\n      </button>\n    )\n  }\n\n  return (\n    <div className=\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm\">\n      <div className=\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg\">\n        <div className=\"flex items-center space-x-2\">\n          <Search className=\"h-4 w-4 text-muted-foreground\" />\n          <input\n            ref={inputRef}\n            type=\"text\"\n            placeholder=\"Search documentation...\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            className=\"flex-1 bg-transparent text-sm outline-none placeholder:text-muted-foreground\"\n            autoFocus\n          />\n          <button\n            onClick={handleClose}\n            className=\"rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n          >\n            <X className=\"h-4 w-4\" />\n          </button>\n        </div>\n        \n        {results.length > 0 && (\n          <div ref={resultsRef} className=\"max-h-80 overflow-y-auto\">\n            <div className=\"space-y-1\">\n              {results.map((result, index) => (\n                <Link\n                  key={result.href}\n                  href={result.href}\n                  onClick={handleClose}\n                  className={cn(\n                    \"block rounded-md p-3 text-sm transition-colors\",\n                    index === selectedIndex\n                      ? \"bg-accent text-accent-foreground\"\n                      : \"hover:bg-accent hover:text-accent-foreground\"\n                  )}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"font-medium\">{result.title}</div>\n                    <div className=\"text-xs text-muted-foreground\">{result.category}</div>\n                  </div>\n                  <div className=\"text-xs text-muted-foreground mt-1 line-clamp-2\">\n                    {result.description}\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n        \n        {query && results.length === 0 && (\n          <div className=\"py-6 text-center text-sm text-muted-foreground\">\n            No results found for \"{query}\"\n          </div>\n        )}\n        \n        <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n          <div>\n            {results.length > 0 && (\n              <span>Use ↑↓ to navigate, Enter to select</span>\n            )}\n          </div>\n          <div>\n            <kbd className=\"rounded border bg-muted px-1.5 py-0.5\">Esc</kbd> to close\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAcA,MAAM,aAA6B;IACjC,aAAa;IACb;QAAE,OAAO;QAAU,aAAa;QAAwD,MAAM;QAAsB,UAAU;IAAa;IAC3I;QAAE,OAAO;QAAS,aAAa;QAAkD,MAAM;QAAqB,UAAU;IAAa;IACnI;QAAE,OAAO;QAAS,aAAa;QAA0B,MAAM;QAAqB,UAAU;IAAa;IAC3G;QAAE,OAAO;QAAQ,aAAa;QAA4B,MAAM;QAAoB,UAAU;IAAa;IAC3G;QAAE,OAAO;QAAS,aAAa;QAAwB,MAAM;QAAqB,UAAU;IAAa;IAEzG,QAAQ;IACR;QAAE,OAAO;QAAkB,aAAa;QAAkD,MAAM;QAAyB,UAAU;IAAQ;IAC3I;QAAE,OAAO;QAAc,aAAa;QAAsC,MAAM;QAAqB,UAAU;IAAQ;IACvH;QAAE,OAAO;QAAa,aAAa;QAA4C,MAAM;QAAoB,UAAU;IAAQ;IAC3H;QAAE,OAAO;QAAmB,aAAa;QAA0B,MAAM;QAA0B,UAAU;IAAQ;IAErH,QAAQ;IACR;QAAE,OAAO;QAAmB,aAAa;QAAgC,MAAM;QAA0B,UAAU;IAAQ;IAC3H;QAAE,OAAO;QAAe,aAAa;QAA4C,MAAM;QAAsB,UAAU;IAAQ;IAC/H;QAAE,OAAO;QAAmB,aAAa;QAAoC,MAAM;QAA0B,UAAU;IAAQ;IAC/H;QAAE,OAAO;QAAY,aAAa;QAA4C,MAAM;QAAmB,UAAU;IAAQ;IAEzH,aAAa;IACb;QAAE,OAAO;QAAW,aAAa;QAAiC,MAAM;QAAuB,UAAU;IAAa;IACtH;QAAE,OAAO;QAAiB,aAAa;QAAgC,MAAM;QAA6B,UAAU;IAAa;IACjI;QAAE,OAAO;QAAoB,aAAa;QAA8B,MAAM;QAAgC,UAAU;IAAa;IAErI,UAAU;IACV;QAAE,OAAO;QAAS,aAAa;QAA6C,MAAM;QAAkB,UAAU;IAAU;IACxH;QAAE,OAAO;QAAS,aAAa;QAA6C,MAAM;QAAkB,UAAU;IAAU;IACxH;QAAE,OAAO;QAAc,aAAa;QAAqC,MAAM;QAAuB,UAAU;IAAU;IAE1H,UAAU;IACV;QAAE,OAAO;QAAa,aAAa;QAAkE,MAAM;QAAsB,UAAU;IAAU;IACrJ;QAAE,OAAO;QAAQ,aAAa;QAAoD,MAAM;QAAiB,UAAU;IAAU;IAC7H;QAAE,OAAO;QAAa,aAAa;QAAmD,MAAM;QAAsB,UAAU;IAAU;IAEtI,QAAQ;IACR;QAAE,OAAO;QAAgB,aAAa;QAA4C,MAAM;QAAiB,UAAU;IAAkB;IACrI;QAAE,OAAO;QAAoB,aAAa;QAA6C,MAAM;QAAW,UAAU;IAAS;CAC5H;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,IAAI,IAAI;YAChB,MAAM,WAAW,WAAW,MAAM,CAAC,CAAA,OACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACnD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACzD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,KACtD,KAAK,CAAC,GAAG;YACX,WAAW;YACX,iBAAiB,CAAC;QACpB,OAAO;YACL,WAAW,EAAE;YACb,iBAAiB,CAAC;QACpB;IACF,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,GAAG;gBAC7C,EAAE,cAAc;gBAChB,UAAU;gBACV,WAAW,IAAM,SAAS,OAAO,EAAE,SAAS;YAC9C;YAEA,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,UAAU;gBACV,SAAS;YACX;YAEA,IAAI,UAAU,QAAQ,MAAM,GAAG,GAAG;gBAChC,IAAI,EAAE,GAAG,KAAK,aAAa;oBACzB,EAAE,cAAc;oBAChB,iBAAiB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,MAAM;gBACtD;gBAEA,IAAI,EAAE,GAAG,KAAK,WAAW;oBACvB,EAAE,cAAc;oBAChB,iBAAiB,CAAA,OAAQ,QAAQ,IAAI,QAAQ,MAAM,GAAG,IAAI,OAAO;gBACnE;gBAEA,IAAI,EAAE,GAAG,KAAK,WAAW,iBAAiB,GAAG;oBAC3C,EAAE,cAAc;oBAChB,OAAO,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI;gBACpD;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAQ;QAAS;KAAc;IAEnC,MAAM,cAAc;QAClB,UAAU;QACV,SAAS;QACT,iBAAiB,CAAC;IACpB;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YACC,SAAS,IAAM,UAAU;YACzB,WAAU;;8BAEV,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;8BAClB,8OAAC;8BAAK;;;;;;8BACN,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAU;;;;;;wBAAQ;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,WAAU;4BACV,SAAS;;;;;;sCAEX,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAIhB,QAAQ,MAAM,GAAG,mBAChB,8OAAC;oBAAI,KAAK;oBAAY,WAAU;8BAC9B,cAAA,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,OAAO,IAAI;gCACjB,SAAS;gCACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kDACA,UAAU,gBACN,qCACA;;kDAGN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAe,OAAO,KAAK;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;0DAAiC,OAAO,QAAQ;;;;;;;;;;;;kDAEjE,8OAAC;wCAAI,WAAU;kDACZ,OAAO,WAAW;;;;;;;+BAfhB,OAAO,IAAI;;;;;;;;;;;;;;;gBAuBzB,SAAS,QAAQ,MAAM,KAAK,mBAC3B,8OAAC;oBAAI,WAAU;;wBAAiD;wBACvC;wBAAM;;;;;;;8BAIjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCACE,QAAQ,MAAM,GAAG,mBAChB,8OAAC;0CAAK;;;;;;;;;;;sCAGV,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;gCAAS;;;;;;;;;;;;;;;;;;;;;;;;AAM5E", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/mobile-nav.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { Menu, X } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { \n  Home, \n  Download, \n  Palette, \n  Wrench, \n  Zap, \n  Shield, \n  HelpCircle,\n  Layout,\n  Paintbrush\n} from \"lucide-react\"\n\nconst navigation = [\n  {\n    name: \"Home\",\n    href: \"/\",\n    icon: Home,\n  },\n  {\n    name: \"Installation\",\n    href: \"/installation\",\n    icon: Download,\n  },\n  {\n    name: \"UI Components\",\n    href: \"/components\",\n    icon: Palette,\n  },\n  {\n    name: \"Layouts\",\n    href: \"/layouts\",\n    icon: Layout,\n  },\n  {\n    name: \"Styles\",\n    href: \"/styles\",\n    icon: Paintbrush,\n  },\n  {\n    name: \"Utils\",\n    href: \"/utils\",\n    icon: Wrench,\n  },\n  {\n    name: \"Hook<PERSON>\",\n    href: \"/hooks\",\n    icon: Zap,\n  },\n  {\n    name: \"Validators\",\n    href: \"/validators\",\n    icon: Shield,\n  },\n  {\n    name: \"Helpers\",\n    href: \"/helpers\",\n    icon: HelpCircle,\n  },\n]\n\nexport function MobileNav() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n\n  return (\n    <div className=\"md:hidden\">\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n      >\n        <Menu className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Toggle menu</span>\n      </button>\n\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm\">\n          <div className=\"fixed inset-y-0 left-0 z-50 w-full max-w-xs border-r bg-background p-6 shadow-lg\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\" onClick={() => setIsOpen(false)}>\n                <div className=\"h-8 w-8 bg-primary rounded-md flex items-center justify-center\">\n                  <span className=\"text-primary-foreground font-bold text-sm\">LDS</span>\n                </div>\n                <span className=\"font-bold text-lg\">Legalyard</span>\n              </Link>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n              >\n                <X className=\"h-4 w-4\" />\n                <span className=\"sr-only\">Close menu</span>\n              </button>\n            </div>\n\n            <nav className=\"space-y-2\">\n              {navigation.map((item) => {\n                const Icon = item.icon\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    onClick={() => setIsOpen(false)}\n                    className={cn(\n                      \"flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-colors\",\n                      pathname === item.href\n                        ? \"bg-accent text-accent-foreground\"\n                        : \"text-muted-foreground hover:bg-accent hover:text-accent-foreground\"\n                    )}\n                  >\n                    <Icon className=\"h-4 w-4\" />\n                    <span>{item.name}</span>\n                  </Link>\n                )\n              })}\n            </nav>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,8MAAA,CAAA,aAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gMAAA,CAAA,MAAG;IACX;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,aAAU;IAClB;CACD;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;;kCAEV,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;YAG3B,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;oCAA8B,SAAS,IAAM,UAAU;;sDAC9E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;;sDAEV,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAI9B,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0FACA,aAAa,KAAK,IAAI,GAClB,qCACA;;sDAGN,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCAXX,KAAK,IAAI;;;;;4BAcpB;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\nimport { ThemeToggle } from \"./theme-toggle\"\nimport { MasterSearch } from \"./search\"\nimport { MobileNav } from \"./mobile-nav\"\nimport { \n  Home, \n  Download, \n  Palette, \n  Wrench, \n  Zap, \n  Shield, \n  HelpCircle,\n  Layout,\n  Paintbrush\n} from \"lucide-react\"\n\nconst navigation = [\n  {\n    name: \"Home\",\n    href: \"/\",\n    icon: Home,\n  },\n  {\n    name: \"Installation\",\n    href: \"/installation\",\n    icon: Download,\n  },\n  {\n    name: \"UI Components\",\n    href: \"/components\",\n    icon: Palette,\n  },\n  {\n    name: \"Layouts\",\n    href: \"/layouts\",\n    icon: Layout,\n  },\n  {\n    name: \"Styles\",\n    href: \"/styles\",\n    icon: Paintbrush,\n  },\n  {\n    name: \"Utils\",\n    href: \"/utils\",\n    icon: Wrench,\n  },\n  {\n    name: \"Hooks\",\n    href: \"/hooks\",\n    icon: Zap,\n  },\n  {\n    name: \"Validators\",\n    href: \"/validators\",\n    icon: Shield,\n  },\n  {\n    name: \"Helpers\",\n    href: \"/helpers\",\n    icon: HelpCircle,\n  },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"legalyard-nav sticky top-0 z-50 flex items-center justify-between p-4\">\n      <div className=\"flex items-center space-x-8\">\n        <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n          <div className=\"h-10 w-10 legalyard-gradient rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300\">\n            <span className=\"text-white font-bold text-lg\">L</span>\n          </div>\n          <div className=\"flex flex-col\">\n            <span className=\"font-bold text-xl bg-gradient-to-r from-orange-600 to-amber-500 bg-clip-text text-transparent\">\n              Legalyard\n            </span>\n            <span className=\"text-xs text-muted-foreground -mt-1\">Design System</span>\n          </div>\n        </Link>\n        \n        <div className=\"hidden md:flex items-center space-x-6\">\n          {navigation.map((item) => {\n            const Icon = item.icon\n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  \"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-accent/50\",\n                  pathname === item.href\n                    ? \"text-primary bg-accent/30 shadow-sm\"\n                    : \"text-muted-foreground hover:text-foreground\"\n                )}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{item.name}</span>\n              </Link>\n            )\n          })}\n        </div>\n      </div>\n      \n      <div className=\"flex items-center space-x-4\">\n        <MasterSearch />\n        <ThemeToggle />\n        <MobileNav />\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAoBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,8MAAA,CAAA,aAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gMAAA,CAAA,MAAG;IACX;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,aAAU;IAClB;CACD;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgG;;;;;;kDAGhH,8OAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;kCAI1D,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uHACA,aAAa,KAAK,IAAI,GAClB,wCACA;;kDAGN,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,KAAK,IAAI;;;;;;;+BAVX,KAAK,IAAI;;;;;wBAapB;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4HAAA,CAAA,eAAY;;;;;kCACb,8OAAC,qIAAA,CAAA,cAAW;;;;;kCACZ,8OAAC,mIAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;AAIlB", "debugId": null}}]}