{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/app/components/page.tsx"], "sourcesContent": ["import { PageLayout } from \"@/components/page-layout\"\nimport Link from \"next/link\"\nimport { ArrowRight } from \"lucide-react\"\n\n// Import some example components from the toolkit\n// Note: These would be imported from the actual toolkit once it's properly set up\n\nconst componentCategories = [\n  {\n    name: \"Form Components\",\n    description: \"Input fields, buttons, checkboxes, and form controls\",\n    components: [\n      { name: \"Button\", description: \"Customizable button component with multiple variants\" },\n      { name: \"Input\", description: \"Text input with validation and styling options\" },\n      { name: \"Checkbox\", description: \"Checkbox input with custom styling\" },\n      { name: \"Radio\", description: \"Radio button input component\" },\n      { name: \"Select\", description: \"Dropdown select component\" },\n      { name: \"Textarea\", description: \"Multi-line text input component\" },\n      { name: \"Toggle\", description: \"Switch/toggle component\" },\n      { name: \"DatePicker\", description: \"Date selection component\" },\n      { name: \"TimePicker\", description: \"Time selection component\" },\n      { name: \"FileUpload\", description: \"File upload component\" },\n    ]\n  },\n  {\n    name: \"Navigation\",\n    description: \"Navigation components for user interface\",\n    components: [\n      { name: \"Breadcrumb\", description: \"Navigation breadcrumb component\" },\n      { name: \"<PERSON>u\", description: \"Navigation menu component\" },\n      { name: \"Tabs\", description: \"Tab navigation component\" },\n      { name: \"Stepper\", description: \"Step-by-step navigation component\" },\n    ]\n  },\n  {\n    name: \"Feedback\",\n    description: \"Components for user feedback and notifications\",\n    components: [\n      { name: \"Alert\", description: \"Alert and notification component\" },\n      { name: \"Modal\", description: \"Modal dialog component\" },\n      { name: \"Tooltip\", description: \"Tooltip component for additional information\" },\n      { name: \"Progress\", description: \"Progress indicator component\" },\n      { name: \"Loader\", description: \"Loading spinner and skeleton components\" },\n    ]\n  },\n  {\n    name: \"Display\",\n    description: \"Components for displaying content\",\n    components: [\n      { name: \"Card\", description: \"Card container component\" },\n      { name: \"Table\", description: \"Data table component\" },\n      { name: \"Image\", description: \"Optimized image component\" },\n      { name: \"Avatar\", description: \"User avatar component\" },\n      { name: \"Badge\", description: \"Badge and tag components\" },\n      { name: \"Divider\", description: \"Content divider component\" },\n      { name: \"Collapse\", description: \"Collapsible content component\" },\n      { name: \"Carousel\", description: \"Image and content carousel\" },\n    ]\n  },\n  {\n    name: \"Layout\",\n    description: \"Layout and container components\",\n    components: [\n      { name: \"Container\", description: \"Main container component\" },\n      { name: \"Grid\", description: \"Grid layout system\" },\n      { name: \"Flex\", description: \"Flexbox layout component\" },\n      { name: \"Sidebar\", description: \"Sidebar navigation component\" },\n      { name: \"Header\", description: \"Page header component\" },\n      { name: \"Footer\", description: \"Page footer component\" },\n    ]\n  }\n]\n\nexport default function ComponentsPage() {\n  return (\n    <PageLayout>\n      <div className=\"max-w-6xl mx-auto\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold tracking-tight mb-4\">UI Components</h1>\n          <p className=\"text-xl text-muted-foreground\">\n            A comprehensive collection of reusable UI components built with React and TypeScript.\n          </p>\n        </div>\n\n        <div className=\"space-y-12\">\n          {componentCategories.map((category) => (\n            <section key={category.name}>\n              <div className=\"mb-6\">\n                <h2 className=\"text-2xl font-semibold mb-2\">{category.name}</h2>\n                <p className=\"text-muted-foreground\">{category.description}</p>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {category.components.map((component) => (\n                  <ComponentCard\n                    key={component.name}\n                    name={component.name}\n                    description={component.description}\n                    href={`/components/${component.name.toLowerCase()}`}\n                  />\n                ))}\n              </div>\n            </section>\n          ))}\n        </div>\n\n        <div className=\"mt-16 p-8 bg-muted/50 rounded-lg\">\n          <h3 className=\"text-xl font-semibold mb-4\">Need a Custom Component?</h3>\n          <p className=\"text-muted-foreground mb-4\">\n            Can't find what you're looking for? Our design system is extensible and we're always adding new components.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <Link\n              href=\"/installation\"\n              className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4\"\n            >\n              Get Started\n            </Link>\n            <Link\n              href=\"/utils\"\n              className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4\"\n            >\n              View Utilities\n            </Link>\n          </div>\n        </div>\n      </div>\n    </PageLayout>\n  )\n}\n\ninterface ComponentCardProps {\n  name: string\n  description: string\n  href: string\n}\n\nfunction ComponentCard({ name, description, href }: ComponentCardProps) {\n  return (\n    <Link\n      href={href}\n      className=\"group block p-4 border border-border rounded-lg hover:border-primary/50 transition-colors\"\n    >\n      <div className=\"flex items-center justify-between mb-2\">\n        <h3 className=\"font-medium group-hover:text-primary transition-colors\">{name}</h3>\n        <ArrowRight className=\"h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors\" />\n      </div>\n      <p className=\"text-sm text-muted-foreground leading-relaxed\">{description}</p>\n    </Link>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,kDAAkD;AAClD,kFAAkF;AAElF,MAAM,sBAAsB;IAC1B;QACE,MAAM;QACN,aAAa;QACb,YAAY;YACV;gBAAE,MAAM;gBAAU,aAAa;YAAuD;YACtF;gBAAE,MAAM;gBAAS,aAAa;YAAiD;YAC/E;gBAAE,MAAM;gBAAY,aAAa;YAAqC;YACtE;gBAAE,MAAM;gBAAS,aAAa;YAA+B;YAC7D;gBAAE,MAAM;gBAAU,aAAa;YAA4B;YAC3D;gBAAE,MAAM;gBAAY,aAAa;YAAkC;YACnE;gBAAE,MAAM;gBAAU,aAAa;YAA0B;YACzD;gBAAE,MAAM;gBAAc,aAAa;YAA2B;YAC9D;gBAAE,MAAM;gBAAc,aAAa;YAA2B;YAC9D;gBAAE,MAAM;gBAAc,aAAa;YAAwB;SAC5D;IACH;IACA;QACE,MAAM;QACN,aAAa;QACb,YAAY;YACV;gBAAE,MAAM;gBAAc,aAAa;YAAkC;YACrE;gBAAE,MAAM;gBAAQ,aAAa;YAA4B;YACzD;gBAAE,MAAM;gBAAQ,aAAa;YAA2B;YACxD;gBAAE,MAAM;gBAAW,aAAa;YAAoC;SACrE;IACH;IACA;QACE,MAAM;QACN,aAAa;QACb,YAAY;YACV;gBAAE,MAAM;gBAAS,aAAa;YAAmC;YACjE;gBAAE,MAAM;gBAAS,aAAa;YAAyB;YACvD;gBAAE,MAAM;gBAAW,aAAa;YAA+C;YAC/E;gBAAE,MAAM;gBAAY,aAAa;YAA+B;YAChE;gBAAE,MAAM;gBAAU,aAAa;YAA0C;SAC1E;IACH;IACA;QACE,MAAM;QACN,aAAa;QACb,YAAY;YACV;gBAAE,MAAM;gBAAQ,aAAa;YAA2B;YACxD;gBAAE,MAAM;gBAAS,aAAa;YAAuB;YACrD;gBAAE,MAAM;gBAAS,aAAa;YAA4B;YAC1D;gBAAE,MAAM;gBAAU,aAAa;YAAwB;YACvD;gBAAE,MAAM;gBAAS,aAAa;YAA2B;YACzD;gBAAE,MAAM;gBAAW,aAAa;YAA4B;YAC5D;gBAAE,MAAM;gBAAY,aAAa;YAAgC;YACjE;gBAAE,MAAM;gBAAY,aAAa;YAA6B;SAC/D;IACH;IACA;QACE,MAAM;QACN,aAAa;QACb,YAAY;YACV;gBAAE,MAAM;gBAAa,aAAa;YAA2B;YAC7D;gBAAE,MAAM;gBAAQ,aAAa;YAAqB;YAClD;gBAAE,MAAM;gBAAQ,aAAa;YAA2B;YACxD;gBAAE,MAAM;gBAAW,aAAa;YAA+B;YAC/D;gBAAE,MAAM;gBAAU,aAAa;YAAwB;YACvD;gBAAE,MAAM;gBAAU,aAAa;YAAwB;SACxD;IACH;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,oIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAK/C,8OAAC;oBAAI,WAAU;8BACZ,oBAAoB,GAAG,CAAC,CAAC,yBACxB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+B,SAAS,IAAI;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAAyB,SAAS,WAAW;;;;;;;;;;;;8CAG5D,8OAAC;oCAAI,WAAU;8CACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,0BACxB,8OAAC;4CAEC,MAAM,UAAU,IAAI;4CACpB,aAAa,UAAU,WAAW;4CAClC,MAAM,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,WAAW,IAAI;2CAH9C,UAAU,IAAI;;;;;;;;;;;2BATb,SAAS,IAAI;;;;;;;;;;8BAoB/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAQA,SAAS,cAAc,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAsB;IACpE,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0D;;;;;;kCACxE,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;0BAExB,8OAAC;gBAAE,WAAU;0BAAiD;;;;;;;;;;;;AAGpE", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "file": "arrow-right.js", "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;AAG9B,OAAO,MAAMG,eAAe;AAG5B,EAAC;;;;;;;;;;;;;;AAEuG,EAAC,uEAAA;AAEzG,UAAA,kDAA4D;AAC5D,MAAA,CAAO,MAAMK;IAAAA;IAAAA,SAAc,IAAIX,mBAAmB;YAChDY,QAAAA;YAAAA,GAAY;YAAA;wBACVC,IAAAA;oBAAAA,CAAMZ,UAAUa;oBAAAA,OAAQ;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA,GAAU;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;;uBACV,2CAA2C;;iBAC3CC,YAAY;sBACZC,IAAAA,CAAAA;YAAAA,CAAU;SAAA;;SACVC,UAAU,EAAE;UACd,QAAA;YAAA,MAAA;gBACAC,OAAU,QAAA;wBAAA;4BACRC,KAAAA,CAAAA,GAAAA,IAAYnB,wMAAZmB,CAAAA,sBAAYnB,EAAAA,MAAAA,MAAAA,MAAAA,MAAAA,EAAAA,iBAAAA,CAAAA,CAAAA,EAAAA,6SAAAA,CAAAA,UAAAA,CAAAA,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,MAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAAA,EAAAA;4BACd,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACA,MAAA,CAAA,YAAA,CAAA", "ignoreList": [0], "debugId": null}}]}