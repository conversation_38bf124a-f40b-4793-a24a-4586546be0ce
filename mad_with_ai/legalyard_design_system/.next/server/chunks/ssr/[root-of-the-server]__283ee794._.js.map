{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n\n  return (\n    <button\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\n      className={cn(\n        \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors\",\n        \"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n        \"disabled:opacity-50 disabled:pointer-events-none ring-offset-background\",\n        \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        \"h-10 px-4 py-2\"\n      )}\n    >\n      <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n      <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n      <span className=\"sr-only\">Toggle theme</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AANA;;;;;AAQO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEnC,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA,uGACA,2EACA,kFACA;;0BAGF,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BACf,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/search.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useRef, useEffect } from \"react\"\nimport { Search as SearchIcon, X } from \"lucide-react\"\nimport Link from \"next/link\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SearchResult {\n  title: string\n  description: string\n  href: string\n  category: string\n}\n\nconst searchData: SearchResult[] = [\n  // Components\n  { title: \"Button\", description: \"Customizable button component with multiple variants\", href: \"/components/button\", category: \"Components\" },\n  { title: \"Input\", description: \"Text input with validation and styling options\", href: \"/components/input\", category: \"Components\" },\n  { title: \"Modal\", description: \"Modal dialog component\", href: \"/components/modal\", category: \"Components\" },\n  { title: \"Card\", description: \"Card container component\", href: \"/components/card\", category: \"Components\" },\n  { title: \"Table\", description: \"Data table component\", href: \"/components/table\", category: \"Components\" },\n  \n  // Utils\n  { title: \"formatCurrency\", description: \"Format numbers as currency with locale support\", href: \"/utils/formatcurrency\", category: \"Utils\" },\n  { title: \"formatDate\", description: \"Format dates with various patterns\", href: \"/utils/formatdate\", category: \"Utils\" },\n  { title: \"apiClient\", description: \"Configured HTTP client with interceptors\", href: \"/utils/apiclient\", category: \"Utils\" },\n  { title: \"copyToClipboard\", description: \"Copy text to clipboard\", href: \"/utils/copytoclipboard\", category: \"Utils\" },\n  \n  // Hooks\n  { title: \"useLocalStorage\", description: \"Sync state with localStorage\", href: \"/hooks/uselocalstorage\", category: \"Hooks\" },\n  { title: \"useDebounce\", description: \"Debounce a value with configurable delay\", href: \"/hooks/usedebounce\", category: \"Hooks\" },\n  { title: \"useClickOutside\", description: \"Detect clicks outside an element\", href: \"/hooks/useclickoutside\", category: \"Hooks\" },\n  { title: \"useFetch\", description: \"Fetch data with loading and error states\", href: \"/hooks/usefetch\", category: \"Hooks\" },\n  \n  // Validators\n  { title: \"isEmail\", description: \"Validate email address format\", href: \"/validators/isemail\", category: \"Validators\" },\n  { title: \"isPhoneNumber\", description: \"Validate phone number format\", href: \"/validators/isphonenumber\", category: \"Validators\" },\n  { title: \"isStrongPassword\", description: \"Validate password strength\", href: \"/validators/isstrongpassword\", category: \"Validators\" },\n  \n  // Helpers\n  { title: \"clamp\", description: \"Clamp a number between min and max values\", href: \"/helpers/clamp\", category: \"Helpers\" },\n  { title: \"chunk\", description: \"Split array into chunks of specified size\", href: \"/helpers/chunk\", category: \"Helpers\" },\n  { title: \"capitalize\", description: \"Capitalize first letter of string\", href: \"/helpers/capitalize\", category: \"Helpers\" },\n  \n  // Layouts\n  { title: \"AppLayout\", description: \"Main application layout with header, sidebar, and content area\", href: \"/layouts/applayout\", category: \"Layouts\" },\n  { title: \"Grid\", description: \"Responsive grid system with customizable columns\", href: \"/layouts/grid\", category: \"Layouts\" },\n  { title: \"Container\", description: \"Responsive container with max-width constraints\", href: \"/layouts/container\", category: \"Layouts\" },\n  \n  // Pages\n  { title: \"Installation\", description: \"Get started with Legalyard Design System\", href: \"/installation\", category: \"Getting Started\" },\n  { title: \"Styles & Theming\", description: \"Design tokens, color palettes, typography\", href: \"/styles\", category: \"Design\" },\n]\n\nexport function MasterSearch() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [query, setQuery] = useState(\"\")\n  const [results, setResults] = useState<SearchResult[]>([])\n  const [selectedIndex, setSelectedIndex] = useState(-1)\n  const inputRef = useRef<HTMLInputElement>(null)\n  const resultsRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    if (query.trim()) {\n      const filtered = searchData.filter(item =>\n        item.title.toLowerCase().includes(query.toLowerCase()) ||\n        item.description.toLowerCase().includes(query.toLowerCase()) ||\n        item.category.toLowerCase().includes(query.toLowerCase())\n      ).slice(0, 8)\n      setResults(filtered)\n      setSelectedIndex(-1)\n    } else {\n      setResults([])\n      setSelectedIndex(-1)\n    }\n  }, [query])\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key === \"k\" && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault()\n        setIsOpen(true)\n        setTimeout(() => inputRef.current?.focus(), 100)\n      }\n      \n      if (e.key === \"Escape\") {\n        setIsOpen(false)\n        setQuery(\"\")\n      }\n      \n      if (isOpen && results.length > 0) {\n        if (e.key === \"ArrowDown\") {\n          e.preventDefault()\n          setSelectedIndex(prev => (prev + 1) % results.length)\n        }\n        \n        if (e.key === \"ArrowUp\") {\n          e.preventDefault()\n          setSelectedIndex(prev => prev <= 0 ? results.length - 1 : prev - 1)\n        }\n        \n        if (e.key === \"Enter\" && selectedIndex >= 0) {\n          e.preventDefault()\n          window.location.href = results[selectedIndex].href\n        }\n      }\n    }\n\n    document.addEventListener(\"keydown\", handleKeyDown)\n    return () => document.removeEventListener(\"keydown\", handleKeyDown)\n  }, [isOpen, results, selectedIndex])\n\n  const handleClose = () => {\n    setIsOpen(false)\n    setQuery(\"\")\n    setSelectedIndex(-1)\n  }\n\n  if (!isOpen) {\n    return (\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm text-muted-foreground border border-border rounded-md hover:border-primary/50 transition-colors\"\n      >\n        <SearchIcon className=\"h-4 w-4\" />\n        <span>Search...</span>\n        <kbd className=\"hidden sm:inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground\">\n          <span className=\"text-xs\">⌘</span>K\n        </kbd>\n      </button>\n    )\n  }\n\n  return (\n    <div className=\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm\">\n      <div className=\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg\">\n        <div className=\"flex items-center space-x-2\">\n          <SearchIcon className=\"h-4 w-4 text-muted-foreground\" />\n          <input\n            ref={inputRef}\n            type=\"text\"\n            placeholder=\"Search documentation...\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            className=\"flex-1 bg-transparent text-sm outline-none placeholder:text-muted-foreground\"\n            autoFocus\n          />\n          <button\n            onClick={handleClose}\n            className=\"rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n          >\n            <X className=\"h-4 w-4\" />\n          </button>\n        </div>\n        \n        {results.length > 0 && (\n          <div ref={resultsRef} className=\"max-h-80 overflow-y-auto\">\n            <div className=\"space-y-1\">\n              {results.map((result, index) => (\n                <Link\n                  key={result.href}\n                  href={result.href}\n                  onClick={handleClose}\n                  className={cn(\n                    \"block rounded-md p-3 text-sm transition-colors\",\n                    index === selectedIndex\n                      ? \"bg-accent text-accent-foreground\"\n                      : \"hover:bg-accent hover:text-accent-foreground\"\n                  )}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"font-medium\">{result.title}</div>\n                    <div className=\"text-xs text-muted-foreground\">{result.category}</div>\n                  </div>\n                  <div className=\"text-xs text-muted-foreground mt-1 line-clamp-2\">\n                    {result.description}\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n        \n        {query && results.length === 0 && (\n          <div className=\"py-6 text-center text-sm text-muted-foreground\">\n            No results found for \"{query}\"\n          </div>\n        )}\n        \n        <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n          <div>\n            {results.length > 0 && (\n              <span>Use ↑↓ to navigate, Enter to select</span>\n            )}\n          </div>\n          <div>\n            <kbd className=\"rounded border bg-muted px-1.5 py-0.5\">Esc</kbd> to close\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAcA,MAAM,aAA6B;IACjC,aAAa;IACb;QAAE,OAAO;QAAU,aAAa;QAAwD,MAAM;QAAsB,UAAU;IAAa;IAC3I;QAAE,OAAO;QAAS,aAAa;QAAkD,MAAM;QAAqB,UAAU;IAAa;IACnI;QAAE,OAAO;QAAS,aAAa;QAA0B,MAAM;QAAqB,UAAU;IAAa;IAC3G;QAAE,OAAO;QAAQ,aAAa;QAA4B,MAAM;QAAoB,UAAU;IAAa;IAC3G;QAAE,OAAO;QAAS,aAAa;QAAwB,MAAM;QAAqB,UAAU;IAAa;IAEzG,QAAQ;IACR;QAAE,OAAO;QAAkB,aAAa;QAAkD,MAAM;QAAyB,UAAU;IAAQ;IAC3I;QAAE,OAAO;QAAc,aAAa;QAAsC,MAAM;QAAqB,UAAU;IAAQ;IACvH;QAAE,OAAO;QAAa,aAAa;QAA4C,MAAM;QAAoB,UAAU;IAAQ;IAC3H;QAAE,OAAO;QAAmB,aAAa;QAA0B,MAAM;QAA0B,UAAU;IAAQ;IAErH,QAAQ;IACR;QAAE,OAAO;QAAmB,aAAa;QAAgC,MAAM;QAA0B,UAAU;IAAQ;IAC3H;QAAE,OAAO;QAAe,aAAa;QAA4C,MAAM;QAAsB,UAAU;IAAQ;IAC/H;QAAE,OAAO;QAAmB,aAAa;QAAoC,MAAM;QAA0B,UAAU;IAAQ;IAC/H;QAAE,OAAO;QAAY,aAAa;QAA4C,MAAM;QAAmB,UAAU;IAAQ;IAEzH,aAAa;IACb;QAAE,OAAO;QAAW,aAAa;QAAiC,MAAM;QAAuB,UAAU;IAAa;IACtH;QAAE,OAAO;QAAiB,aAAa;QAAgC,MAAM;QAA6B,UAAU;IAAa;IACjI;QAAE,OAAO;QAAoB,aAAa;QAA8B,MAAM;QAAgC,UAAU;IAAa;IAErI,UAAU;IACV;QAAE,OAAO;QAAS,aAAa;QAA6C,MAAM;QAAkB,UAAU;IAAU;IACxH;QAAE,OAAO;QAAS,aAAa;QAA6C,MAAM;QAAkB,UAAU;IAAU;IACxH;QAAE,OAAO;QAAc,aAAa;QAAqC,MAAM;QAAuB,UAAU;IAAU;IAE1H,UAAU;IACV;QAAE,OAAO;QAAa,aAAa;QAAkE,MAAM;QAAsB,UAAU;IAAU;IACrJ;QAAE,OAAO;QAAQ,aAAa;QAAoD,MAAM;QAAiB,UAAU;IAAU;IAC7H;QAAE,OAAO;QAAa,aAAa;QAAmD,MAAM;QAAsB,UAAU;IAAU;IAEtI,QAAQ;IACR;QAAE,OAAO;QAAgB,aAAa;QAA4C,MAAM;QAAiB,UAAU;IAAkB;IACrI;QAAE,OAAO;QAAoB,aAAa;QAA6C,MAAM;QAAW,UAAU;IAAS;CAC5H;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,IAAI,IAAI;YAChB,MAAM,WAAW,WAAW,MAAM,CAAC,CAAA,OACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACnD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACzD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,KACtD,KAAK,CAAC,GAAG;YACX,WAAW;YACX,iBAAiB,CAAC;QACpB,OAAO;YACL,WAAW,EAAE;YACb,iBAAiB,CAAC;QACpB;IACF,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,GAAG;gBAC7C,EAAE,cAAc;gBAChB,UAAU;gBACV,WAAW,IAAM,SAAS,OAAO,EAAE,SAAS;YAC9C;YAEA,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,UAAU;gBACV,SAAS;YACX;YAEA,IAAI,UAAU,QAAQ,MAAM,GAAG,GAAG;gBAChC,IAAI,EAAE,GAAG,KAAK,aAAa;oBACzB,EAAE,cAAc;oBAChB,iBAAiB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,MAAM;gBACtD;gBAEA,IAAI,EAAE,GAAG,KAAK,WAAW;oBACvB,EAAE,cAAc;oBAChB,iBAAiB,CAAA,OAAQ,QAAQ,IAAI,QAAQ,MAAM,GAAG,IAAI,OAAO;gBACnE;gBAEA,IAAI,EAAE,GAAG,KAAK,WAAW,iBAAiB,GAAG;oBAC3C,EAAE,cAAc;oBAChB,OAAO,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI;gBACpD;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAQ;QAAS;KAAc;IAEnC,MAAM,cAAc;QAClB,UAAU;QACV,SAAS;QACT,iBAAiB,CAAC;IACpB;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YACC,SAAS,IAAM,UAAU;YACzB,WAAU;;8BAEV,8OAAC,sMAAA,CAAA,SAAU;oBAAC,WAAU;;;;;;8BACtB,8OAAC;8BAAK;;;;;;8BACN,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAU;;;;;;wBAAQ;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAU;4BAAC,WAAU;;;;;;sCACtB,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,WAAU;4BACV,SAAS;;;;;;sCAEX,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAIhB,QAAQ,MAAM,GAAG,mBAChB,8OAAC;oBAAI,KAAK;oBAAY,WAAU;8BAC9B,cAAA,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,OAAO,IAAI;gCACjB,SAAS;gCACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kDACA,UAAU,gBACN,qCACA;;kDAGN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAe,OAAO,KAAK;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;0DAAiC,OAAO,QAAQ;;;;;;;;;;;;kDAEjE,8OAAC;wCAAI,WAAU;kDACZ,OAAO,WAAW;;;;;;;+BAfhB,OAAO,IAAI;;;;;;;;;;;;;;;gBAuBzB,SAAS,QAAQ,MAAM,KAAK,mBAC3B,8OAAC;oBAAI,WAAU;;wBAAiD;wBACvC;wBAAM;;;;;;;8BAIjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCACE,QAAQ,MAAM,GAAG,mBAChB,8OAAC;0CAAK;;;;;;;;;;;sCAGV,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;gCAAS;;;;;;;;;;;;;;;;;;;;;;;;AAM5E", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/mobile-nav.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { Menu, X } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { \n  Home, \n  Download, \n  Palette, \n  Wrench, \n  Zap, \n  Shield, \n  HelpCircle,\n  Layout,\n  Paintbrush\n} from \"lucide-react\"\n\nconst navigation = [\n  {\n    name: \"Home\",\n    href: \"/\",\n    icon: Home,\n  },\n  {\n    name: \"Installation\",\n    href: \"/installation\",\n    icon: Download,\n  },\n  {\n    name: \"UI Components\",\n    href: \"/components\",\n    icon: Palette,\n  },\n  {\n    name: \"Layouts\",\n    href: \"/layouts\",\n    icon: Layout,\n  },\n  {\n    name: \"Styles\",\n    href: \"/styles\",\n    icon: Paintbrush,\n  },\n  {\n    name: \"Utils\",\n    href: \"/utils\",\n    icon: Wrench,\n  },\n  {\n    name: \"Hook<PERSON>\",\n    href: \"/hooks\",\n    icon: Zap,\n  },\n  {\n    name: \"Validators\",\n    href: \"/validators\",\n    icon: Shield,\n  },\n  {\n    name: \"Helpers\",\n    href: \"/helpers\",\n    icon: HelpCircle,\n  },\n]\n\nexport function MobileNav() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n\n  return (\n    <div className=\"md:hidden\">\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n      >\n        <Menu className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Toggle menu</span>\n      </button>\n\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm\">\n          <div className=\"fixed inset-y-0 left-0 z-50 w-full max-w-xs border-r bg-background p-6 shadow-lg\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\" onClick={() => setIsOpen(false)}>\n                <div className=\"h-8 w-8 bg-primary rounded-md flex items-center justify-center\">\n                  <span className=\"text-primary-foreground font-bold text-sm\">LDS</span>\n                </div>\n                <span className=\"font-bold text-lg\">Legalyard</span>\n              </Link>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n              >\n                <X className=\"h-4 w-4\" />\n                <span className=\"sr-only\">Close menu</span>\n              </button>\n            </div>\n\n            <nav className=\"space-y-2\">\n              {navigation.map((item) => {\n                const Icon = item.icon\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    onClick={() => setIsOpen(false)}\n                    className={cn(\n                      \"flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-colors\",\n                      pathname === item.href\n                        ? \"bg-accent text-accent-foreground\"\n                        : \"text-muted-foreground hover:bg-accent hover:text-accent-foreground\"\n                    )}\n                  >\n                    <Icon className=\"h-4 w-4\" />\n                    <span>{item.name}</span>\n                  </Link>\n                )\n              })}\n            </nav>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,8MAAA,CAAA,aAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gMAAA,CAAA,MAAG;IACX;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,aAAU;IAClB;CACD;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;;kCAEV,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;YAG3B,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;oCAA8B,SAAS,IAAM,UAAU;;sDAC9E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;;sDAEV,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAI9B,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0FACA,aAAa,KAAK,IAAI,GAClB,qCACA;;sDAGN,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCAXX,KAAK,IAAI;;;;;4BAcpB;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\nimport { ThemeToggle } from \"./theme-toggle\"\nimport { MasterSearch } from \"./search\"\nimport { MobileNav } from \"./mobile-nav\"\nimport { \n  Home, \n  Download, \n  Palette, \n  Wrench, \n  Zap, \n  Shield, \n  HelpCircle,\n  Layout,\n  Paintbrush\n} from \"lucide-react\"\n\nconst navigation = [\n  {\n    name: \"Home\",\n    href: \"/\",\n    icon: Home,\n  },\n  {\n    name: \"Installation\",\n    href: \"/installation\",\n    icon: Download,\n  },\n  {\n    name: \"UI Components\",\n    href: \"/components\",\n    icon: Palette,\n  },\n  {\n    name: \"Layouts\",\n    href: \"/layouts\",\n    icon: Layout,\n  },\n  {\n    name: \"Styles\",\n    href: \"/styles\",\n    icon: Paintbrush,\n  },\n  {\n    name: \"Utils\",\n    href: \"/utils\",\n    icon: Wrench,\n  },\n  {\n    name: \"Hooks\",\n    href: \"/hooks\",\n    icon: Zap,\n  },\n  {\n    name: \"Validators\",\n    href: \"/validators\",\n    icon: Shield,\n  },\n  {\n    name: \"Helpers\",\n    href: \"/helpers\",\n    icon: HelpCircle,\n  },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"legalyard-nav sticky top-0 z-50 flex items-center justify-between p-4\">\n      <div className=\"flex items-center space-x-8\">\n        <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n          <div className=\"h-10 w-10 legalyard-gradient rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300\">\n            <span className=\"text-white font-bold text-lg\">L</span>\n          </div>\n          <div className=\"flex flex-col\">\n            <span className=\"font-bold text-xl bg-gradient-to-r from-orange-600 to-amber-500 bg-clip-text text-transparent\">\n              Legalyard\n            </span>\n            <span className=\"text-xs text-muted-foreground -mt-1\">Design System</span>\n          </div>\n        </Link>\n        \n        <div className=\"hidden md:flex items-center space-x-6\">\n          {navigation.map((item) => {\n            const Icon = item.icon\n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  \"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-accent/50\",\n                  pathname === item.href\n                    ? \"text-primary bg-accent/30 shadow-sm\"\n                    : \"text-muted-foreground hover:text-foreground\"\n                )}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{item.name}</span>\n              </Link>\n            )\n          })}\n        </div>\n      </div>\n      \n      <div className=\"flex items-center space-x-4\">\n        <MasterSearch />\n        <ThemeToggle />\n        <MobileNav />\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAoBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,8MAAA,CAAA,aAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gMAAA,CAAA,MAAG;IACX;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,aAAU;IAClB;CACD;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgG;;;;;;kDAGhH,8OAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;kCAI1D,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uHACA,aAAa,KAAK,IAAI,GAClB,wCACA;;kDAGN,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,KAAK,IAAI;;;;;;;+BAVX,KAAK,IAAI;;;;;wBAapB;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4HAAA,CAAA,eAAY;;;;;kCACb,8OAAC,qIAAA,CAAA,cAAW;;;;;kCACZ,8OAAC,mIAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Github, Twitter, Linkedin, Mail } from \"lucide-react\"\n\nconst footerLinks = {\n  documentation: [\n    { name: \"Getting Started\", href: \"/installation\" },\n    { name: \"Components\", href: \"/components\" },\n    { name: \"Utilities\", href: \"/utils\" },\n    { name: \"<PERSON><PERSON>\", href: \"/hooks\" },\n  ],\n  resources: [\n    { name: \"Storybook\", href: \"#\" },\n    { name: \"GitHub\", href: \"#\" },\n    { name: \"Changelog\", href: \"#\" },\n    { name: \"Roadmap\", href: \"#\" },\n  ],\n  community: [\n    { name: \"Discord\", href: \"#\" },\n    { name: \"Twitter\", href: \"#\" },\n    { name: \"LinkedIn\", href: \"#\" },\n    { name: \"Support\", href: \"#\" },\n  ],\n  legal: [\n    { name: \"Privacy Policy\", href: \"#\" },\n    { name: \"Terms of Service\", href: \"#\" },\n    { name: \"License\", href: \"#\" },\n    { name: \"Security\", href: \"#\" },\n  ],\n}\n\nconst socialLinks = [\n  { name: \"GitHub\", href: \"#\", icon: Github },\n  { name: \"Twitter\", href: \"#\", icon: Twitter },\n  { name: \"LinkedIn\", href: \"#\", icon: Linkedin },\n  { name: \"Email\", href: \"mailto:<EMAIL>\", icon: Mail },\n]\n\nexport function Footer() {\n  return (\n    <footer className=\"border-t border-border bg-background\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"h-8 w-8 bg-primary rounded-md flex items-center justify-center\">\n                <span className=\"text-primary-foreground font-bold text-sm\">LDS</span>\n              </div>\n              <span className=\"font-bold text-lg\">Legalyard</span>\n            </Link>\n            <p className=\"text-sm text-muted-foreground mb-4\">\n              A comprehensive design system for building modern web applications with React, TypeScript, and Tailwind CSS.\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((link) => {\n                const Icon = link.icon\n                return (\n                  <Link\n                    key={link.name}\n                    href={link.href}\n                    className=\"text-muted-foreground hover:text-primary transition-colors\"\n                    aria-label={link.name}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n\n          {/* Documentation Links */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Documentation</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.documentation.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Resources Links */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Resources</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Community Links */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Community</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.community.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal Links */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <div className=\"text-sm text-muted-foreground mb-4 md:mb-0\">\n            © {new Date().getFullYear()} Legalyard. All rights reserved.\n          </div>\n          <div className=\"text-sm text-muted-foreground\">\n            Built with ❤️ using Next.js, TypeScript, and Tailwind CSS\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,cAAc;IAClB,eAAe;QACb;YAAE,MAAM;YAAmB,MAAM;QAAgB;QACjD;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAa,MAAM;QAAS;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IACD,WAAW;QACT;YAAE,MAAM;YAAa,MAAM;QAAI;QAC/B;YAAE,MAAM;YAAU,MAAM;QAAI;QAC5B;YAAE,MAAM;YAAa,MAAM;QAAI;QAC/B;YAAE,MAAM;YAAW,MAAM;QAAI;KAC9B;IACD,WAAW;QACT;YAAE,MAAM;YAAW,MAAM;QAAI;QAC7B;YAAE,MAAM;YAAW,MAAM;QAAI;QAC7B;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAI;KAC9B;IACD,OAAO;QACL;YAAE,MAAM;YAAkB,MAAM;QAAI;QACpC;YAAE,MAAM;YAAoB,MAAM;QAAI;QACtC;YAAE,MAAM;YAAW,MAAM;QAAI;QAC7B;YAAE,MAAM;YAAY,MAAM;QAAI;KAC/B;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAU,MAAM;QAAK,MAAM,sMAAA,CAAA,SAAM;IAAC;IAC1C;QAAE,MAAM;QAAW,MAAM;QAAK,MAAM,wMAAA,CAAA,UAAO;IAAC;IAC5C;QAAE,MAAM;QAAY,MAAM;QAAK,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAS,MAAM;QAAgC,MAAM,kMAAA,CAAA,OAAI;IAAC;CACnE;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,OAAO,KAAK,IAAI;wCACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,cAAY,KAAK,IAAI;sDAErB,cAAA,8OAAC;gDAAK,WAAU;;;;;;2CALX,KAAK,IAAI;;;;;oCAQpB;;;;;;;;;;;;sCAKJ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,aAAa,CAAC,GAAG,CAAC,CAAC,qBAC9B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAA6C;gCACvD,IAAI,OAAO,WAAW;gCAAG;;;;;;;sCAE9B,8OAAC;4BAAI,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/page-layout.tsx"], "sourcesContent": ["import { Navigation } from \"./navigation\"\nimport { Footer } from \"./footer\"\n\ninterface PageLayoutProps {\n  children: React.ReactNode\n}\n\nexport function PageLayout({ children }: PageLayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background flex flex-col\">\n      <Navigation />\n      <main className=\"flex-1 container mx-auto px-4 py-8\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 1436, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/app/error.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect } from \"react\"\nimport Link from \"next/link\"\nimport { PageLayout } from \"@/components/page-layout\"\nimport { AlertTriangle, RefreshCw, Home } from \"lucide-react\"\n\ninterface ErrorProps {\n  error: Error & { digest?: string }\n  reset: () => void\n}\n\nexport default function Error({ error, reset }: ErrorProps) {\n  useEffect(() => {\n    // Log the error to an error reporting service\n    console.error(\"Application error:\", error)\n  }, [error])\n\n  return (\n    <PageLayout>\n      <div className=\"max-w-2xl mx-auto text-center py-16\">\n        <div className=\"mb-8\">\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"p-4 bg-destructive/10 rounded-full\">\n              <AlertTriangle className=\"h-12 w-12 text-destructive\" />\n            </div>\n          </div>\n          <h1 className=\"text-3xl font-bold tracking-tight mb-4\">Something went wrong!</h1>\n          <p className=\"text-lg text-muted-foreground mb-8\">\n            We encountered an unexpected error. This has been logged and we'll look into it.\n          </p>\n        </div>\n\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n          <button\n            onClick={reset}\n            className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-11 px-8\"\n          >\n            <RefreshCw className=\"mr-2 h-4 w-4\" />\n            Try Again\n          </button>\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 px-8\"\n          >\n            <Home className=\"mr-2 h-4 w-4\" />\n            Go Home\n          </Link>\n        </div>\n\n        {process.env.NODE_ENV === \"development\" && (\n          <div className=\"text-left p-6 bg-muted/50 rounded-lg\">\n            <h3 className=\"font-semibold mb-2 text-destructive\">Error Details (Development Only)</h3>\n            <pre className=\"text-xs text-muted-foreground overflow-auto\">\n              {error.message}\n              {error.stack && (\n                <>\n                  {\"\\n\\n\"}\n                  {error.stack}\n                </>\n              )}\n            </pre>\n          </div>\n        )}\n\n        <div className=\"mt-12\">\n          <h2 className=\"text-xl font-semibold mb-4\">What you can do:</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-left\">\n            <div className=\"p-4 border border-border rounded-lg\">\n              <h3 className=\"font-medium mb-2\">Refresh the page</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                Sometimes a simple refresh can resolve temporary issues.\n              </p>\n            </div>\n            <div className=\"p-4 border border-border rounded-lg\">\n              <h3 className=\"font-medium mb-2\">Check your connection</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                Make sure you have a stable internet connection.\n              </p>\n            </div>\n            <div className=\"p-4 border border-border rounded-lg\">\n              <h3 className=\"font-medium mb-2\">Clear your cache</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                Try clearing your browser cache and cookies.\n              </p>\n            </div>\n            <div className=\"p-4 border border-border rounded-lg\">\n              <h3 className=\"font-medium mb-2\">Contact support</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                If the problem persists, please contact our support team.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-12 p-6 bg-muted/50 rounded-lg\">\n          <h3 className=\"font-semibold mb-2\">Need Help?</h3>\n          <p className=\"text-sm text-muted-foreground mb-4\">\n            If this error continues to occur, please report it to our development team.\n          </p>\n          <Link\n            href=\"mailto:<EMAIL>\"\n            className=\"inline-flex items-center text-sm text-primary hover:underline\"\n          >\n            Report Error\n          </Link>\n        </div>\n      </div>\n    </PageLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAYe,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAc;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,QAAQ,KAAK,CAAC,sBAAsB;IACtC,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,oIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG7B,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;;;;;;;8BAKpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGxC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;gBAKpC,oDAAyB,+BACxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAI,WAAU;;gCACZ,MAAM,OAAO;gCACb,MAAM,KAAK,kBACV;;wCACG;wCACA,MAAM,KAAK;;;;;;;;;;;;;;;8BAOtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAOnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAGlD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}]}