{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/code-block.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CodeBlock = registerClientReference(\n    function() { throw new Error(\"Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/code-block.tsx <module evaluation>\",\n    \"CodeBlock\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/code-block.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CodeBlock = registerClientReference(\n    function() { throw new Error(\"Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/code-block.tsx\",\n    \"CodeBlock\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/app/components/button/page.tsx"], "sourcesContent": ["import { PageLayout } from \"@/components/page-layout\"\nimport { CodeBlock } from \"@/components/code-block\"\nimport { Play, Download, Heart, ArrowRight, Plus } from \"lucide-react\"\n\nexport default function ButtonPage() {\n  return (\n    <PageLayout>\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold tracking-tight mb-4\">Button</h1>\n          <p className=\"text-xl text-muted-foreground\">\n            A versatile button component with multiple variants, sizes, and states built with the Legalyard design system.\n          </p>\n        </div>\n\n        {/* Examples Section */}\n        <section className=\"mb-12\">\n          <h2 className=\"text-2xl font-semibold mb-6\">Examples</h2>\n          \n          <div className=\"space-y-8\">\n            {/* Basic Usage */}\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Variants</h3>\n              <div className=\"legalyard-card p-6\">\n                <div className=\"flex flex-wrap gap-4\">\n                  <button className=\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-orange-600 to-amber-500 text-white hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2\">\n                    Primary\n                  </button>\n                  <button className=\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-amber-500 to-yellow-500 text-white hover:from-amber-600 hover:to-yellow-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2\">\n                    Secondary\n                  </button>\n                  <button className=\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-blue-600 to-sky-500 text-white hover:from-blue-700 hover:to-sky-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2\">\n                    Accent\n                  </button>\n                  <button className=\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-green-600 to-emerald-500 text-white hover:from-green-700 hover:to-emerald-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2\">\n                    Success\n                  </button>\n                  <button className=\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden bg-gradient-to-r from-red-600 to-rose-500 text-white hover:from-red-700 hover:to-rose-600 shadow-lg hover:shadow-xl hover:-translate-y-0.5 h-10 px-4 py-2\">\n                    Error\n                  </button>\n                  <button className=\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden border-2 border-border bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 h-10 px-4 py-2\">\n                    Outline\n                  </button>\n                  <button className=\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2\">\n                    Ghost\n                  </button>\n                </div>\n              </div>\n              <CodeBlock\n                code={`import { Button } from '@legalyard/design-system'\n\nfunction Example() {\n  return (\n    <div className=\"flex gap-4 flex-wrap\">\n      <Button variant=\"primary\">Primary</Button>\n      <Button variant=\"secondary\">Secondary</Button>\n      <Button variant=\"accent\">Accent</Button>\n      <Button variant=\"success\">Success</Button>\n      <Button variant=\"error\">Error</Button>\n      <Button variant=\"outline\">Outline</Button>\n      <Button variant=\"ghost\">Ghost</Button>\n    </div>\n  )\n}`}\n              />\n            </div>\n\n            {/* Sizes */}\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Sizes</h3>\n              <div className=\"p-6 border border-border rounded-lg bg-background\">\n                <div className=\"flex flex-wrap items-center gap-4\">\n                  <button className=\"inline-flex items-center justify-center rounded-md text-xs font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-8 px-3\">\n                    Small\n                  </button>\n                  <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2\">\n                    Medium\n                  </button>\n                  <button className=\"inline-flex items-center justify-center rounded-md text-base font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-12 px-6\">\n                    Large\n                  </button>\n                </div>\n              </div>\n              <CodeBlock \n                code={`<Button size=\"sm\">Small</Button>\n<Button size=\"md\">Medium</Button>\n<Button size=\"lg\">Large</Button>`}\n              />\n            </div>\n\n            {/* States */}\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">States</h3>\n              <div className=\"p-6 border border-border rounded-lg bg-background\">\n                <div className=\"flex flex-wrap gap-4\">\n                  <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2\">\n                    Default\n                  </button>\n                  <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 opacity-50 pointer-events-none\" disabled>\n                    Disabled\n                  </button>\n                  <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Loading\n                  </button>\n                </div>\n              </div>\n              <CodeBlock \n                code={`<Button>Default</Button>\n<Button disabled>Disabled</Button>\n<Button loading>Loading</Button>`}\n              />\n            </div>\n          </div>\n        </section>\n\n        {/* API Reference */}\n        <section className=\"mb-12\">\n          <h2 className=\"text-2xl font-semibold mb-6\">API Reference</h2>\n          \n          <div className=\"border border-border rounded-lg overflow-hidden\">\n            <table className=\"w-full\">\n              <thead className=\"bg-muted/50\">\n                <tr>\n                  <th className=\"text-left p-4 font-medium\">Prop</th>\n                  <th className=\"text-left p-4 font-medium\">Type</th>\n                  <th className=\"text-left p-4 font-medium\">Default</th>\n                  <th className=\"text-left p-4 font-medium\">Description</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr className=\"border-t border-border\">\n                  <td className=\"p-4 font-mono text-sm\">variant</td>\n                  <td className=\"p-4 text-sm\">'primary' | 'secondary' | 'destructive' | 'ghost'</td>\n                  <td className=\"p-4 text-sm\">'primary'</td>\n                  <td className=\"p-4 text-sm\">The visual style variant of the button</td>\n                </tr>\n                <tr className=\"border-t border-border\">\n                  <td className=\"p-4 font-mono text-sm\">size</td>\n                  <td className=\"p-4 text-sm\">'sm' | 'md' | 'lg'</td>\n                  <td className=\"p-4 text-sm\">'md'</td>\n                  <td className=\"p-4 text-sm\">The size of the button</td>\n                </tr>\n                <tr className=\"border-t border-border\">\n                  <td className=\"p-4 font-mono text-sm\">disabled</td>\n                  <td className=\"p-4 text-sm\">boolean</td>\n                  <td className=\"p-4 text-sm\">false</td>\n                  <td className=\"p-4 text-sm\">Whether the button is disabled</td>\n                </tr>\n                <tr className=\"border-t border-border\">\n                  <td className=\"p-4 font-mono text-sm\">loading</td>\n                  <td className=\"p-4 text-sm\">boolean</td>\n                  <td className=\"p-4 text-sm\">false</td>\n                  <td className=\"p-4 text-sm\">Whether the button is in loading state</td>\n                </tr>\n                <tr className=\"border-t border-border\">\n                  <td className=\"p-4 font-mono text-sm\">onClick</td>\n                  <td className=\"p-4 text-sm\">(event: MouseEvent) => void</td>\n                  <td className=\"p-4 text-sm\">-</td>\n                  <td className=\"p-4 text-sm\">Click event handler</td>\n                </tr>\n                <tr className=\"border-t border-border\">\n                  <td className=\"p-4 font-mono text-sm\">children</td>\n                  <td className=\"p-4 text-sm\">ReactNode</td>\n                  <td className=\"p-4 text-sm\">-</td>\n                  <td className=\"p-4 text-sm\">Button content</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </section>\n\n        {/* Usage Guidelines */}\n        <section>\n          <h2 className=\"text-2xl font-semibold mb-6\">Usage Guidelines</h2>\n          \n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium mb-3\">When to Use</h3>\n              <ul className=\"list-disc list-inside space-y-2 text-muted-foreground\">\n                <li>For primary actions that users need to take</li>\n                <li>To submit forms or trigger important actions</li>\n                <li>For navigation between different sections</li>\n                <li>To open modals or dialogs</li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-lg font-medium mb-3\">Best Practices</h3>\n              <ul className=\"list-disc list-inside space-y-2 text-muted-foreground\">\n                <li>Use clear, action-oriented labels</li>\n                <li>Limit primary buttons to one per section</li>\n                <li>Provide feedback for loading states</li>\n                <li>Ensure adequate touch targets (minimum 44px)</li>\n                <li>Use consistent sizing throughout your application</li>\n              </ul>\n            </div>\n          </div>\n        </section>\n      </div>\n    </PageLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAGe,SAAS;IACtB,qBACE,8OAAC,oIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAM/C,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAE5C,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAO,WAAU;kEAAuc;;;;;;kEAGzd,8OAAC;wDAAO,WAAU;kEAAuc;;;;;;kEAGzd,8OAAC;wDAAO,WAAU;kEAA+b;;;;;;kEAGjd,8OAAC;wDAAO,WAAU;kEAAyc;;;;;;kEAG3d,8OAAC;wDAAO,WAAU;kEAA+b;;;;;;kEAGjd,8OAAC;wDAAO,WAAU;kEAA8Z;;;;;;kEAGhb,8OAAC;wDAAO,WAAU;kEAAiW;;;;;;;;;;;;;;;;;sDAKvX,8OAAC,mIAAA,CAAA,YAAS;4CACR,MAAM,CAAC;;;;;;;;;;;;;;CActB,CAAC;;;;;;;;;;;;8CAKU,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAO,WAAU;kEAAuU;;;;;;kEAGzV,8OAAC;wDAAO,WAAU;kEAA6U;;;;;;kEAG/V,8OAAC;wDAAO,WAAU;kEAA0U;;;;;;;;;;;;;;;;;sDAKhW,8OAAC,mIAAA,CAAA,YAAS;4CACR,MAAM,CAAC;;gCAES,CAAC;;;;;;;;;;;;8CAKrB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAO,WAAU;kEAA6U;;;;;;kEAG/V,8OAAC;wDAAO,WAAU;wDAA4W,QAAQ;kEAAC;;;;;;kEAGvY,8OAAC;wDAAO,WAAU;;0EAChB,8OAAC;gEAAI,WAAU;;;;;;4DAAuE;;;;;;;;;;;;;;;;;;sDAK5F,8OAAC,mIAAA,CAAA,YAAS;4CACR,MAAM,CAAC;;gCAES,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8BAOzB,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAE5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;;;;;;;;;;;;kDAG9C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;;;;;;;0DAE9B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;;;;;;;0DAE9B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;;;;;;;0DAE9B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;;;;;;;0DAE9B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;;;;;;;0DAE9B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtC,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAE5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;AAG9B,OAAO,MAAMG,eAAe;AAG5B,EAAC;;;;;;;;;;;;;;AAEuG,EAAC,uEAAA;AAEzG,UAAA,kDAA4D;AAC5D,MAAA,CAAO,MAAMK;IAAAA;IAAAA,SAAc,IAAIX,mBAAmB;YAChDY,QAAAA;YAAAA,GAAY;YAAA;wBACVC,IAAAA;oBAAAA,CAAMZ;oBAAAA,CAAUa,QAAQ;gCACxBC,IAAAA;4BAAAA,CAAM;4BAAA;iCACNC,UAAU;sCACV,IAAA,CAAA;gCAAA,QAAA;oCAAA,IAAA,iBAA2C;oCAAA;iCAAA;;+BAC3CC,YAAY;;yBACZC,UAAU;8BACVC,IAAAA,CAAAA;oBAAAA,CAAU;iBAAA,CAAE;;aACd;kBACAC,QAAAA,CAAU,CAAA;YAAA;SAAA;;SACRC,YAAYnB;UACd,QAAA;YAAA,MAAA;gBACA,OAAA,QAAA;wBAAA", "ignoreList": [0], "debugId": null}}]}