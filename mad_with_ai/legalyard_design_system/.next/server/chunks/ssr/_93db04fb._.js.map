{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/code-block.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CodeBlock = registerClientReference(\n    function() { throw new Error(\"Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/code-block.tsx <module evaluation>\",\n    \"CodeBlock\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/components/code-block.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CodeBlock = registerClientReference(\n    function() { throw new Error(\"Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/code-block.tsx\",\n    \"CodeBlock\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/SUHAS/Legalyard/frontend/WEB/TOOLKIT/mad_with_ai/legalyard_design_system/src/app/layouts/page.tsx"], "sourcesContent": ["import { PageLayout } from \"@/components/page-layout\"\nimport { CodeBlock } from \"@/components/code-block\"\nimport Link from \"next/link\"\nimport { ArrowRight, Layout, PanelLeft, Columns, Grid } from \"lucide-react\"\n\nconst layoutCategories = [\n  {\n    name: \"Page Layouts\",\n    description: \"Complete page layout components and templates\",\n    icon: <Layout className=\"h-6 w-6\" />,\n    layouts: [\n      {\n        name: \"AppLayout\",\n        description: \"Main application layout with header, sidebar, and content area\",\n        example: \"<AppLayout header={<Header />} sidebar={<Sidebar />}>{content}</AppLayout>\"\n      },\n      {\n        name: \"DashboardLayout\",\n        description: \"Dashboard layout with navigation and widget areas\",\n        example: \"<DashboardLayout>{dashboardContent}</DashboardLayout>\"\n      },\n      {\n        name: \"AuthLayout\",\n        description: \"Authentication pages layout with centered forms\",\n        example: \"<AuthLayout>{loginForm}</AuthLayout>\"\n      },\n      {\n        name: \"LandingLayout\",\n        description: \"Marketing/landing page layout with hero sections\",\n        example: \"<LandingLayout>{landingContent}</LandingLayout>\"\n      },\n      {\n        name: \"DocumentLayout\",\n        description: \"Document-style layout with table of contents\",\n        example: \"<DocumentLayout toc={toc}>{documentContent}</DocumentLayout>\"\n      }\n    ]\n  },\n  {\n    name: \"Navigation Components\",\n    description: \"Header, sidebar, and navigation components\",\n    icon: <Columns className=\"h-6 w-6\" />,\n    layouts: [\n      {\n        name: \"Header\",\n        description: \"Application header with logo, navigation, and user menu\",\n        example: \"<Header logo={logo} navigation={nav} userMenu={menu} />\"\n      },\n      {\n        name: \"Sidebar\",\n        description: \"Collapsible sidebar navigation component\",\n        example: \"<Sidebar items={navItems} collapsed={isCollapsed} />\"\n      },\n      {\n        name: \"Breadcrumb\",\n        description: \"Breadcrumb navigation component\",\n        example: \"<Breadcrumb items={breadcrumbItems} />\"\n      },\n      {\n        name: \"TabNavigation\",\n        description: \"Tab-based navigation component\",\n        example: \"<TabNavigation tabs={tabs} activeTab={activeTab} />\"\n      },\n      {\n        name: \"Footer\",\n        description: \"Application footer with links and information\",\n        example: \"<Footer links={footerLinks} copyright={copyrightText} />\"\n      }\n    ]\n  },\n  {\n    name: \"Grid Systems\",\n    description: \"Flexible grid and layout systems\",\n    icon: <Grid className=\"h-6 w-6\" />,\n    layouts: [\n      {\n        name: \"Grid\",\n        description: \"Responsive grid system with customizable columns\",\n        example: \"<Grid cols={12} gap={4}>{gridItems}</Grid>\"\n      },\n      {\n        name: \"Container\",\n        description: \"Responsive container with max-width constraints\",\n        example: \"<Container size='lg'>{content}</Container>\"\n      },\n      {\n        name: \"Stack\",\n        description: \"Vertical or horizontal stack layout\",\n        example: \"<Stack direction='vertical' spacing={4}>{items}</Stack>\"\n      },\n      {\n        name: \"Flex\",\n        description: \"Flexbox layout component with common patterns\",\n        example: \"<Flex justify='between' align='center'>{flexItems}</Flex>\"\n      },\n      {\n        name: \"Masonry\",\n        description: \"Pinterest-style masonry layout\",\n        example: \"<Masonry columns={3} gap={16}>{masonryItems}</Masonry>\"\n      }\n    ]\n  },\n  {\n    name: \"Content Layouts\",\n    description: \"Specialized layouts for different content types\",\n    icon: <PanelLeft className=\"h-6 w-6\" />,\n    layouts: [\n      {\n        name: \"ArticleLayout\",\n        description: \"Blog post or article layout with metadata\",\n        example: \"<ArticleLayout meta={articleMeta}>{articleContent}</ArticleLayout>\"\n      },\n      {\n        name: \"CardLayout\",\n        description: \"Card-based layout for displaying collections\",\n        example: \"<CardLayout cards={cardData} columns={3} />\"\n      },\n      {\n        name: \"ListLayout\",\n        description: \"List layout with filtering and sorting\",\n        example: \"<ListLayout items={listItems} filters={filters} />\"\n      },\n      {\n        name: \"SplitLayout\",\n        description: \"Split-screen layout with resizable panels\",\n        example: \"<SplitLayout left={leftPanel} right={rightPanel} />\"\n      },\n      {\n        name: \"ModalLayout\",\n        description: \"Modal and dialog layout component\",\n        example: \"<ModalLayout isOpen={isOpen} onClose={handleClose}>{modalContent}</ModalLayout>\"\n      }\n    ]\n  }\n]\n\nexport default function LayoutsPage() {\n  return (\n    <PageLayout>\n      <div className=\"max-w-6xl mx-auto\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold tracking-tight mb-4\">Layouts</h1>\n          <p className=\"text-xl text-muted-foreground\">\n            Flexible layout components and systems for building consistent user interfaces.\n          </p>\n        </div>\n\n        {/* Quick Start */}\n        <section className=\"mb-12\">\n          <h2 className=\"text-2xl font-semibold mb-4\">Quick Start</h2>\n          <div className=\"bg-muted/50 rounded-lg p-6\">\n            <p className=\"mb-4\">Import layout components from the design system:</p>\n            <CodeBlock \n              code={`import { \n  AppLayout, \n  Header, \n  Sidebar, \n  Container,\n  Grid,\n  Stack \n} from '@legalyard/design-system/layouts'\n\nfunction App() {\n  return (\n    <AppLayout\n      header={\n        <Header \n          logo={<Logo />}\n          navigation={<Navigation />}\n          userMenu={<UserMenu />}\n        />\n      }\n      sidebar={\n        <Sidebar \n          items={navigationItems}\n          collapsed={sidebarCollapsed}\n        />\n      }\n    >\n      <Container size=\"xl\">\n        <Grid cols={12} gap={6}>\n          <div className=\"col-span-8\">\n            <Stack spacing={6}>\n              {mainContent}\n            </Stack>\n          </div>\n          <div className=\"col-span-4\">\n            {sidebarContent}\n          </div>\n        </Grid>\n      </Container>\n    </AppLayout>\n  )\n}`}\n            />\n          </div>\n        </section>\n\n        {/* Layout Categories */}\n        <div className=\"space-y-12\">\n          {layoutCategories.map((category) => (\n            <section key={category.name}>\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className=\"text-primary\">{category.icon}</div>\n                <div>\n                  <h2 className=\"text-2xl font-semibold\">{category.name}</h2>\n                  <p className=\"text-muted-foreground\">{category.description}</p>\n                </div>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {category.layouts.map((layout) => (\n                  <LayoutCard\n                    key={layout.name}\n                    name={layout.name}\n                    description={layout.description}\n                    example={layout.example}\n                    href={`/layouts/${layout.name.toLowerCase()}`}\n                  />\n                ))}\n              </div>\n            </section>\n          ))}\n        </div>\n\n        {/* Layout Examples */}\n        <section className=\"mt-16\">\n          <h2 className=\"text-2xl font-semibold mb-6\">Layout Examples</h2>\n          \n          <div className=\"space-y-8\">\n            {/* Dashboard Layout Example */}\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Dashboard Layout</h3>\n              <div className=\"border border-border rounded-lg p-4 bg-muted/20\">\n                <div className=\"h-12 bg-primary/20 rounded mb-4 flex items-center px-4\">\n                  <div className=\"w-24 h-6 bg-primary/40 rounded\"></div>\n                  <div className=\"ml-auto flex space-x-2\">\n                    <div className=\"w-8 h-8 bg-primary/40 rounded\"></div>\n                    <div className=\"w-8 h-8 bg-primary/40 rounded\"></div>\n                  </div>\n                </div>\n                <div className=\"flex gap-4\">\n                  <div className=\"w-48 bg-muted/50 rounded p-4\">\n                    <div className=\"space-y-2\">\n                      <div className=\"h-4 bg-muted rounded\"></div>\n                      <div className=\"h-4 bg-muted rounded\"></div>\n                      <div className=\"h-4 bg-muted rounded\"></div>\n                    </div>\n                  </div>\n                  <div className=\"flex-1 bg-background rounded p-4 border border-border\">\n                    <div className=\"grid grid-cols-3 gap-4 mb-4\">\n                      <div className=\"h-20 bg-muted/50 rounded\"></div>\n                      <div className=\"h-20 bg-muted/50 rounded\"></div>\n                      <div className=\"h-20 bg-muted/50 rounded\"></div>\n                    </div>\n                    <div className=\"h-32 bg-muted/50 rounded\"></div>\n                  </div>\n                </div>\n              </div>\n              <CodeBlock \n                code={`<DashboardLayout>\n  <DashboardHeader>\n    <Logo />\n    <UserMenu />\n  </DashboardHeader>\n  \n  <DashboardSidebar>\n    <Navigation items={navItems} />\n  </DashboardSidebar>\n  \n  <DashboardContent>\n    <Grid cols={3} gap={6}>\n      <MetricCard title=\"Users\" value=\"1,234\" />\n      <MetricCard title=\"Revenue\" value=\"$12,345\" />\n      <MetricCard title=\"Orders\" value=\"567\" />\n    </Grid>\n    \n    <Chart data={chartData} />\n  </DashboardContent>\n</DashboardLayout>`}\n              />\n            </div>\n\n            {/* Article Layout Example */}\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Article Layout</h3>\n              <div className=\"border border-border rounded-lg p-4 bg-muted/20\">\n                <div className=\"max-w-2xl mx-auto\">\n                  <div className=\"h-8 bg-primary/40 rounded mb-2\"></div>\n                  <div className=\"h-4 bg-muted rounded mb-4 w-1/3\"></div>\n                  <div className=\"space-y-2 mb-6\">\n                    <div className=\"h-3 bg-muted rounded\"></div>\n                    <div className=\"h-3 bg-muted rounded\"></div>\n                    <div className=\"h-3 bg-muted rounded w-3/4\"></div>\n                  </div>\n                  <div className=\"h-32 bg-muted/50 rounded mb-6\"></div>\n                  <div className=\"space-y-2\">\n                    <div className=\"h-3 bg-muted rounded\"></div>\n                    <div className=\"h-3 bg-muted rounded\"></div>\n                    <div className=\"h-3 bg-muted rounded w-5/6\"></div>\n                  </div>\n                </div>\n              </div>\n              <CodeBlock \n                code={`<ArticleLayout\n  meta={{\n    title: \"Getting Started with Design Systems\",\n    author: \"John Doe\",\n    publishedAt: \"2023-12-01\",\n    readTime: \"5 min read\"\n  }}\n  tableOfContents={toc}\n>\n  <ArticleHeader />\n  \n  <ArticleContent>\n    <p>Your article content goes here...</p>\n    <Image src=\"/article-image.jpg\" alt=\"Article illustration\" />\n    <p>More content...</p>\n  </ArticleContent>\n  \n  <ArticleFooter>\n    <ShareButtons />\n    <RelatedArticles />\n  </ArticleFooter>\n</ArticleLayout>`}\n              />\n            </div>\n          </div>\n        </section>\n\n        {/* Responsive Design */}\n        <section className=\"mt-16\">\n          <h2 className=\"text-2xl font-semibold mb-6\">Responsive Design</h2>\n          <div className=\"bg-muted/50 rounded-lg p-6\">\n            <p className=\"mb-4\">All layout components are built with responsive design in mind:</p>\n            <CodeBlock \n              code={`// Responsive grid\n<Grid \n  cols={{ xs: 1, sm: 2, md: 3, lg: 4 }}\n  gap={{ xs: 4, md: 6 }}\n>\n  {items}\n</Grid>\n\n// Responsive container\n<Container \n  size={{ xs: 'full', sm: 'md', lg: 'xl' }}\n  padding={{ xs: 4, md: 8 }}\n>\n  {content}\n</Container>\n\n// Responsive stack\n<Stack \n  direction={{ xs: 'vertical', md: 'horizontal' }}\n  spacing={{ xs: 4, md: 8 }}\n>\n  {stackItems}\n</Stack>`}\n            />\n          </div>\n        </section>\n\n        {/* Best Practices */}\n        <section className=\"mt-16\">\n          <h2 className=\"text-2xl font-semibold mb-6\">Best Practices</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"p-6 border border-border rounded-lg\">\n              <h3 className=\"font-semibold mb-3 text-green-600\">✅ Do</h3>\n              <ul className=\"space-y-2 text-sm text-muted-foreground\">\n                <li>Use semantic layout components</li>\n                <li>Plan for mobile-first responsive design</li>\n                <li>Maintain consistent spacing and alignment</li>\n                <li>Use appropriate layout for content type</li>\n                <li>Test layouts across different screen sizes</li>\n                <li>Consider accessibility in layout design</li>\n              </ul>\n            </div>\n            <div className=\"p-6 border border-border rounded-lg\">\n              <h3 className=\"font-semibold mb-3 text-red-600\">❌ Don't</h3>\n              <ul className=\"space-y-2 text-sm text-muted-foreground\">\n                <li>Nest too many layout components</li>\n                <li>Use fixed dimensions without responsive alternatives</li>\n                <li>Ignore content overflow scenarios</li>\n                <li>Create layouts that break on small screens</li>\n                <li>Use layout components for styling purposes only</li>\n                <li>Forget to test with real content</li>\n              </ul>\n            </div>\n          </div>\n        </section>\n\n        {/* Related Links */}\n        <section className=\"mt-16 p-8 bg-muted/50 rounded-lg\">\n          <h3 className=\"text-xl font-semibold mb-4\">Related</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <Link\n              href=\"/components\"\n              className=\"flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors\"\n            >\n              <span>UI Components</span>\n              <ArrowRight className=\"h-4 w-4\" />\n            </Link>\n            <Link\n              href=\"/styles\"\n              className=\"flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors\"\n            >\n              <span>Styles & Theming</span>\n              <ArrowRight className=\"h-4 w-4\" />\n            </Link>\n            <Link\n              href=\"/utils\"\n              className=\"flex items-center space-x-2 p-4 border border-border rounded-lg hover:border-primary/50 transition-colors\"\n            >\n              <span>Utilities</span>\n              <ArrowRight className=\"h-4 w-4\" />\n            </Link>\n          </div>\n        </section>\n      </div>\n    </PageLayout>\n  )\n}\n\ninterface LayoutCardProps {\n  name: string\n  description: string\n  example: string\n  href: string\n}\n\nfunction LayoutCard({ name, description, example, href }: LayoutCardProps) {\n  return (\n    <Link\n      href={href}\n      className=\"group block p-4 border border-border rounded-lg hover:border-primary/50 transition-colors\"\n    >\n      <div className=\"flex items-center justify-between mb-2\">\n        <h3 className=\"font-mono text-sm font-medium group-hover:text-primary transition-colors\">{name}</h3>\n        <ArrowRight className=\"h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors\" />\n      </div>\n      <p className=\"text-sm text-muted-foreground mb-3 leading-relaxed\">{description}</p>\n      <code className=\"text-xs bg-muted px-2 py-1 rounded text-muted-foreground block overflow-x-auto\">{example}</code>\n    </Link>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEA,MAAM,mBAAmB;IACvB;QACE,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,qNAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,SAAS;YACP;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;SACD;IACH;IACA;QACE,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,6MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,SAAS;YACP;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;SACD;IACH;IACA;QACE,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,yMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,SAAS;YACP;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;SACD;IACH;IACA;QACE,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,SAAS;YACP;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;SACD;IACH;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,oIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAM/C,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAC5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,8OAAC,mIAAA,CAAA,YAAS;oCACR,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwCpB,CAAC;;;;;;;;;;;;;;;;;;8BAMM,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB,SAAS,IAAI;;;;;;sDAC5C,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0B,SAAS,IAAI;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAAyB,SAAS,WAAW;;;;;;;;;;;;;;;;;;8CAI9D,8OAAC;oCAAI,WAAU;8CACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,uBACrB,8OAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,aAAa,OAAO,WAAW;4CAC/B,SAAS,OAAO,OAAO;4CACvB,MAAM,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,WAAW,IAAI;2CAJxC,OAAO,IAAI;;;;;;;;;;;2BAZV,SAAS,IAAI;;;;;;;;;;8BAyB/B,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAE5C,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAGnB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;sEAGnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;;;;;;;;;;;;8EAEjB,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAIrB,8OAAC,mIAAA,CAAA,YAAS;4CACR,MAAM,CAAC;;;;;;;;;;;;;;;;;;;kBAmBL,CAAC;;;;;;;;;;;;8CAKP,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAIrB,8OAAC,mIAAA,CAAA,YAAS;4CACR,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;gBAqBP,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8BAOT,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAC5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,8OAAC,mIAAA,CAAA,YAAS;oCACR,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;QAsBb,CAAC;;;;;;;;;;;;;;;;;;8BAMD,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAC5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAChD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOZ,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;AASA,SAAS,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAmB;IACvE,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4E;;;;;;kCAC1F,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;0BAExB,8OAAC;gBAAE,WAAU;0BAAsD;;;;;;0BACnE,8OAAC;gBAAK,WAAU;0BAAkF;;;;;;;;;;;;AAGxG", "debugId": null}}]}