import { c as createExpect, a as globalExpect, i as inject, v as vi, b as vitest } from './vi.ClIskdbk.js';
import { i as isFirstRun, a as runOnce } from './run-once.Dimr7O9f.js';
import { b as bench } from './benchmark.BoF7jW0Q.js';
import { expectTypeOf } from 'expect-type';
import { afterAll, afterEach, beforeAll, beforeEach, describe, it, onTestFailed, onTestFinished, suite, test } from '@vitest/runner';
import * as chai from 'chai';
import { assert, should } from 'chai';

function getRunningMode() {
	return process.env.VITEST_MODE === "WATCH" ? "watch" : "run";
}
function isWatchMode() {
	return getRunningMode() === "watch";
}

const assertType = function assertType() {};

var VitestIndex = /*#__PURE__*/Object.freeze({
  __proto__: null,
  afterAll: afterAll,
  afterEach: afterEach,
  assert: assert,
  assertType: assertType,
  beforeAll: beforeAll,
  beforeEach: beforeEach,
  bench: bench,
  chai: chai,
  createExpect: createExpect,
  describe: describe,
  expect: globalExpect,
  expectTypeOf: expectTypeOf,
  getRunningMode: getRunningMode,
  inject: inject,
  isFirstRun: isFirstRun,
  isWatchMode: isWatchMode,
  it: it,
  onTestFailed: onTestFailed,
  onTestFinished: onTestFinished,
  runOnce: runOnce,
  should: should,
  suite: suite,
  test: test,
  vi: vi,
  vitest: vitest
});

export { VitestIndex as V, assertType as a, getRunningMode as g, isWatchMode as i };
