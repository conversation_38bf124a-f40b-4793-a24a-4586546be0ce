export { l as loadDiffConfig, b as loadSnapshotSerializers, c as setupCommonEnv, s as startCoverageInsideWorker, a as stopCoverageInsideWorker, t as takeCoverageInsideWorker } from './chunks/setup-common.AQcDs321.js';
export { collectTests, processError, startTests } from '@vitest/runner';
import * as spy from '@vitest/spy';
export { spy as SpyModule };
import './chunks/coverage.0iPg4Wrz.js';
import '@vitest/snapshot';
import '@vitest/utils';
import './chunks/run-once.Dimr7O9f.js';
import './chunks/utils.CgTj3MsC.js';
