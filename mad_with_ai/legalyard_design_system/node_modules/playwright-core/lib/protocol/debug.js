"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var debug_exports = {};
__export(debug_exports, {
  commandsWithTracingSnapshots: () => commandsWithTracingSnapshots,
  pausesBeforeInputActions: () => pausesBeforeInputActions,
  slowMoActions: () => slowMoActions
});
module.exports = __toCommonJS(debug_exports);
const slowMoActions = /* @__PURE__ */ new Set([
  "Page.goBack",
  "Page.goForward",
  "Page.reload",
  "Page.keyboardDown",
  "Page.keyboardUp",
  "Page.keyboardInsertText",
  "Page.keyboardType",
  "Page.keyboardPress",
  "Page.mouseMove",
  "Page.mouseDown",
  "Page.mouseUp",
  "Page.mouseClick",
  "Page.mouseWheel",
  "Page.touchscreenTap",
  "Frame.blur",
  "Frame.check",
  "Frame.click",
  "Frame.dragAndDrop",
  "Frame.dblclick",
  "Frame.dispatchEvent",
  "Frame.fill",
  "Frame.focus",
  "Frame.goto",
  "Frame.hover",
  "Frame.press",
  "Frame.selectOption",
  "Frame.setInputFiles",
  "Frame.tap",
  "Frame.type",
  "Frame.uncheck",
  "ElementHandle.check",
  "ElementHandle.click",
  "ElementHandle.dblclick",
  "ElementHandle.dispatchEvent",
  "ElementHandle.fill",
  "ElementHandle.focus",
  "ElementHandle.hover",
  "ElementHandle.press",
  "ElementHandle.scrollIntoViewIfNeeded",
  "ElementHandle.selectOption",
  "ElementHandle.selectText",
  "ElementHandle.setInputFiles",
  "ElementHandle.tap",
  "ElementHandle.type",
  "ElementHandle.uncheck"
]);
const commandsWithTracingSnapshots = /* @__PURE__ */ new Set([
  "EventTarget.waitForEventInfo",
  "BrowserContext.waitForEventInfo",
  "Page.waitForEventInfo",
  "WebSocket.waitForEventInfo",
  "ElectronApplication.waitForEventInfo",
  "AndroidDevice.waitForEventInfo",
  "Page.emulateMedia",
  "Page.goBack",
  "Page.goForward",
  "Page.reload",
  "Page.expectScreenshot",
  "Page.screenshot",
  "Page.setViewportSize",
  "Page.keyboardDown",
  "Page.keyboardUp",
  "Page.keyboardInsertText",
  "Page.keyboardType",
  "Page.keyboardPress",
  "Page.mouseMove",
  "Page.mouseDown",
  "Page.mouseUp",
  "Page.mouseClick",
  "Page.mouseWheel",
  "Page.touchscreenTap",
  "Page.accessibilitySnapshot",
  "Frame.evalOnSelector",
  "Frame.evalOnSelectorAll",
  "Frame.addScriptTag",
  "Frame.addStyleTag",
  "Frame.ariaSnapshot",
  "Frame.blur",
  "Frame.check",
  "Frame.click",
  "Frame.content",
  "Frame.dragAndDrop",
  "Frame.dblclick",
  "Frame.dispatchEvent",
  "Frame.evaluateExpression",
  "Frame.evaluateExpressionHandle",
  "Frame.fill",
  "Frame.focus",
  "Frame.getAttribute",
  "Frame.goto",
  "Frame.hover",
  "Frame.innerHTML",
  "Frame.innerText",
  "Frame.inputValue",
  "Frame.isChecked",
  "Frame.isDisabled",
  "Frame.isEnabled",
  "Frame.isHidden",
  "Frame.isVisible",
  "Frame.isEditable",
  "Frame.press",
  "Frame.querySelector",
  "Frame.querySelectorAll",
  "Frame.queryCount",
  "Frame.selectOption",
  "Frame.setContent",
  "Frame.setInputFiles",
  "Frame.tap",
  "Frame.textContent",
  "Frame.type",
  "Frame.uncheck",
  "Frame.waitForTimeout",
  "Frame.waitForFunction",
  "Frame.waitForSelector",
  "Frame.expect",
  "JSHandle.evaluateExpression",
  "ElementHandle.evaluateExpression",
  "JSHandle.evaluateExpressionHandle",
  "ElementHandle.evaluateExpressionHandle",
  "ElementHandle.evalOnSelector",
  "ElementHandle.evalOnSelectorAll",
  "ElementHandle.boundingBox",
  "ElementHandle.check",
  "ElementHandle.click",
  "ElementHandle.contentFrame",
  "ElementHandle.dblclick",
  "ElementHandle.dispatchEvent",
  "ElementHandle.fill",
  "ElementHandle.focus",
  "ElementHandle.hover",
  "ElementHandle.innerHTML",
  "ElementHandle.innerText",
  "ElementHandle.inputValue",
  "ElementHandle.isChecked",
  "ElementHandle.isDisabled",
  "ElementHandle.isEditable",
  "ElementHandle.isEnabled",
  "ElementHandle.isHidden",
  "ElementHandle.isVisible",
  "ElementHandle.press",
  "ElementHandle.querySelector",
  "ElementHandle.querySelectorAll",
  "ElementHandle.screenshot",
  "ElementHandle.scrollIntoViewIfNeeded",
  "ElementHandle.selectOption",
  "ElementHandle.selectText",
  "ElementHandle.setInputFiles",
  "ElementHandle.tap",
  "ElementHandle.textContent",
  "ElementHandle.type",
  "ElementHandle.uncheck",
  "ElementHandle.waitForElementState",
  "ElementHandle.waitForSelector"
]);
const pausesBeforeInputActions = /* @__PURE__ */ new Set([
  "Frame.check",
  "Frame.click",
  "Frame.dragAndDrop",
  "Frame.dblclick",
  "Frame.fill",
  "Frame.hover",
  "Frame.press",
  "Frame.selectOption",
  "Frame.setInputFiles",
  "Frame.tap",
  "Frame.type",
  "Frame.uncheck",
  "ElementHandle.check",
  "ElementHandle.click",
  "ElementHandle.dblclick",
  "ElementHandle.fill",
  "ElementHandle.hover",
  "ElementHandle.press",
  "ElementHandle.selectOption",
  "ElementHandle.setInputFiles",
  "ElementHandle.tap",
  "ElementHandle.type",
  "ElementHandle.uncheck"
]);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  commandsWithTracingSnapshots,
  pausesBeforeInputActions,
  slowMoActions
});
