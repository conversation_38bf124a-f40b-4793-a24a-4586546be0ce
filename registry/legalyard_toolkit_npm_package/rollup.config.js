import resolve from "@rollup/plugin-node-resolve";
import commonjs from "@rollup/plugin-commonjs";
import typescript from "@rollup/plugin-typescript";
import { terser } from "rollup-plugin-terser";
import postcss from "rollup-plugin-postcss";
import url from "@rollup/plugin-url";
import babel from "@rollup/plugin-babel";
import json from "@rollup/plugin-json";

export default {
  input: "src/index.ts",
  output: [
    {
      dir: "dist",
      format: "cjs",
      sourcemap: true,
      preserveModules: true,
      preserveModulesRoot: "src",
    },
    {
      dir: "dist",
      format: "esm",
      sourcemap: true,
      preserveModules: true,
      preserveModulesRoot: "src",
    },
  ],
  plugins: [
    resolve({
      extensions: [".js", ".ts", ".jsx", ".tsx"],
      modulesOnly: true,
    }),
    commonjs(),
    typescript({
      tsconfig: "./tsconfig.json",
      module: "esnext",
      declaration: true,
      declarationDir: "dist/types",
      emitDeclarationOnly: true,
      jsx: "react-jsx",
    }),
    postcss({
      inject: true,
      minimize: true,
    }),
    terser(),
    babel({
      babelHelpers: "bundled",
      extensions: [".js", ".jsx", ".ts", ".tsx"],
      presets: ["@babel/preset-react"],
      exclude: "node_modules/**",
    }),
    json(),
    url({
      include: [
        "**/*.png",
        "**/*.jpg",
        "**/*.jpeg",
        "**/*.gif",
        "**/*.svg",
        "**/*.pdf",
        "**/*.docx",
      ],
      limit: 0,
      emitFiles: false,
    }),
  ],
  external: [
    "react",
    "react-dom",
    "styled-components",
    "tailwind.config.ts",
    /node_modules/,
    /assets/,
  ],
};
