{"name": "@lds/toolkit", "version": "1.0.0", "description": "Legalyard design system", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*", "types/**/*", "tailwind.config.ts", "declarations.d.ts"], "sideEffects": false, "readme": "none", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./styles": {"import": "./dist/styles/index.js", "require": "./dist/styles/index.js", "types": "./types/styles/index.d.ts"}, "./ui": {"import": "./dist/ui/index.js", "require": "./dist/ui/index.js", "types": "./types/ui/index.d.ts"}, "./utils": {"import": "./dist/utils/index.js", "require": "./dist/utils/index.js", "types": "./types/utils/index.d.ts"}, "./tailwind.config": "./dist/tailwind.config.js", "./index.css": "./dist/index.css"}, "publishConfig": {"@lds:registry": "https://gitlab.com/api/v4/projects/62788617/packages/npm/", "ignore": ["README.md"]}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf dist", "build": "NODE_ENV=production tsc && npm run copy-assets && npm run copy-declarations", "rollup-build": "NODE_ENV=production rollup -c --allow-overwrite && npm run copy-assets && npm run copy-declarations && npm run copy-tailwind-config && npm run minify", "copy-assets": "cp -r src/assets dist/", "copy-declarations": "cp -r src/declarations.d.ts dist/", "copy-jsx": "cpx \"src/**/*.jsx\" dist", "copy-tailwind-config": "cp -r src/tailwind.config.ts dist/", "minify": "find dist -name '*.js' | xargs -P 8 -I {} npx esbuild {} --minify --outfile={}", "prepare": "npm run build", "postbuild": "cpx \"src/**/*.css\" dist/ && rm -rf dist/node_modules"}, "repository": {"type": "git", "url": "git+ssh://**************/legalyard.web/toolkit/legalyard_toolkit_npm_package.git"}, "keywords": [], "author": "Legalyard", "license": "ISC", "bugs": {"url": "https://gitlab.com/legalyard.web/toolkit/legalyard_toolkit_npm_package/issues"}, "homepage": "https://gitlab.com/legalyard.web/toolkit/legalyard_toolkit_npm_package#readme", "peerDependencies": {"classnames": "^2.5.1", "moment": ">=2.30.1", "react": "^17.0.0 || >=18.0.0", "react-error-boundary": ">=4.0.13", "react-feather": ">=2.0.10", "styled-components": ">=6.1.8", "tailwindcss": ">=3.0.0", "uuid": ">=9.0.1"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-dom": {"optional": true}, "styled-components": {"optional": true}, "react-feather": {"optional": true}, "moment": {"optional": true}}, "devDependencies": {"@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-typescript": "^12.1.1", "@rollup/plugin-url": "^8.0.2", "@svgr/webpack": "^8.1.0", "@types/node": "^22.5.5", "@types/react": "^18.0.0", "@types/uuid": "^10.0.0", "classnames": "^2.5.1", "cpx": "^1.5.0", "esbuild": "^0.23.1", "install": "^0.13.0", "moment": "^2.30.1", "react": "^18.0.0", "react-error-boundary": "^4.0.13", "react-feather": "^2.0.10", "rollup": "^2.79.2", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "styled-components": "^6.1.13", "tailwindcss": "^3.0.0", "typescript": "^5.6.2", "uuid": "^9.0.1"}, "bundleDependencies": ["remirror", "@remirror/react", "@remirror/react-editors"], "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "@remirror/react": "^3.0.1", "@remirror/react-editors": "^2.0.1", "classnames": "^2.5.1", "remirror": "^3.0.1"}}