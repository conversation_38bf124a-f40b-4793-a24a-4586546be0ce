import { validArray } from "../../data/array";

export const getPath = (data: any[], currentId: string | number) => {
  try {
    const currentPath = validArray(data) ? data : [];
    const completePath = [...currentPath, currentId];
    return completePath;
  } catch (err) {
    console.error(err);
  }
};

export const getFileExtension = (name: string) => {
  try {
    if (name) {
      let fileName = name;
      const allowedExtensions = [".txt", ".html", ".php", ".js"];
      const desiredExtension = ".txt";

      if (fileName.includes(".")) {
        const parts = fileName.split(".");
        const fileExtension = `.${parts.pop()}`;

        if (!allowedExtensions.includes(fileExtension)) {
          fileName = parts.join(".") + desiredExtension;
        }
      } else {
        fileName += desiredExtension;
      }

      return fileName;
    }
  } catch (err) {
    console.error(err);
  }
};
