export const dataToSend = (
  parameters: string[],
  values: { [key: string]: any }
) => {
  try {
    const data: any = {};

    const hasNested = (propertyString: any) => {
      return propertyString.includes(".");
    };

    parameters?.forEach((prop: any) => {
      if (!hasNested(prop)) {
        if (<PERSON><PERSON>an(values?.[prop])) {
          data[prop] = values?.[prop];
        }
      } else {
        const properties = prop.split(".");
        if (Boolean(values?.[properties[0]])) {
          let currentData = values;

          for (const property of properties) {
            if (property in currentData) {
              currentData = currentData[property];
            } else {
              currentData[property] = {};
              currentData = currentData[property];
            }
          }

          currentData = values;
          for (const property of properties) {
            currentData = currentData[property];
          }

          data[properties[0]] = currentData;
        }
      }
    });

    return data;
  } catch (err) {
    console.error(err);
  }
};

export const getItemIds = (data: any[]) => {
  try {
    const idData = data?.map((item: any) => item?._id);
    return idData;
  } catch (err) {
    console.error(err);
  }
};

interface objType {
  localId?: string | number;
  _id?: string | number;
  [key: string]: any;
}

type outType = string | number | null | undefined;

export const toUseId = (obj: objType): outType => {
  try {
    return obj?.localId ?? obj?._id;
  } catch (err) {
    console.error(err);
    return null;
  }
};
