export const urlDownload = (
  url: string = "https://legalyard.com",
  name: string = "download"
) => {
  try {
    const urlContent = `[InternetShortcut]\r\nURL=${url}`;

    const blob = new Blob([urlContent], { type: "text/plain" });
    const objURL = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = objURL;
    link.download = `${name}.url`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error(error);
  }
};

export const textDownload = (
  content: string = "https://legalyard.com",
  name: string = "download"
) => {
  try {
    const finalContent = Boolean(content) ? content : "Start Writing here";
    const blob = new Blob([finalContent], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error(error);
  }
};

export const fileDownload = (
  url: string = "https://legalyard.com",
  name: string = "download"
) => {
  try {
    const link = document.createElement("a");
    link.href = url;
    link.download = name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error(error);
  }
};
