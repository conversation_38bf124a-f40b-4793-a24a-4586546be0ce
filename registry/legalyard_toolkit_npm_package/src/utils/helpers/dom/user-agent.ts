export const getCurrentOS = () => {
  try {
    const userAgent = window?.navigator?.userAgent;

    if (userAgent.indexOf("Win") !== -1) {
      return "windows";
    }

    if (userAgent.indexOf("Mac") !== -1) {
      return "macos";
    }

    if (userAgent.match(/iPhone|iPad|iPod/i)) {
      return "ios";
    }

    if (userAgent.match(/Android/i)) {
      return "android";
    }

    if (userAgent.indexOf("Linux") !== -1) {
      return "linux";
    }

    return "unknown";
  } catch (error) {
    console.error(error);
    return "unknown";
  }
};
