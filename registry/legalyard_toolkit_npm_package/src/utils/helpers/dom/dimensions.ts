export const getTotalHeight = (elements: any) => {
  try {
    if (!elements) {
      return 0;
    }

    if (!Array.isArray(elements)) {
      elements = [elements];
    }

    let totalHeight = 0;
    elements.forEach((id: any) => {
      const element = document.getElementById(id);
      if (element) {
        totalHeight += element.clientHeight;
      }
    });

    return totalHeight;
  } catch (err) {
    console.error(err);
  }
};
