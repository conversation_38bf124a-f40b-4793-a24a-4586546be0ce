import { KeyboardEvent } from "react";

export const emptyFieldValidator = (props: any) => {
  if (props === "") return { validate: false, msg: "Field is required" };
  else return { validate: true };
};

export const nameValidator = (props: any) => {
  const regExp = /^[A-Za-z ]+$/;
  if (props === "") return { validate: false, msg: "Field is required" };
  else if (!regExp.test(props))
    return { validate: false, msg: "Please enter characters only" };
  else return { validate: true };
};

export const radioValidator = (props: any) => {
  if (props === "") return { validate: false, msg: "Field is required" };
  else return { validate: true };
};

export const onlyNoValidator = (props: any) => {
  const regExp = /^[0-9\b]+$/;
  if (props === "") return { validate: false, msg: "Field is required" };
  else if (!regExp.test(props))
    return { validate: false, msg: "Please enter digits only" };
  else return { validate: true };
};

export const handleNumberException = (e: KeyboardEvent<HTMLInputElement>) => {
  try {
    if (e) {
      const accepts = [
        "Enter",
        "Backspace",
        "Shift",
        "Meta",
        "Tab",
        "Control",
        "Alt",
      ];

      const withAcceptance =
        accepts.includes(e.key) || ((e.ctrlKey || e.metaKey) && e.key === "a");

      const exceptThisSymbols = ["e", "E", "+", "-", ".", " "];

      if (
        !withAcceptance &&
        (exceptThisSymbols.includes(e.key) || isNaN(parseInt(e?.key)))
      ) {
        e.preventDefault();
      }
    }
  } catch (err) {
    console.error(err);
  }
};

export const handleRupeeException = (e: KeyboardEvent) => {
  try {
    if (e) {
      const exceptThisSymbols = ["e", "E", "+", "-", " "];
      exceptThisSymbols.includes(e.key) && e.preventDefault();
    }
  } catch (error) {
    console.error({ error });
  }
};

export const mobileValidator = (props: any) => {
  const regExp = /^[0-9\b]+$/;
  if (props === "") return { validate: false, msg: "Field is required" };
  else if (!regExp.test(props))
    return { validate: false, msg: "Please enter digits only" };
  else if (props.length < 10 || props.length > 10)
    return { validate: false, msg: "Please enter 10 digits" };
  else return { validate: true };
};

export const emailValidator = (props: any) => {
  const regExp = /^[\w.-]+@([\w-]+\.)+[\w-]{2,}$/;
  if (props === "") return { validate: false, msg: "Field is required" };
  else if (!regExp.test(props))
    return { validate: false, msg: "Enter valid email id" };
  else return { validate: true };
};

export const passwordValidator = (props: any) => {
  const minReg = props.length < 8;
  const maxReg = props.length >= 50;
  const alphaReg = props.search(/[A-Za-z]/) < 0;
  const digitReg = props.search(/[0-9]/) < 0;
  const speCharReg = props.search(/.*[!@#$%^&*() =+_-]/) < 0;

  const errMsg = [];

  if (props === "") return { validate: false, msg: "Field is required" };
  else if (minReg || maxReg) {
    errMsg.push("Length must be between 8 to 50 characters.");
    return {
      validate: false,
      msg: errMsg,
    };
  } else if (alphaReg) {
    errMsg.push("Your password must contain at least one alphabet.");
    return {
      validate: false,
      msg: errMsg,
    };
  } else if (digitReg) {
    errMsg.push("Must have at least one digit.");
    return {
      validate: false,
      msg: errMsg,
    };
  } else if (speCharReg) {
    errMsg.push("Must have at least one special character.");
    return {
      validate: false,
      msg: errMsg,
    };
  } else return { validate: true };
};

export const otpValidator = (props: any) => {
  const otpLengthRegex = /^\d{6}$/;
  if (props === "") return { validate: false, msg: "Field is required" };
  else if (!otpLengthRegex.test(props))
    return { validate: false, msg: "Enter valid OTP" };
  else return { validate: true };
};

export const dayValidator = (props: any) => {
  const minReg = parseInt(props) < 1;
  const maxReg = parseInt(props) > 31;
  if (props === "") return { validate: false, msg: "Field is required" };
  else if (minReg || maxReg)
    return { validate: false, msg: "Please enter valid Date" };
  else return { validate: true };
};

export const yearValidator = (props: any) => {
  const minLength = props?.length < 4;
  const maxLength = props?.length > 4;
  const date = new Date();
  // const maxReg = props > date.getFullYear();
  const minReg = props < date.getFullYear() - 150;
  if (props === "") return { validate: false, msg: "Field is required" };
  else if (minLength || maxLength)
    return { validate: false, msg: "Please enter valid year" };
  else if (minReg) return { validate: false, msg: "Please enter valid year" };
  else return { validate: true };
};

export const dateValidator = ({
  day,
  month,
  year,
}: {
  day: string | number;
  month: string | number;
  year: string | number;
}) => {
  const selectedDate = `${month} ${day}, ${year}`;
  const getDate = new Date(selectedDate).getDate() === Number(day);
  const getYear = new Date(selectedDate).getFullYear() === Number(year);
  const isValid = getDate && getYear;
  const inputDate = new Date(selectedDate);
  const currentDate = new Date();

  if (selectedDate === "") return { validate: false, msg: "Field is required" };
  else if (!isValid) return { validate: false, msg: "Please enter valid date" };
  else if (!isValid) return { validate: false, msg: "Please enter valid date" };
  else if (currentDate < inputDate)
    return { validate: false, msg: "Date should not be ahead of today" };
  else return { validate: true };
};

export const monthValidator = (props: any) => {
  if (props === "") return { validate: false, msg: "Field is required" };
  else return { validate: true };
};

export const pincodeValidator = (props: any) => {
  const regExp = /^[0-9\b]+$/;
  if (props === "") return { validate: false, msg: "Field is required" };
  else if (!regExp.test(props))
    return { validate: false, msg: "Please enter digits only" };
  else if (props.length < 6 || props.length > 6)
    return { validate: false, msg: "Please enter 6 digits" };
  else return { validate: true };
};

export const usernameValidator = (props: any) => {
  const minReg = props.length < 4;
  const maxReg = props.length > 25;
  const alphanumericRegex = props.match(/^[0-9a-zA-Z]+$/);
  const available = true;
  if (props === "") return { validate: false, msg: "Field is required" };
  else if (minReg || maxReg)
    return {
      validate: false,
      msg: "Usernames must be between 4 and 25 characters.",
    };
  else if (!alphanumericRegex)
    return {
      validate: false,
      msg: "Usernames must only contain alphanumeric characters.",
    };
  else if (!available)
    return {
      validate: false,
      msg: "This username is unavailable.",
    };
  else return { validate: true };
};

// Checks if user loggedin with token
export const isUserLoggedIn = () => localStorage.getItem("userData");

// Checks if user loggedin
export const getUserData = () => {
  try {
    const data = localStorage.getItem("userData");
    if (Boolean(data)) {
      return JSON.parse(String(data));
    } else return "";
  } catch (err) {
    console.error(err);
  }
};

// Checks if an object is empty (returns boolean)
export const isObjEmpty = (obj: any) => Object.keys(obj).length === 0;

// Returns K format from a number
export const kFormatter = (num: any) =>
  num > 999 ? `${(num / 1000).toFixed(1)}k` : num;

// Converts HTML to string
export const htmlToString = (html: any) => html.replace(/<\/?[^>]+(>|$)/g, "");

// ** Checks if the passed date is today
const isToday = (date: any) => {
  const today = new Date();
  return (
    /* eslint-disable operator-linebreak */
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
    /* eslint-enable */
  );
};

// This function is used for demo purpose route navigation
export const getHomeRouteForLoggedInUser = (userRole: any) => {
  if (userRole === "admin") return "/";
  if (userRole === "client") return "/access-control";
  return "/";
};

// Format and return date in Humanize format
export const formatDate = (
  value: any,
  formatting: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "numeric",
    year: "numeric",
  }
) => {
  if (!value) return value;
  return new Intl.DateTimeFormat("en-US", formatting).format(new Date(value));
};

// Returns short month of passed date
export const formatDateToMonthShort = (
  value: any,
  toTimeForCurrentDay: boolean = true
) => {
  const date = new Date(value);
  let formatting: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "numeric",
  };

  if (toTimeForCurrentDay && isToday(date)) {
    formatting = { hour: "numeric", minute: "numeric" };
  }

  return new Intl.DateTimeFormat("en-US", formatting).format(new Date(value));
};
