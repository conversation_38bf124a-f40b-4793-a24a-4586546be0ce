import { useCallback, useEffect, useRef, useState } from "react";

export interface UseTypingType {
  texts: string[];
  interval?: number;
  timeout?: number;
  prefix?: string;
  suffix?: string;
}

const useTyping = ({
  texts,
  interval = 120,
  timeout = 2000,
  prefix = "",
  suffix = "",
}: UseTypingType) => {
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const [placeholderText, setPlaceholderText] = useState<string>("search ...");
  const textIndexRef = useRef(0);
  const charIndexRef = useRef(0);

  const clearExistingInterval = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const updatePlaceholder = useCallback(() => {
    const currentText = `${prefix} ${
      texts[textIndexRef.current]
    } ${suffix}`.trim();
    setPlaceholderText(currentText.slice(0, charIndexRef.current + 1));

    charIndexRef.current++;

    if (charIndexRef.current > currentText.length) {
      setTimeout(() => {
        charIndexRef.current = 0;
        textIndexRef.current = (textIndexRef.current + 1) % texts.length;
      }, timeout);
    }
  }, [prefix, suffix, texts, timeout]);

  useEffect(() => {
    if (!texts.length) return;

    clearExistingInterval();

    intervalRef.current = setInterval(updatePlaceholder, interval);

    return clearExistingInterval;
  }, [texts, interval, prefix, suffix, timeout, updatePlaceholder]);

  return { text: placeholderText };
};

export default useTyping;
