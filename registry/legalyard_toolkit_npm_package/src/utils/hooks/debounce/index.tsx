export function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number
): T {
  let timeout: NodeJS.Timeout | null = null;

  return function (this: any, ...args: Parameters<T>) {
    if (timeout !== null) {
      clearTimeout(timeout);
    }

    const context = this;
    const eventArgs = args.map((arg) =>
      arg instanceof Event ? { ...arg } : arg
    );

    timeout = setTimeout(() => {
      try {
        func.apply(context, eventArgs);
      } catch (error) {
        console.error("An error occurred in the debounced function:", error);
      }
    }, wait);
  } as T;
}

function useDebounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  return debounce(func, wait);
}

export default useDebounce;
