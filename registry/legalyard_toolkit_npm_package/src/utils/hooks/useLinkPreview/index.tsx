import { useState, useEffect } from "react";

interface Metadata {
  title?: string;
  description?: string;
  image?: string;
  color?: string;
}

interface UseLinkPreviewResult extends Metadata {}

const useLinkPreview = (propUrl: string): UseLinkPreviewResult => {
  const [metadata, setMetadata] = useState<Metadata | null>(null);

  useEffect(() => {
    const fetchMetadata = async () => {
      try {
        if (propUrl) {
          const url =
            propUrl.startsWith("http://") || propUrl.startsWith("https://")
              ? propUrl
              : `http://${propUrl}`;

          const response = await fetch(url);
          const html = await response.text();

          const matches = html.match(
            /<meta property="og:(.*)" content="(.*)"\/>/g
          );
          const data: Partial<Metadata> = {};

          if (matches) {
            matches.forEach((match) => {
              const [property, content] = match.match(
                /<meta property="og:(.*)" content="(.*)"\/>/
              )!;
              data[property as keyof Metadata] = content;
            });
          }

          const themeColorMatch = html.match(
            /<meta name="theme-color" content="(.*)"\/>/
          );
          if (themeColorMatch) {
            data.color = themeColorMatch[1];
          }

          setMetadata(data as Metadata);
        }
      } catch (error) {
        console.error("Error fetching metadata:", error);
      }
    };

    fetchMetadata();
  }, [propUrl]);

  return { ...metadata };
};

export default useLinkPreview;
