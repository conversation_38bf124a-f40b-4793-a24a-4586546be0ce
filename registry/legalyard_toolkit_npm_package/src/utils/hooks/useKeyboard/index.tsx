import { useEffect, useCallback } from "react";

type KeyCode = string;
type Callback = () => void;

const useKeyboard = (callFunction: Callback, keyCode: KeyCode): void => {
  const handleKeydown = useCallback(
    (e: KeyboardEvent) => {
      if (e.code === keyCode && callFunction) {
        callFunction();
      }
    },
    [callFunction, keyCode]
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeydown);

    return () => {
      document.removeEventListener("keydown", handleKeydown);
    };
  }, [handleKeydown]);
};

export default useKeyboard;
