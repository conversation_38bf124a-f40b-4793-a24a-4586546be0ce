import { FileText } from "react-feather";

// TODO
// move this to DB

export const masterDataGroups = {
  documentCategory: {
    name: "Document Category",
    path: "document-category",
    group: "documentCategory",
    data: [
      {
        name: "Agreements",
        default: true,
        description: null,
      },
      {
        name: "Answers",
        default: true,
        description: null,
      },
      {
        name: "<PERSON><PERSON>",
        default: true,
        description: null,
      },
      {
        name: "Briefs",
        default: true,
        description: null,
      },
      {
        name: "Cause Of Action",
        default: true,
        description: null,
      },
      {
        name: "Closings",
        default: true,
        description: null,
      },
      {
        name: "Communications",
        default: true,
        description: null,
      },
      {
        name: "Motions",
        default: true,
        description: null,
      },
      {
        name: "Complain<PERSON>",
        default: true,
        description: null,
      },
      {
        name: "Contracts",
        default: true,
        description: null,
      },
      {
        name: "Opinions",
        default: true,
        description: null,
      },
      {
        name: "Statutes",
        default: true,
        description: null,
      },
      {
        name: "Precedents",
        default: true,
        description: null,
      },
      {
        name: "Disclosures",
        default: true,
        description: null,
      },
      {
        name: "Orders",
        default: true,
        description: null,
      },
      {
        name: "Discovery",
        default: true,
        description: null,
      },
      {
        name: "Forms",
        default: true,
        description: null,
      },
      {
        name: "Resolutions",
        default: true,
        description: null,
      },
      {
        name: "Statements",
        default: true,
        description: null,
      },
      {
        name: "Letters",
        default: true,
        description: null,
      },
      {
        name: "Offers",
        default: true,
        description: null,
      },
      {
        name: "Rules",
        default: true,
        description: null,
      },
      {
        name: "Settlements",
        default: true,
        description: null,
      },
      {
        name: "Instructions",
        default: true,
        description: null,
      },
      {
        name: "Notice",
        default: true,
        description: null,
      },
      {
        name: "Communication",
        default: true,
        description: null,
      },
      {
        name: "Event",
        default: true,
        description: null,
      },
      {
        name: "Legal Research Library",
        default: true,
        description: null,
      },
      {
        name: "Motion",
        default: true,
        description: null,
      },
      {
        name: "Task",
        default: true,
        description: null,
      },
      {
        name: "Reports",
        default: true,
        description: null,
      },
      {
        name: "Wills",
        default: true,
        description: null,
      },
      {
        name: "Research",
        default: true,
        description: null,
      },
      {
        name: "Pleadings",
        default: true,
        description: null,
      },
      {
        name: "Others",
        default: true,
        description: null,
      },
    ],
  },
  contactRelation: {
    name: "Contact Relation",
    path: "contact-relation",
    group: "",
    data: [
      {
        name: "Plaintiff",
        default: true,
        description: null,
      },
      {
        name: "Witness",
        default: true,
        description: null,
      },
      {
        name: "Defendant",
        default: true,
        description: null,
      },
      {
        name: "Petitioner",
        default: true,
        description: null,
      },
      {
        name: "Responden",
        default: true,
        description: null,
      },
      {
        name: "Defense Attorney",
        default: true,
        description: null,
      },
      {
        name: "Plaintiff's Attorney",
        default: true,
        description: null,
      },
      {
        name: "Court Judge",
        default: true,
        description: null,
      },
      {
        name: "Others",
        default: true,
        description: null,
      },
    ],
  },
  courts: {
    name: "Courts",
    path: "courts",
    group: "",
    data: [
      {
        name: "Others",
        default: true,
        description: null,
      },
    ],
  },
  taskTypes: {
    name: "Task Types",
    path: "task-types",
    group: "",
    data: [
      {
        name: "Analysis/Strategy",
        default: true,
        description: null,
      },
      {
        name: "Uncategorized",
        default: true,
        description: null,
      },
      {
        name: "Appeal",
        default: true,
        description: null,
      },
      {
        name: "Document/File Management",
        default: true,
        description: null,
      },
      {
        name: "Fact Investigation/Development",
        default: true,
        description: null,
      },
      {
        name: "Miscellaneous",
        default: true,
        description: null,
      },
      {
        name: "Plan and prepare for",
        default: true,
        description: null,
      },
      {
        name: "Research",
        default: true,
        description: null,
      },
      {
        name: "Review/analyze",
        default: true,
        description: null,
      },
      {
        name: "Trial Preparation",
        default: true,
        description: null,
      },
    ],
  },
  taskGroups: {
    name: "Task Groups",
    path: "task-groups",
    group: "",
    data: [
      {
        name: "Client Meeting",
        default: true,
        description: null,
      },
    ],
  },
  eventType: {
    name: "Event Types",
    path: "event-types",
    group: "",
    data: [
      {
        name: "Appeal",
        default: true,
        description: null,
      },
      {
        name: "Continuation Filing",
        default: true,
        description: null,
      },
      {
        name: "Divisional Filing",
        default: true,
        description: null,
      },
      {
        name: "Filing",
        default: true,
        description: null,
      },
      {
        name: "Hearing",
        default: true,
        description: null,
      },
      {
        name: "Meeting",
        default: true,
        description: null,
      },
      {
        name: "Misc Preparation",
        default: true,
        description: null,
      },
      {
        name: "Reply Brief",
        default: true,
        description: null,
      },
      {
        name: "Trial",
        default: true,
        description: null,
      },
      {
        name: "Others",
        default: true,
        description: null,
      },
    ],
  },
  matter: {
    legalResearch: {
      name: "Legal Research Categories",
      path: "legal-research-categories",
      group: "",
      data: [
        {
          name: "Applied",
          default: true,
          description: null,
        },
        {
          name: "Doctrinal",
          default: true,
          description: null,
        },
        {
          name: "Divisional Filing",
          default: true,
          description: null,
        },
        {
          name: "Comparative",
          default: true,
          description: null,
        },
        {
          name: "Analytical",
          default: true,
          description: null,
        },
        {
          name: "Non-doctrinal",
          default: true,
          description: null,
        },
        {
          name: "Uncategorized",
          default: true,
          description: null,
        },
        {
          name: "Conceptual",
          default: true,
          description: null,
        },
        {
          name: "Empirical",
          default: true,
          description: null,
        },
        {
          name: "Pure",
          default: true,
          description: null,
        },
        {
          name: "Quantitative",
          default: true,
          description: null,
        },
        {
          name: "Descriptive",
          default: true,
          description: null,
        },
        {
          name: "Qualitative",
          default: true,
          description: null,
        },
        {
          name: "Others",
          default: true,
          description: null,
        },
      ],
    },
    practiceArea: {
      name: "Matter Practice Area",
      path: "matter-practice-area",
      group: "",
      data: [
        {
          name: "Conveyance",
          default: true,
          description: null,
        },
        {
          name: "Real Estate",
          default: true,
          description: null,
        },
        {
          name: "Civil Litigation",
          default: true,
          description: null,
        },
        {
          name: "Criminal Defense",
          default: true,
          description: null,
        },
        {
          name: "Immigration",
          default: true,
          description: null,
        },
        {
          name: "Bankruptcy",
          default: true,
          description: null,
        },
        {
          name: "Family",
          default: true,
          description: null,
        },
        {
          name: "Estates",
          default: true,
          description: null,
        },
        {
          name: "Banking and Finance",
          default: true,
          description: null,
        },
        {
          name: "Employment",
          default: true,
          description: null,
        },
        {
          name: "Criminal",
          default: true,
          description: null,
        },
        {
          name: "Business",
          default: true,
          description: null,
        },
        {
          name: "Administrative",
          default: true,
          description: null,
        },
        {
          name: "Corporate",
          default: true,
          description: null,
        },
        {
          name: "Wills",
          default: true,
          description: null,
        },
        {
          name: "Uncategorize",
          default: true,
          description: null,
        },
        {
          name: "Insurance",
          default: true,
          description: null,
        },
        {
          name: "Commercial",
          default: true,
          description: null,
        },
        {
          name: "Personal Injury",
          default: true,
          description: null,
        },
        {
          name: "Tax",
          default: true,
          description: null,
        },
        {
          name: "Others",
          default: true,
          description: null,
        },
      ],
    },
    stages: {
      name: "Matter Stages",
      path: "matter-stages",
      group: "",
      data: [
        {
          name: "Discovery",
          default: true,
          description: null,
        },
        {
          name: "On Hold",
          default: true,
          description: null,
        },
        {
          name: "Pre-Filing",
          default: true,
          description: null,
        },
        {
          name: "In Trial",
          default: true,
          description: null,
        },
        {
          name: "Pleading",
          default: true,
          description: null,
        },
        {
          name: "Others",
          default: true,
          description: null,
        },
      ],
    },
    status: {
      name: "Matter Status",
      path: "matter-status",
      group: "",
      data: [
        {
          name: "Open",
          default: true,
          description: null,
        },
        {
          name: "Close",
          default: true,
          description: null,
        },
        {
          name: "Stayed",
          default: true,
          description: null,
        },
        {
          name: "Uncategorized",
          default: true,
          description: null,
        },
        {
          name: "Others",
          default: true,
          description: null,
        },
      ],
    },
  },
  permissionGroups: {
    name: "Permission Groups",
    path: "permission-groups",
    group: "",
    data: [
      {
        name: "Everyone",
        default: true,
        description: null,
      },
      {
        name: "Me",
        default: true,
        description: null,
      },
    ],
  },
};
