import { FC, useEffect, useRef, useState } from "react";
import classNames from "classnames";
import { ChevronDown, ChevronUp } from "react-feather";
import { propsType } from "./types";

const Collapse: FC<propsType> = ({
  title,
  children,
  className,
  onChange,
  value,
}) => {
  const contentRef = useRef<HTMLDivElement | null>(null);
  const [open, setOpen] = useState(value ?? false);
  const [contentHeight, setContentHeight] = useState(0);

  useEffect(() => {
    if (value) {
      setOpen(value);
    }
  }, [value]);

  const handleChange = () => {
    try {
      setOpen((prev) => {
        if (onChange) {
          onChange(!prev);
        }
        return !prev;
      });
    } catch (error) {
      console.error(error);
    }
  };

  const updateContentHeight = () => {
    const content = contentRef.current;
    setContentHeight(content?.clientHeight ?? 0);
  };

  useEffect(() => {
    updateContentHeight();

    const observer = new MutationObserver(() => {
      updateContentHeight();
    });

    if (contentRef.current) {
      observer.observe(contentRef.current, {
        childList: true,
        subtree: true,
      });
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div
      aria-label="Collapse"
      className={classNames(
        "collapse flex flex-col gap-0 w-full h-full shadow rounded-base",
        className
      )}
    >
      <div
        className={classNames(
          "collapse-action flex items-center justify-between gap-x-1 bg-white px-3 py-3 cursor-pointer hover:bg-slate-100",
          !open && "rounded-br-base rounded-bl-base"
        )}
        onClick={handleChange}
      >
        <h3 className="collapse-action-title text-sb">{title}</h3>
        <div className="collapse-action-icon flex items-center justify-center">
          {open ? <ChevronUp /> : <ChevronDown />}
        </div>
      </div>
      <div
        ref={contentRef}
        aria-label="content wrapper"
        className={classNames(
          "collapse-content border-slate-300 overflow-hidden transition-all duration-300",
          open ? "!max-h-screen border-t " : "max-h-0"
        )}
        style={{ maxHeight: open ? contentHeight : 0 }}
      >
        {children}
      </div>
    </div>
  );
};

export default Collapse;
