import classNames from "classnames";
import { forwardRef, ReactNode, useEffect, useRef, useState } from "react";

export interface MiniCollapseReferenceType {
  toggle: (v: boolean) => void;
  open: boolean;
}

export interface MiniCollapseType {
  children: ReactNode;
  className?: string;
  contentClassName?: string;
  open: boolean;
}

const MiniCollapse = forwardRef<MiniCollapseReferenceType, MiniCollapseType>(
  ({ children, className, contentClassName, open, ...rest }, ref) => {
    const contentRef = useRef<HTMLDivElement | null>(null);
    const [contentHeight, setContentHeight] = useState(0);

    const updateContentHeight = () => {
      const content = contentRef.current;
      setContentHeight(content?.clientHeight ?? 0);
    };

    useEffect(() => {
      updateContentHeight();

      const resizeObserver = new ResizeObserver(() => {
        updateContentHeight();
      });

      if (contentRef.current) {
        resizeObserver.observe(contentRef.current);
      }

      return () => resizeObserver.disconnect();
    }, []);

    return (
      <div
        className={classNames(
          "mini-collapse flex-1 h-full transition-all overflow-hidden ease-in-out duration-200",
          className
        )}
        style={{ maxHeight: open ? contentHeight : 0 }}
        {...rest}
      >
        <div
          ref={contentRef}
          aria-label="content wrapper"
          className={classNames(
            "mini-collapse-content h-full",
            contentClassName
          )}
        >
          {children}
        </div>
      </div>
    );
  }
);

export default MiniCollapse;
