import { createElement, isValidElement, MouseE<PERSON>, ReactNode } from "react";
import { ChevronRight } from "react-feather";
import { MenuDataItemType } from "../../types";

const MenuItem = ({ menu }: { menu: MenuDataItemType }) => {
  const handleEvent = (e: MouseEvent<HTMLDivElement>) => {
    try {
      e?.preventDefault();
      e?.stopPropagation();

      if (menu?.event) {
        menu?.event();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const renderIcon = (icon: any) => {
    try {
      const IconComponent = icon;
      return createElement(IconComponent, null);
    } catch (error) {
      return "";
    }
  };

  return (
    <div
      className="menu-item flex items-center justify-between space-x-2 [&:not(button)]:py-[0.55rem] [&:not(button)]:px-3 "
      onClick={(e) => handleEvent(e)}
    >
      <div className="flex items-center gap-x-1.5">
        {menu?.icon && (
          <div className="menu-item-icon-container relative max-w-max [&>svg]:w-3 [&>svg]:h-3">
            {typeof menu?.icon === "string" ? (
              <img
                src={menu?.icon ? menu?.icon : ""}
                alt={menu?.icon ?? "menu-icon"}
                className="menu-item-icon w-3 h-3 object-contain"
              />
            ) : isValidElement(menu?.icon) ? (
              <>{menu?.icon}</>
            ) : (
              typeof menu?.icon === "object" && (
                <>{renderIcon(menu?.icon as ReactNode)}</>
              )
            )}
          </div>
        )}

        <div className="menu-item-title-container flex flex-col items-start gap-y-1">
          {menu?.title && (
            <div className="menu-item-title capitalize text-sb">
              {menu?.title}
            </div>
          )}
          {menu?.text && (
            <div className="menu-item-text text-xs text-gray">{menu?.text}</div>
          )}
        </div>
      </div>

      {menu?.child ? (
        <ChevronRight className="menu-item-toggle-icon" />
      ) : menu?.rightText ? (
        <p className="menu-item-right-text text-xs text-slate-400">
          {menu?.rightText}
        </p>
      ) : null}
    </div>
  );
};

export default MenuItem;
