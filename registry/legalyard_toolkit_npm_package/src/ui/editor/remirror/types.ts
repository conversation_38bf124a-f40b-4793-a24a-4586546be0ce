import { HTMLAttributes, ReactNode } from "react";
import { RemirrorJSON } from "remirror";

export interface RemirrorEditorReferenceType {
  insertImage?: (src: string) => void;
}

export interface RemirrorEditorType {
  id?: string;
  name?: string;
  className?: string;
  valueFormat?: "json" | "html";
  initialContent?: string | RemirrorJSON;
  variant?: "standard" | "wysiwyg" | "social" | "markdown";
  placeholder?: string;
  onChange?: (data: any) => void;
  autoRender?: boolean | "end" | "start";
  toolbarPosition?: "top" | "bottom";
  autoFocus?: boolean;
  extensions?: ExtensionsType;
  toolbar?: ToolbarType;
  users?: {
    id: string;
    label: string;
  }[];
  tags?: {
    id: string;
    label: string;
  }[];
}

export interface ToolbarType {
  additionalTools?: ReactNode;
  className?: string;
}

export interface ExtensionsType {
  collapsibleList?: boolean;
  spineList?: boolean;
  autoLink?: boolean;
  selectTextOnClick?: boolean;
  defaultFontSize?: string;
  excludeAlignmentNodes?: any[];
  image?: {
    /**
     * @note resize will allow you to resize image.
     */
    resize?: boolean;
    /**
     * @note onImageUpload will call on Click of Upload Image.
     */
    onImageUpload?: (file?: File) => string | Promise<string> | void;
    /**
     * @note onImageSelect will call on click of Choose Existing Image.
     */
    onImageSelect?: () => string;
    /**
     * @note with selectedImageUrl you can pass image url which you want to paste.
     */
    selectedImageUrl?: string;
    /**
     * @note ImageFormatsAccept for which types of image formate you want to allow.
     */
    ImageFormatsAccept?: string;
    /**
     * @note with fileInputProps, spread file input props.
     */
    fileInputProps?: HTMLAttributes<HTMLInputElement>;
  };
}
