import {
  Dispatch,
  forwardRef,
  ForwardRefRenderFunction,
  ReactNode,
  SetStateAction,
  useMemo,
  useState,
} from "react";
import { Context } from ".";
import { RemirrorEditorReferenceType, RemirrorEditorType } from "../types";
import { editorTheme, useEditorHelper } from "./helpers";

interface ContextTypes extends RemirrorEditorType {
  children?: ReactNode;
}

export interface StateType {
  currentTextColor: string;
  setCurrentTextColor: Dispatch<SetStateAction<string>>;
  currentHighlightColor: string;
  setCurrentHighlightColor: Dispatch<SetStateAction<string>>;
}

const EditorProvider: ForwardRefRenderFunction<
  RemirrorEditorReferenceType,
  ContextTypes
> = ({ children, ...rest }, ref) => {
  const [currentTextColor, setCurrentTextColor] = useState<string>("#000000");
  const [currentHighlightColor, setCurrentHighlightColor] =
    useState<string>("");

  const helper = useEditorHelper({
    ...rest,
  });

  const statesData: StateType = useMemo(
    () => ({
      currentTextColor,
      setCurrentTextColor,
      currentHighlightColor,
      setCurrentHighlightColor,
    }),
    [
      currentTextColor,
      setCurrentTextColor,
      currentHighlightColor,
      setCurrentHighlightColor,
    ]
  );

  const valueGroup = useMemo(
    () => ({
      editorRef: ref,
      editorTheme,
      helper,
      state: statesData,
      ...rest,
    }),
    [helper, rest, statesData]
  );

  return (
    <Context.Provider value={{ ...valueGroup }}>{children}</Context.Provider>
  );
};

export default forwardRef(EditorProvider);
