import { useCallback } from "react";
import { <PERSON>mirrorJSON } from "remirror";
import { RemirrorEditorType } from "../types";
import { styleVariables } from "../../../../styles";

export type EditorThemeType = { [key: string]: any };
export const editorTheme: EditorThemeType = {
  fontFamily: { default: "Roboto, sans-serif" },
  color: {
    foreground: "#e0e",
    background: "#aaa",
    primary: "#f00",
    secondary: "#00f",
    muted: "#0ee",
    faded: "#ee0",
    border: "transparent",
    shadow1: "#550",
    shadow2: "#050",
    shadow3: "#500",
    outline: "transparent",
    active: {
      primary: styleVariables.color.main,
      border: styleVariables.color.sub,
    },
    hover: {
      primary: "#121212",
      border: "#ee00ee",
    },
  },
  boxShadow: {
    1: "none",
    2: "none",
    3: "none",
  },
  styles: {
    ".remirror-editor:focus-within": {
      outline: "5px solid #ff0000",
      borderRadius: "8px",
    },
  },
};

interface HelperType extends RemirrorEditorType {}

export const useEditorHelper = ({
  name = "textEditor",
  onChange,
}: HelperType): HelperExportType => {
  const handleOnchangeCallback = useCallback(
    (value: string | RemirrorJSON) => {
      try {
        if (onChange) {
          onChange({
            target: {
              type: "richtext",
              name,
              value,
            },
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    [name, onChange]
  );

  return {
    handleOnchangeCallback,
  };
};

export interface HelperExportType {
  handleOnchangeCallback: (value: string | RemirrorJSON) => void;
}
