import { createContext, ForwardedRef, useContext } from "react";
import { RemirrorEditorReferenceType, RemirrorEditorType } from "../types";
import { EditorThemeType, HelperExportType } from "./helpers";
import { StateType } from "./provider";

export interface ContextValueType extends RemirrorEditorType {
  editorRef: ForwardedRef<RemirrorEditorReferenceType>;
  editorTheme: EditorThemeType;
  helper: HelperExportType;
  state: StateType;
}
export const Context = createContext<ContextValueType | undefined>(undefined);

const useEditor = (): ContextValueType => {
  const context = useContext(Context);

  if (!context) {
    throw new Error("useEditor must be used within a EditorProvider");
  }

  return context;
};

export default useEditor;
