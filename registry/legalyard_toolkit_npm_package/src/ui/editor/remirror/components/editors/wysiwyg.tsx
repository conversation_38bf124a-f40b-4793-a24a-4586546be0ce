import { OnChangeHTML, OnChangeJSON } from "@remirror/react";
import { WysiwygEditor } from "@remirror/react-editors/wysiwyg";
import useEditor from "../../@context";

const Wysiwyg = () => {
  const {
    editorTheme,
    valueFormat,
    initialContent,
    helper: { handleOnchangeCallback },
    placeholder = "Start typing here...",
    autoFocus = false,
  } = useEditor();

  return (
    <WysiwygEditor
      theme={editorTheme}
      initialContent={initialContent}
      placeholder={placeholder}
      autoFocus={autoFocus}
    >
      {valueFormat === "html" ? (
        <OnChangeHTML onChange={handleOnchangeCallback} />
      ) : (
        <OnChangeJSON onChange={handleOnchangeCallback} />
      )}
    </WysiwygEditor>
  );
};

export default Wysiwyg;
