import { OnChangeHTML, OnChangeJSON } from "@remirror/react";
import { MarkdownEditor } from "@remirror/react-editors/markdown";
import useEditor from "../../@context";

const Markdown = () => {
  const {
    editorTheme,
    valueFormat,
    initialContent,
    helper: { handleOnchangeCallback },
    placeholder = "Start typing here...",
    autoFocus = false,
  } = useEditor();

  return (
    <MarkdownEditor
      theme={editorTheme}
      placeholder={placeholder}
      initialContent={initialContent}
      autoFocus={autoFocus}
    >
      {valueFormat === "html" ? (
        <OnChangeHTML onChange={handleOnchangeCallback} />
      ) : (
        <OnChangeJSON onChange={handleOnchangeCallback} />
      )}
    </MarkdownEditor>
  );
};

export default Markdown;
