import { useCommands } from "@remirror/react";
import {
  CommandButtonGroup,
  CommandMenuItem,
  DropdownButton,
} from "@remirror/react-ui";
import classNames from "classnames";
import {
  ChangeEvent,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
} from "react";
import { Link2, Mouse<PERSON>ointer, Upload } from "react-feather";
import useEditor from "../../../../../../../@context";

const ImageUploadButtons = () => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const { insertImage } = useCommands();

  const { id, name, editorRef, extensions: { image } = {} } = useEditor();

  const {
    ImageFormatsAccept = "image/png, image/jpeg, image/jpg",
    fileInputProps = {},
    selectedImageUrl = "",
  } = image || {};

  const handleOutsideClick = () => {
    try {
      setTimeout(() => {
        const backdropElement = document.querySelector(
          ".MuiBackdrop-root"
        ) as HTMLElement;
        backdropElement?.click();
      }, 0);
    } catch (error) {
      console.error(error);
    }
  };

  const handleInsertImage = useCallback((imageSrc: string) => {
    try {
      handleOutsideClick();
      insertImage({
        src: imageSrc ?? "",
      });
    } catch (error) {
      console.error(error);
    }
  }, []);

  useImperativeHandle(editorRef, () => ({
    insertImage: (src: string) => handleInsertImage(src),
  }));

  useEffect(() => {
    if (selectedImageUrl) {
      handleInsertImage(selectedImageUrl);
    }
  }, [selectedImageUrl]);

  const handleFileInputInvoke = () => {
    if (fileInputRef.current) fileInputRef.current?.click();
  };

  const handleUploadFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    try {
      const file = e?.target?.files?.[0];
      if (file) {
        if (image?.onImageUpload) {
          const imageUrl = await image?.onImageUpload(file);
          if (imageUrl && typeof imageUrl === "string") {
            handleInsertImage(imageUrl);
          }
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleSelectFileInvoke = () => {
    if (image?.onImageSelect) {
      image?.onImageSelect();
    }
  };

  return (
    <CommandButtonGroup>
      <DropdownButton aria-label="Insert Image" icon="imageAddLine">
        <input
          ref={fileInputRef}
          type="file"
          id={id ?? name ?? "richtext-image-uploader"}
          onChange={handleUploadFileChange}
          multiple={false}
          accept={ImageFormatsAccept}
          className={classNames("hidden", fileInputProps?.className)}
          {...(fileInputProps ?? {})}
        />
        {Boolean(image?.onImageUpload) && (
          <CommandMenuItem
            key="uploadImage"
            commandName="uploadImage"
            onSelect={() => handleFileInputInvoke()}
            enabled={true}
            label={
              <span className="flex items-center gap-2 py-1">
                <Upload /> Upload Image
              </span>
            }
          />
        )}

        {Boolean(image?.onImageSelect) && (
          <CommandMenuItem
            key="chooseExisting"
            commandName="chooseExisting"
            onSelect={() => handleSelectFileInvoke()}
            enabled={true}
            label={
              <span className="flex items-center gap-2 py-1">
                <MousePointer /> Choose Existing Image
              </span>
            }
          />
        )}

        <CommandMenuItem
          key="linkInsert"
          commandName="linkInsert"
          onSelect={() => {
            const url = prompt("Enter image URL");
            if (url) {
              handleInsertImage(url);
            }
          }}
          enabled={true}
          label={
            <span className="flex items-center gap-2 py-1">
              <Link2 /> Insert Image url
            </span>
          }
        />
      </DropdownButton>
    </CommandButtonGroup>
  );
};

export default ImageUploadButtons;
