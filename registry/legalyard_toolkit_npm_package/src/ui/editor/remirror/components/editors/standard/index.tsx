import { styleVariables } from "../../../../../../styles";
import { EditorComponent, Remirror, useRemirror } from "@remirror/react";
import classNames from "classnames";
import { useCallback } from "react";
import {
  BlockquoteExtension,
  BoldExtension,
  BulletListExtension,
  DropCursorExtension,
  FontFamilyExtension,
  FontSizeExtension,
  HardBreakExtension,
  HeadingExtension,
  ImageExtension,
  ItalicExtension,
  LinkExtension,
  ListItemExtension,
  NodeFormattingExtension,
  OrderedListExtension,
  PlaceholderExtension,
  TableExtension,
  TaskListExtension,
  TextCaseExtension,
  TextColorExtension,
  TextHighlightExtension,
  UnderlineExtension,
  EmojiExtension,
  HorizontalRuleExtension,
  MentionAtomExtension,
  ShortcutsExtension,
} from "remirror/extensions";
import useEditor from "../../../@context";
import RemirrorToolbar from "./components/toolbar";
import MentionSuggester from "./components/toolbar/components/mention";

// TODO
// Link editor popup

const Standard = () => {
  const {
    placeholder = "Start typing here...",
    helper: { handleOnchangeCallback },
    extensions: {
      spineList = true,
      collapsibleList = true,
      selectTextOnClick = false,
      autoLink = true,
      defaultFontSize = "16px",
      excludeAlignmentNodes = [],
      image: { resize = true } = {},
    } = {},
    valueFormat,
    autoFocus = false,
    toolbarPosition = "top",
  } = useEditor();

  const extensions = useCallback(
    () => [
      new PlaceholderExtension({ placeholder }),
      new BoldExtension({ weight: "bold" }),
      new ItalicExtension(),
      new UnderlineExtension(),
      new BlockquoteExtension(),
      new BulletListExtension({ enableSpine: spineList }),
      new OrderedListExtension(),
      new TaskListExtension(),
      new ListItemExtension({ enableCollapsible: collapsibleList }),
      new LinkExtension({ autoLink, selectTextOnClick }),
      new ImageExtension({ enableResizing: resize }),
      new DropCursorExtension({ color: styleVariables?.color?.main }),
      new TextHighlightExtension({ defaultHighlight: "transparent" }),
      new TextColorExtension({ defaultColor: "#000000" }),
      new FontFamilyExtension(),
      new FontSizeExtension({
        defaultSize: defaultFontSize,
        unit: "px",
      }),
      new NodeFormattingExtension({ excludeNodes: excludeAlignmentNodes }),
      new HardBreakExtension(),
      new HeadingExtension({ levels: [1, 2, 3, 4, 5, 6], defaultLevel: 3 }),
      new TextCaseExtension({ defaultCasing: "none" }),
      new TableExtension({ resizable: true, tabKeyboardShortcuts: true }),
      new HorizontalRuleExtension({ insertionNode: "paragraph" }),
      new ShortcutsExtension(),
      new MentionAtomExtension({
        matchers: [
          { name: "at", char: "@" },
          { name: "tag", char: "#" },
        ],
      }),
    ],
    [
      autoLink,
      collapsibleList,
      defaultFontSize,
      excludeAlignmentNodes,
      resize,
      placeholder,
      selectTextOnClick,
      spineList,
    ]
  );

  const { manager, state } = useRemirror({
    extensions,
    selection: "start",
    stringHandler: "html",
  });

  const handleChange = useCallback(
    (config: any) => {
      try {
        const content =
          valueFormat === "html"
            ? config?.helpers?.getHTML(config?.state)
            : config?.helpers?.getJSON(config?.state);

        handleOnchangeCallback?.(content);
      } catch (error) {
        console.error("Error handling content change:", error);
      }
    },
    [handleOnchangeCallback, state, valueFormat]
  );

  return (
    <div
      className={classNames(
        "remirror-standard h-full w-full",
        "[&_.remirror-editor]:relative [&_.remirror-editor]:min-h-60 [&_.remirror-editor]:shadow-sm [&_.remirror-editor]:p-4 [&_.remirror-editor]:my-2 [&_.remirror-editor]:rounded-base [&_.remirror-editor]:outline-0 [&_.remirror-editor]:overflow-y-auto [&_.remirror-editor]:break-words [&_.remirror-editor]:whitespace-break-spaces "
      )}
    >
      <Remirror
        manager={manager}
        initialContent={state}
        autoFocus={autoFocus}
        onChange={handleChange}
      >
        {(!toolbarPosition || toolbarPosition === "top") && <RemirrorToolbar />}
        <EditorComponent />
        <MentionSuggester />
        {toolbarPosition === "bottom" && <RemirrorToolbar />}
      </Remirror>
    </div>
  );
};

// TODO
// To be bind for Error: getRootProps has been attached to the DOM more than once. It should only be attached to the DOM once per editor.
// const EditorBindings = () => {
//   const { getRootProps } = useRemirrorContext();
//   return <div {...getRootProps()} />;
// };

export default Standard;
