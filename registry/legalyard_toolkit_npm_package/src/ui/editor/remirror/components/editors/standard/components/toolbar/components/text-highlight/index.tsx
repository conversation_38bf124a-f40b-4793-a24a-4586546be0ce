import { useCommands } from "@remirror/react";
import { CommandButtonGroup, DropdownButton } from "@remirror/react-ui";
import classNames from "classnames";
import { useRef, useState } from "react";
import { ChromePicker, SwatchesPicker } from "react-color";
import useEditor from "../../../../../../../@context";

const TextHighlighter = () => {
  const inputRef = useRef<HTMLInputElement>(null);

  const { setTextHighlight, removeTextHighlight } = useCommands();
  const [currentTab, setCurrentTab] = useState<number>(1);

  const {
    state: { currentHighlightColor, setCurrentHighlightColor },
  } = useEditor();

  const handleTabChange = (tabIndex: number) => {
    setCurrentTab(tabIndex);

    if (tabIndex === 2) {
      inputRef?.current?.click();
    }
  };

  const handleReset = () => {
    setCurrentHighlightColor("");
    removeTextHighlight();
  };

  const handleChange = (color: string) => {
    setTextHighlight(color);
    setCurrentHighlightColor(color);
  };

  return (
    <CommandButtonGroup
      ref={null}
      sx={{
        ".MuiIconButton-root": {
          color: currentHighlightColor,
        },
      }}
    >
      <DropdownButton
        aria-label="Text Highlight"
        icon="markPenLine"
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        <div className="max-w-max h-full max-h-max">
          <div className="flex items-center gap-0 border-b border-transparent">
            <TabBUtton
              title="Basic"
              active={currentTab === 1}
              onClick={() => handleTabChange(1)}
              className="border-r border-slate-200"
            />
            <TabBUtton
              title="Advance"
              active={currentTab === 2}
              onClick={() => handleTabChange(2)}
            />
          </div>

          <button
            className="w-full p-2 mx-2 my-1 rounded-base text-sb border-t border-slate-200 bg-transparent hover:bg-gray hover:bg-opacity-10"
            onClick={() => handleReset()}
          >
            Reset
          </button>

          {currentTab === 1 && (
            <SwatchesPicker
              color={currentHighlightColor}
              onChange={(clr) => handleChange(clr?.hex)}
            />
          )}

          {currentTab === 2 && (
            <ChromePicker
              color={currentHighlightColor}
              onChange={(clr) => handleChange(clr?.hex)}
            />
          )}
        </div>
      </DropdownButton>
    </CommandButtonGroup>
  );
};

const TabBUtton = ({
  title,
  active,
  className,
  onClick,
}: {
  title: string;
  active: boolean;
  className?: string;
  onClick: () => void;
}) => (
  <button
    className={classNames(
      "flex-1 px-1.5 py-2 text-sb hover:bg-gray hover:bg-opacity-10",
      {
        "font-bold": active,
      },
      className
    )}
    onClick={() => onClick()}
  >
    {title}
  </button>
);

export default TextHighlighter;
