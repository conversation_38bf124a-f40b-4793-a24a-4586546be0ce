import { useActive } from "@remirror/react";
import {
  CommandButtonGroup,
  CreateTableButton,
  DropdownButton,
  IndentationButtonGroup,
  InsertHorizontalRuleButton,
  ListButtonGroup,
  Toolbar as Retoolbar,
  TextAlignmentButtonGroup,
  ToggleBlockquoteButton,
  ToggleBoldButton,
  ToggleItalicButton,
  ToggleUnderlineButton
} from "@remirror/react-ui";
import classNames from "classnames";
import useEditor from "../../../../../@context";
import FontFamilyButtons from "./components/font-family";
import FontSizeButtons from "./components/font-size";
import ImageUploadButtons from "./components/image";
import LineHeightButtonDropdown from "./components/line-height";
import TableOptionsButton from "./components/table";
import TextCaseButton from "./components/text-case";
import TextColorSelector from "./components/text-color";
import TextHighlighter from "./components/text-highlight";

const RemirrorToolbar = () => {
  const { toolbar: { additionalTools, className } = {} } = useEditor();

  const { table } = useActive();

  return (
    <Retoolbar className={classNames(className)}>
      <ToggleBoldButton />
      <ToggleItalicButton />
      <ToggleUnderlineButton />
      <ToggleBlockquoteButton />
      <FontSizeButtons />

      <CommandButtonGroup>
        <DropdownButton
          aria-label="More"
          icon={<span className="text-sm">More</span>}
          sx={{ maxHeight: "300px" }}
        >
          <div className="flex flex-wrap gap-2.5 px-2 max-w-72">
            <TextCaseButton />
            <FontFamilyButtons />
            <TextColorSelector />
            <TextHighlighter />
            <ImageUploadButtons />
            <LineHeightButtonDropdown />
            <ListButtonGroup />
            <TextAlignmentButtonGroup showAll={true} />
            <IndentationButtonGroup />
            <InsertHorizontalRuleButton />
            <CreateTableButton displayShortcut />
            {table() && <TableOptionsButton />}
          </div>
        </DropdownButton>
      </CommandButtonGroup>

      {additionalTools}
    </Retoolbar>
  );
};

export default RemirrorToolbar;
