import classNames from "classnames";
import { FC, HTMLAttributes, ReactNode } from "react";

interface RemirrorToolbarButtonType extends HTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  className?: string;
}

const RemirrorToolbarButton: FC<RemirrorToolbarButtonType> = ({
  children,
  className,
  ...rest
}) => {
  return (
    <button
      className={classNames(
        "inline-flex flex-row items-center justify-center gap-1 relative bg-transparent hover:bg-zinc-100 active:bg-zinc-200 transition-all outline-0 margin-0 select-none font-medium text-sm leading-3 tracking-wide rounded-[4px] border border-zinc-300 text-zinc-500 px-[12px] py-[6px]",
        className
      )}
      {...rest}
    >
      {children}
    </button>
  );
};

export default RemirrorToolbarButton;
