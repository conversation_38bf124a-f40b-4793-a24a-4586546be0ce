import Button from "../../../button";
import InputField from "../../../input/field";
import classNames from "classnames";
import { ChangeEvent, FC, useState } from "react";
import { Trash2 } from "react-feather";

export interface propsType {
  data?: {
    url?: string;
  };
  count?: number;
  validation?: boolean;
  className?: string;
  onDelete?: (d?: any) => void;
  onCancel?: (d?: any) => void;
}

const Delete: FC<propsType> = ({
  count,
  onDelete,
  onCancel,
  className,
  validation,
}) => {
  const [value, setValue] = useState<string>("");
  const [valid, setValid] = useState<boolean>(false);

  const handleDelete = () => {
    try {
      if (onDelete) {
        onDelete();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      const value = e?.target?.value;
      setValue(value);

      const isValid = Number(value) === count;
      setValid(isValid);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div
      aria-label="delete-modal"
      className={classNames(
        "w-full flex flex-col items-start gap-0 rounded-base overflow-hidden",
        className
      )}
    >
      <div
        className={classNames(
          "w-full px-8 py-6 bg-gradient-to-r from-red to-rose-700 text-white"
        )}
      >
        <h3 className="mt-1 teko_font text-5xl leading-tight">
          Delete {count} record{count && count > 1 && "s"}?
        </h3>
      </div>
      <div className="bg-white px-8 py-2 mt-5">
        <h5 className="text-base leading-snug">
          You're about to delete {count} record{count && count > 1 && "s"}.
          Deleted records can't be restored after 90 days.
        </h5>

        {validation && (
          <div className="mt-5">
            <InputField
              type="text"
              name="count"
              label={{ name: "Type the number of records below to delete" }}
              onChange={(e) => handleChange(e)}
              placeholder={String(count) || "Enter Record Count"}
              value={value || ""}
            />
          </div>
        )}

        <div className="mt-8 mb-2 flex items-center justify-start gap-3">
          <Button
            variant="outline"
            className={classNames(
              "flex items-center justify-center gap-x-1",
              validation &&
                valid &&
                "bg-gradient-to-r from-red to-rose-700 !text-white"
            )}
            onClick={() => handleDelete()}
            disabled={validation && !valid}
          >
            <Trash2 /> <span>Delete</span>
          </Button>
          <Button
            variant="ghost"
            className="flex items-center justify-center gap-x-1"
            onClick={() => {
              try {
                if (onCancel) {
                  onCancel();
                }
              } catch (error) {
                console.error(error);
              }
            }}
          >
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Delete;
