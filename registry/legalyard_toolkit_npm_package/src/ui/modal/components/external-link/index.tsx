import { FC, useState } from "react";
import { openExternalUrl } from "../../../../utils/helpers/dom/navigate";
import { ExternalLink as ExternalLinkIcon } from "react-feather";
import useFavicon from "../../../../utils/hooks/useFavicon";
import Image from "../../../image";
import Button from "../../../button";

export interface propsType {
  data: {
    name?: string;
    url?: string;
  };
  onClose?: (d?: any) => void;
}

const ExternalLink: FC<propsType> = ({ data, onClose }) => {
  const [iconError, setIconError] = useState<boolean>(false);

  const handleNavigate = () => {
    if (onClose) onClose();

    if (data?.url) openExternalUrl({ url: data?.url, newTab: true });
  };

  const handleCancel = () => {
    if (onClose) onClose();
  };

  const favicon = useFavicon(data?.url) as string;

  return (
    <div className="w-full h-full max-w-[calc(100vw-2rem)] sm:max-w-96 flex flex-col items-start gap-y-4 p-4">
      <ExternalLinkIcon className="w-14 h-14 text-main" />
      <div className="flex flex-col gap-y-1">
        <h3 className="text-xl">
          You are leaving <b>{window?.location?.hostname ?? "Legalyard.com"}</b>
        </h3>
        <p className="text-sm leading-4 text-gray">
          You are leaving our website and we cannot be held responsible for the
          content of external website
        </p>
      </div>
      <div
        className="flex flex-row items-center gap-x-2 bg-zinc-200 p-1.5 shadow rounded-base w-full overflow-hidden"
        title={data?.url ?? ""}
      >
        <div className="flex items-center justify-center bg-white shadow rounded-full w-8 h-8 p-1">
          {!iconError ? (
            <Image
              src={favicon}
              alt={`favicon - ${data?.url}`}
              onError={() => setIconError(true)}
              className="w-6 h-6"
            />
          ) : (
            <ExternalLinkIcon />
          )}
        </div>
        <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
          <h3 className="w-full text-sm font-bold whitespace-nowrap text-ellipsis overflow-hidden">
            {data?.name ?? "-"}
          </h3>
          <p className="text-xs text-gray leading-3 whitespace-nowrap text-ellipsis overflow-hidden">
            {data?.url}
          </p>
        </div>
      </div>
      <div className="flex items-center gap-x-2 w-full">
        <Button size="full" onClick={() => handleCancel()}>
          Stay Here
        </Button>

        <Button
          size="full"
          className="flex items-center justify-center gap-x-1"
          onClick={() => handleNavigate()}
        >
          Continue <ExternalLinkIcon className="w-3 h-3" />
        </Button>
      </div>
    </div>
  );
};

export default ExternalLink;
