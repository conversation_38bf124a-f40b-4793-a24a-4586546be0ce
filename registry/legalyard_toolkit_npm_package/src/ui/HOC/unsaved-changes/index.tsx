import { useContext, useState, useEffect, FC, ComponentType } from "react";
import { propsType } from "./types";
import DefaultContext from "./context";
import * as Styles from "./styles";

const withUnsavedChanges = <P extends object>(
  WrappedComponent: ComponentType<P>
) => {
  const Handler: FC<P & propsType> = (props) => {
    const { cxt, setCxt } = useContext(props?.Context ?? DefaultContext) as {
      cxt: any;
      setCxt: any;
    };
    const [showWarning, setShowWarning] = useState(false);

    const isChanged = props?.isChanged ?? cxt?.isChanged;

    useEffect(() => {
      const handleBeforeUnload = (e: BeforeUnloadEvent) => {
        try {
          if (isChanged) {
            e.preventDefault();

            const msg = "Changes that you made may not be saved.";
            e.returnValue = msg;

            return msg;
          }
        } catch (err) {
          console.error(err);
        }
      };
      window.addEventListener("beforeunload", handleBeforeUnload);
      window.addEventListener("popstate", handleBeforeUnload);

      return () => {
        window.removeEventListener("beforeunload", handleBeforeUnload);
        window.removeEventListener("popstate", handleBeforeUnload);
      };
    }, [isChanged]);

    const handleUnsaved = () => {
      if (isChanged) {
        setShowWarning(true);
      } else {
        if (props?.onClose) {
          props?.onClose();
        }
      }
    };

    const handleDiscard = () => {
      setShowWarning(false);
      setCxt((prevState: any) => ({
        ...prevState,
        isChanged: false,
      }));
      if (props?.onClose) {
        props?.onClose();
      }
    };

    return (
      <Styles.Container aria-label="Unsaved Changes">
        <WrappedComponent {...props} onClose={handleUnsaved}>
          {showWarning && (
            <Styles.MessageWrapper aria-label="discard-confirmation-modal">
              <Styles.MessageBox>
                <Styles.TextHolder>
                  {props?.warning ?? "Discard unsaved changes?"}
                </Styles.TextHolder>
                <Styles.ButtonWrapper>
                  <Styles.Button onClick={() => setShowWarning(false)}>
                    Cancel
                  </Styles.Button>
                  <Styles.Button onClick={() => handleDiscard()}>
                    Discard
                  </Styles.Button>
                </Styles.ButtonWrapper>
              </Styles.MessageBox>
            </Styles.MessageWrapper>
          )}
          {props?.children}
        </WrappedComponent>
      </Styles.Container>
    );
  };

  return Handler;
};

export default withUnsavedChanges;
