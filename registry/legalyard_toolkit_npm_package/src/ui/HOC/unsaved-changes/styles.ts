import styled from "styled-components";
import { StyledType } from "./types";

export const Container = styled.div`
  position: relative;
`;

export const MessageWrapper = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 1rem;
  backdrop-filter: blur(4px);
  background-color: ${({ theme: { color } }) => `${color.main}30`};

  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 10;

  ${({ theme: { placeholders } }) => placeholders.makeCenter};
`;

export const MessageBox = styled.div`
  border-radius: ${({ theme: { element } }) => element.radius};
  background-color: ${({ theme: { color } }) => color.white};
  padding: 1rem;

  display: flex;
  flex-direction: column;
  row-gap: 1rem;
`;

export const TextHolder = styled.span<StyledType>`
  font-weight: ${({ bold }) => (bold ? 600 : 400)};
`;

export const ButtonWrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  align-self: flex-end;
`;

export const Button = styled.button`
  border-radius: ${({ theme: { element } }) => element.radius};
  border: none;
  outline: none;
  background-color: transparent;
  padding: 0.5rem;
  color: ${({ theme: { color } }) => color.blue};

  &:hover {
    background-color: ${({ theme: { color } }) => `${color.main}10`};
    color: ${({ theme: { color } }) => color.font};
  }
`;
