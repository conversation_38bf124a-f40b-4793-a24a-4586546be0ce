import { HTMLAttributes, ReactNode } from "react";

export interface propsType extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
  duration?: number;
  boolean?: boolean;
  toLeft?: boolean;
  toRight?: boolean;
  toBottom?: boolean;
  toTop?: boolean;
  className?: string;
}

export interface styles {
  $isVisible?: boolean;
  $duration?: number;
  $toLeft?: boolean;
  $toRight?: boolean;
  $toBottom?: boolean;
  $toTop?: boolean;
}

export type StyledType = styles;
