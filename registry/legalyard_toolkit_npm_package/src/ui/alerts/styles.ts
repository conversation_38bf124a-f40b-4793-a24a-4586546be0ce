import styled, { css } from "styled-components";
import { StyledType } from "./types";
import { inputHeight } from "../../styles/constants";

const bgColor = css<StyledType>`
  background-color: ${({ $alertType, theme: { color } }: any) =>
    $alertType.success
      ? `${color.green}50`
      : $alertType.error
      ? `${color.red}50`
      : $alertType.warning
      ? `${color.orange}50`
      : $alertType.info
      ? `${color.blue}50`
      : "transparent"};
`;

const fontColor = css<StyledType>`
  color: ${({ $alertType, theme: { color } }: any) =>
    $alertType.success
      ? color.green
      : $alertType.error
      ? color.red
      : $alertType.warning
      ? color.orange
      : $alertType.info
      ? color.blue
      : color.font};
`;

const fixedPosition = css`
  position: fixed;
`;

const defaultPosition = css`
  position: relative;
`;

const topLeftPosition = css`
  top: 70px;
  left: 0;
`;

const bottomRightPosition = css`
  bottom: 70px;
  right: 0;
`;

const bottomLeftPosition = css`
  bottom: 70px;
  left: 0;
`;

export const Container = styled.div<StyledType>`
  ${({ $fixed }) => ($fixed ? fixedPosition : defaultPosition)};
  ${({ $topRight, $topLeft, $bottomRight, $bottomLeft }) =>
    $topRight
      ? defaultPosition
      : $topLeft
      ? topLeftPosition
      : $bottomRight
      ? bottomRightPosition
      : $bottomLeft && bottomLeftPosition};

  ${({ theme: { placeholders } }) => placeholders.flex};
  ${({ $box, theme: { color } }) => ($box ? color.font : fontColor)};
  min-height: ${inputHeight};

  max-width: ${({ fixed }) => (fixed ? "max-content" : "100%")};
  align-items: ${({ $isTitle }) => ($isTitle ? "flex-start" : "center")};
  background-color: transparent;
  border-radius: ${({ theme: { element } }) => element.radius};
  gap: 12px;
  width: 100%;
  padding: 0.5rem;
  justify-content: space-between;

  z-index: 999;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -99;
    ${({ $box }) => $box && bgColor}
    border-radius: ${({ theme: { element } }) => element.radius};
  }
`;

export const Wrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flex};
  align-items: flex-start;
  gap: 0.5rem;
  flex: 1;
`;

export const IconWrapper = styled.div`
  max-width: 25px;
  opacity: 0.9;

  svg {
    width: 1.2rem;
    height: 1.2rem;
  }
`;

export const TextWrapper = styled.div<StyledType>`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  flex-direction: column;
  align-items: flex-start;
  gap: ${({ $text }) => ($text ? "5px" : 0)};
`;

export const TextHolder = styled.div<StyledType>`
  line-height: ${({ $bold }) => ($bold ? 1.5 : 1)};
  font-weight: ${({ $bold }) => ($bold ? 600 : 400)};
  font-size: ${({ $bold, theme: { font } }) => ($bold ? font.sub : font.small)};
  line-height: 1.3;
`;

export const CloseWrapper = styled.div`
  cursor: pointer;
`;
