export interface LoaderType extends TableLoaderType {
  id?: string;
  color?: string;
  variant?: "spin" | "skeleton" | "table";
  small?: boolean;
  big?: boolean;
  box?: boolean;
  screen?: boolean;
  center?: boolean;
  fullPage?: boolean;
  wrapperStyle?: {
    [key: string]: string | number;
  };
  containerStyle?: {
    [key: string]: string | number;
  };
  style?: {
    [key: string]: string | number;
  };
  count?: string | number;
  height?: string | number;
  width?: string | number;
  right?: boolean;
  dark?: boolean;
  light?: boolean;
  className?: string;
}

export interface TableLoaderType {
  column?: number;
  row?: number;
  className?: string;
}
