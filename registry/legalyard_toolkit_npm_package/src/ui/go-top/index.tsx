import { useEffect, useState } from "react";
import { ArrowUp } from "react-feather";
import * as Styles from "./styles";

const GoTop = () => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      try {
        const scrollPosition = window.scrollY;
        if (scrollPosition > 500) {
          setShow(true);
        } else {
          setShow(false);
        }
      } catch (error) {
        console.error(error);
      }
    };

    if (window) {
      window.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (window) {
        window.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);

  const handleTop = () => {
    if (window) {
      window.scrollTo({ top: 0, left: 0, behavior: "smooth" });
    }
  };

  if (!show) {
    return null;
  }

  return (
    <Styles.Container>
      <Styles.Button onClick={() => handleTop()}>
        <ArrowUp /> top
      </Styles.Button>
    </Styles.Container>
  );
};

export default GoTop;
