import { validArray } from "../../../../../utils/helpers/data/array";
import { Dispatch, SetStateAction, UIEvent, useCallback } from "react";
import { SelectedTileType, TileViewType } from "../types";

interface HelperType extends TileViewType {
  searchValue: string;
  setSelected: Dispatch<SetStateAction<SelectedTileType>>;
}

export const useTilesHelper = ({
  searchValue,
  setSelected,
  tiles,
}: HelperType): HelperExportType => {
  const handleScroll = useCallback((e: UIEvent<HTMLDivElement>) => {
    try {
      // const currentTarget = e?.currentTarget;
    } catch (error) {
      console.error(error);
    }
  }, []);

  const handleSearchTable = useCallback(
    (searched: string): any[] => {
      try {
        if (validArray(tiles?.data) && Boolean(searched)) {
          return (
            tiles?.data?.filter((item) => {
              const valueSet = Object.values(item);
              for (const value of valueSet) {
                if (
                  value
                    ?.toString()
                    ?.toLowerCase()
                    ?.includes(searched?.toString()?.toLowerCase())
                ) {
                  return true;
                }
              }
              return false;
            }) ?? []
          );
        } else {
          return [];
        }
      } catch (error) {
        console.error(error);
        return [];
      }
    },
    [tiles?.data]
  );

  const filteredData = useCallback((): any[] => {
    try {
      return (
        (Boolean(searchValue)
          ? handleSearchTable(searchValue ?? "")
          : tiles?.data) ?? []
      );
    } catch (error) {
      console.error(error);
      return [];
    }
  }, [handleSearchTable, searchValue, tiles]);

  const getSelectRows = useCallback(
    (ids: SelectedTileType, rowData?: any[]): any[] => {
      try {
        const idsSet = new Set(ids);
        const currentRowData = validArray(rowData) ? rowData : [];
        return currentRowData?.filter((item) => idsSet.has(item?.rowId));
      } catch (error) {
        console.error(error);
        return [];
      }
    },
    []
  );
  const removeSelect = useCallback(() => {
    try {
      setSelected([]);
    } catch (error) {
      console.error(error);
    }
  }, [setSelected]);

  return {
    handleScroll,
    getSelectRows,
    removeSelect,
    handleSearchTable,
    filteredData,
  };
};

export interface HelperExportType {
  handleScroll: (e: UIEvent<HTMLDivElement>) => void;
  getSelectRows: (ids: SelectedTileType, rowData?: any[]) => any[];
  removeSelect: () => void;
  handleSearchTable: (s: string) => any[];
  filteredData: () => any[];
}
