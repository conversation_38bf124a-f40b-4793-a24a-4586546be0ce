import TableLoader from "../../../../../loader";
import classNames from "classnames";
import useTiles from "../../@context";
import Operation from "./operation";
import TilesSearch from "./search";
import Showcase from "./showcase";

const Header = () => {
  const { loading = false, header } = useTiles();

  if (!Boolean(header)) {
    return null;
  }

  if (loading) {
    return <TableLoader variant="table" column={2} row={1} />;
  }

  if (header?.component) {
    return <>{header?.component}</>;
  }

  return (
    <div
      className={classNames(
        "relative bg-slate-100 border-b-2 border-solid border-slate-200 px-3 py-1",
        header?.className
      )}
    >
      <div className="w-full h-full flex items-center justify-between gap-2">
        <div className="flex-1 h-full flex items-center gap-2">
          {header?.search?.show && (
            <div className="flex-1 max-w-80">
              <TilesSearch />
            </div>
          )}
          {Boolean(header?.showcase) && <Showcase />}
        </div>
      </div>

      <Operation />
    </div>
  );
};

export default Header;
