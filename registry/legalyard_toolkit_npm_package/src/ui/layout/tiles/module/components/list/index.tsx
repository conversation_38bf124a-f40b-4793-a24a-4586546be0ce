import { Fragment } from "react";
import useTiles from "../../@context";

const ListBody = ({ data }: { data?: any[] }) => {
  const { listCard } = useTiles();

  return (
    <div className="relative flex flex-col bg-clip-border rounded-base bg-transparent shadow-md w-full p-2">
      <nav className="flex flex-col gap-4 min-w-[20vw] w-full">
        {data?.map((dt: any, index: number) => {
          return (
            <Fragment key={String(index) + dt?._id ? String(dt?._id) : ""}>
              {listCard ? listCard({ card: dt }) : null}
            </Fragment>
          );
        })}
      </nav>
    </div>
  );
};

export default ListBody;
