import { ChangeEvent, ReactNode } from "react";

export type SelectedTileType = (string | number)[];

export interface ShowcaseType {
  show: boolean;
  component?: ReactNode;
  dataCount?: number;
}

export interface OperationType {
  show: boolean;
  component?: ReactNode;
  onEdit?: (ids?: SelectedTileType, rows?: any[]) => void;
  onDelete?: (ids?: SelectedTileType, rows?: any[]) => void;
  deleteComplete?: boolean;
  additional?: {
    title?: ReactNode;
    event?: () => void;
  }[];
}

export interface HeaderType {
  show: boolean;
  className?: string;
  component?: ReactNode;
  showcase?: ShowcaseType;
  operation?: OperationType;
  search?: {
    show: boolean;
    loading?: boolean;
    onChange?: (d?: ChangeEvent<HTMLInputElement>) => void;
    onRemove?: (d?: any) => void;
    getFiltered?: (count?: number, d?: any[]) => void;
  };
  pagination?: {
    show: boolean;
    loading?: boolean;
    onPageChange?: (d?: number) => void;
    onRowLimitChange?: (d?: number) => void;
    onPrev?: (d?: number) => void;
    onNext?: (d?: number) => void;
  };
}

export interface ErrorType {
  component: ReactNode;
  message?: string;
}

export interface TilesType {
  data?: any[];
  loading?: boolean;
}

export interface TileViewType {
  loading?: boolean;
  className?: string;
  wrapperClassName?: string;
  header?: HeaderType;
  operation?: OperationType;
  tiles?: TilesType;
  error?: ErrorType;
  tileCard?: (data?: any) => ReactNode;
  listCard?: (data?: any) => ReactNode;
  view?: "list" | "grid";
}
