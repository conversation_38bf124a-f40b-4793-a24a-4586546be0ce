import { createElement } from "react";
import { ArrowDown, ArrowUp, ChevronsLeft, X } from "react-feather";

export const sortMenuData = (
  active: string,
  setActive: (d: string) => void,
  handlePin: (type?: string, d?: string) => void,
  pinned: boolean,
  handleClose: () => void
) => {
  const ids = {
    asc: "asc",
    desc: "desc",
    unsort: "unsort",
    pin: "pin",
  };

  return [
    {
      menu: [
        {
          title: active === ids?.asc ? "Unsort" : "Asc",
          icon: createElement(active === ids?.asc ? X : ArrowUp),
          event: () => {
            setActive(active === ids?.asc ? ids?.unsort : ids?.asc);
            handleClose();
          },
          active: active === ids?.asc,
        },
        {
          title: active === ids?.desc ? "Unsort" : "Desc",
          icon: createElement(active === ids?.desc ? X : ArrowDown),
          event: () => {
            setActive(active === ids?.desc ? ids?.unsort : ids?.desc);
            handleClose();
          },
          active: active === ids?.desc,
        },
        {
          title: pinned ? "unpin" : "Pin",
          icon: createElement(pinned ? X : ChevronsLeft),
          event: () => {
            handlePin();
            handleClose();
          },
          active: pinned,
        },
      ],
    },
  ];
};
