import Dropdown, { useDropdown } from "../../../../../../../../dropdown";
import DropdownMenu from "../../../../../../../../dropdown/menu";
import classNames from "classnames";
import { FC, useState } from "react";
import { MoreVertical } from "react-feather";
import { sortMenuData } from "./data";

interface SortMenuType {
  active: string;
  handleActive: (d?: any) => void;
  handlePin: (type?: string, d?: string) => void;
  pinned: boolean;
}

const SortMenu: FC<SortMenuType> = ({ ...rest }) => {
  const [open, setOpen] = useState<boolean>(false);
  return (
    <div className="relative normal-case z-20">
      <Dropdown
        inlineAlign="left"
        content={<DropContent {...rest} />}
        onToggle={(val: boolean) => setOpen(val)}
      >
        <button
          className={classNames(
            "border-0 outline-none rounded-full p-1 hover:bg-white",
            open && "!bg-white"
          )}
        >
          <MoreVertical />
        </button>
      </Dropdown>
    </div>
  );
};

const DropContent: FC<SortMenuType> = ({
  active,
  handleActive,
  pinned,
  handlePin,
}) => {
  const { handleClose } = useDropdown();

  return (
    <DropdownMenu
      data={sortMenuData(active, handleActive, handlePin, pinned, handleClose)}
      className={classNames("h-full !w-28 font-[400] z-20")}
    />
  );
};

export default SortMenu;
