import InputField from "../../../../../../../input/field";
import useDebounce from "../../../../../../../../utils/hooks/debounce";
import { ChangeEvent, useRef } from "react";
import useTable from "../../../@context";
import { HeaderType } from "../../../types";

const TableSearch = () => {
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    state: { searchValue, setSearchValue, setCurrentPage },
    header,
  } = useTable();

  const {
    search: { onChange, onRemove } = {},
    pagination: { onPageChange } = {},
  } = header as HeaderType;

  const resetListPage = useDebounce(() => {
    try {
      setCurrentPage(1);
      if (onPageChange) {
        onPageChange(1);
      }
    } catch (error) {
      console.error(error);
    }
  }, 300);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      setSearchValue(e?.target?.value);

      if (onChange) {
        onChange(e);
      }

      resetListPage();
    } catch (error) {
      console.error(error);
    }
  };

  const handleRemoveValue = () => {
    try {
      setSearchValue("");

      if (onRemove) {
        onRemove();
      }
      if (inputRef.current) {
        inputRef.current.focus();
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <InputField
      ref={inputRef}
      type="search"
      name="search"
      placeholder="Search name, phone, email, address or company"
      onChange={(e) => handleChange(e)}
      value={searchValue || ""}
      attributes={{
        prefix: {
          icon: {
            show: true,
          },
        },
        suffix: {
          icon: {
            show: true,
            tooltip: {
              show: Boolean(searchValue),
              name: "clear",
            },
            onClick: () => handleRemoveValue(),
          },
        },
      }}
      autoComplete="off"
    />
  );
};

export default TableSearch;
