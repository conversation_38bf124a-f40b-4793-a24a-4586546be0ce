import CheckboxInput from "../../../../../../../input/checkbox/single";
import classNames from "classnames";
import { ChangeEvent, ElementType } from "react";
import useTable from "../../../@context";
import Styles from "../../../styles.module.css";

const RenderCheckbox = ({
  elementType,
  header,
  row,
  onChange,
  isChecked,
}: {
  elementType?: ElementType;
  header?: boolean;
  isChecked?: boolean;
  row?: Record<string, any>;
  onChange: (d?: any) => void;
}) => {
  const {
    variables,
    helper: { getPinnedWidth },
    state: { tableScrollLeft },
    action,
    checkbox,
  } = useTable();

  const Comp = elementType ?? "td";
  const actionWidth = action?.colWidth ?? variables.action.width;
  const finalWidth = checkbox?.colWidth ?? variables.checkbox.width;
  const totalWidth = Number(action?.pin ? actionWidth : 0) + finalWidth;

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      if (onChange) {
        onChange(e);
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Comp
      {...(checkbox?.thProps ?? {})}
      className={classNames(
        "table-data-checkbox-column bg-white shadow text-center z-[5] group-hover:bg-slate-100",
        header && "!bg-slate-100",
        checkbox?.pin && "sticky z-[7]",
        checkbox?.className
      )}
      style={{
        width: finalWidth,
        ...(checkbox?.pin && {
          left: action?.pin ? `${actionWidth}px` : 0,
        }),
      }}
    >
      <div className="flex items-center justify-center">
        <CheckboxInput
          name="table_row_check"
          onChange={(e) => handleChange(e)}
          checked={isChecked}
          boolean
        />
      </div>

      {checkbox?.pin && getPinnedWidth() === totalWidth && (
        <div
          className={classNames(
            Styles.table_data_column_side_show,
            tableScrollLeft > 0 && "!visible"
          )}
        />
      )}
    </Comp>
  );
};
export default RenderCheckbox;
