import TableLoader from "../../../../../../loader";
import { validArray } from "../../../../../../../utils/helpers/data/array";
import classNames from "classnames";
import useTable from "../../@context";
import Operation from "./operation";
import Pagination from "./pagination";
import TableSearch from "./search";
import Showcase from "./showcase";

const Header = () => {
  const {
    loading = false,
    state: { localLoading },
    header,
    rows,
  } = useTable();

  if (!Boolean(header)) {
    return null;
  }

  if (loading || localLoading || header?.loading) {
    return <TableLoader variant="table" column={2} row={1} />;
  }

  if (header?.component) {
    return <>{header?.component}</>;
  }

  return (
    <div
      className={classNames(
        "relative bg-slate-100 border-b-2 border-solid border-slate-200 px-3 py-1",
        header?.className
      )}
    >
      <div className="w-full h-full flex items-center justify-between gap-2">
        <div className="flex-1 h-full flex items-center gap-2">
          {header?.search?.show && (
            <div className="flex-1 max-w-80">
              <TableSearch />
            </div>
          )}
          {Boolean(header?.showcase) && <Showcase />}
        </div>

        {validArray(rows?.data) && (
          <>
            {header?.pagination?.show && (
              <div className="max-w-max ms-auto justify-end">
                <Pagination />
              </div>
            )}
          </>
        )}
      </div>

      <Operation />
    </div>
  );
};

export default Header;
