import TableLoader from "../../../../loader";
import { validArray } from "../../../../../utils/helpers/data/array";
import classNames from "classnames";
import React, { FC, useRef } from "react";
import { ErrorBoundary } from "react-error-boundary";
import useTable from "./@context";
import TableProvider from "./@context/provider";
import TableColumns from "./components/body/column";
import TableRows from "./components/body/row";
import TableNotFoundError from "./components/error";
import TableNoDataError from "./components/error/no-data";
import Header from "./components/header";
import { TableType } from "./types";

// TODO
// handleScroll optimization
// onScroll pagination
// Loading issue

const DataTable: FC<TableType> = React.memo(
  ({ className, wrapperClassName }) => {
    const tableWrapper = useRef<HTMLDivElement>(null);

    const {
      helper: { handleScroll, handlePageData },
      state: { searchValue, localLoading },
      rows: { loading: RowLoading = false, data: RowData = [] } = {},
      loading,
    } = useTable();

    const isLoading = loading || localLoading || RowLoading;
    const finalRowData = handlePageData();

    return (
      <div
        className={classNames(
          "ui-table-bigdata bg-slate-200 rounded-base p-1 h-full w-full max-w-full max-h-full",
          className
        )}
      >
        <Header />
        <div
          ref={tableWrapper}
          className={classNames(
            "w-full h-full max-w-full max-h-full overflow-auto",
            wrapperClassName
          )}
          onScroll={(e) => handleScroll(e)}
        >
          {isLoading ? (
            <TableLoader variant="table" column={4} row={4} />
          ) : validArray(RowData) ? (
            <>
              {validArray(finalRowData) ? (
                <table
                  className={classNames(
                    "table-data text-sb bg-white border-0 table-fixed w-full border-collapse border-slate-400 border-solid mt-0 mb-0"
                  )}
                >
                  <thead
                    className={classNames(
                      "z-10 sticky top-0 cursor-default",
                      "[&>tr>th]:h-10 [&>tr>th]:leading-normal [&>tr>th]:border-b [&>tr>th]:border-solid [&>tr>th]:border-slate-100 [&>tr>th]:sticky [&>tr>th]:top-0 shadow-md"
                    )}
                  >
                    <TableColumns />
                  </thead>
                  <tbody>
                    <TableRows rows={finalRowData} />
                  </tbody>
                </table>
              ) : (
                searchValue && <TableNotFoundError />
              )}
            </>
          ) : (
            <TableNoDataError />
          )}
        </div>
      </div>
    );
  }
);

const BigDataTable: FC<TableType> = React.memo(({ ...rest }) => {
  return (
    <ErrorBoundary fallback={<div>Something went wrong with table</div>}>
      <TableProvider {...rest}>
        <DataTable {...rest} />
      </TableProvider>
    </ErrorBoundary>
  );
});

export default BigDataTable;
