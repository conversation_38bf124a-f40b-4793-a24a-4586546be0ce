import classNames from "classnames";
import { ElementType } from "react";
import useTable from "../../../@context";
import Styles from "../../../styles.module.css";

const RenderActions = ({
  elementType,
  header,
  row,
}: {
  elementType?: ElementType;
  header?: boolean;
  row?: Record<string, any>;
}) => {
  const {
    variables,
    helper: { getPinnedWidth },
    state: { tableScrollLeft },
    action,
  } = useTable();

  const Comp = elementType ?? "td";

  const ReturnComp = () => (
    <Comp
      {...(action?.thProps ?? {})}
      className={classNames(
        "table-data-action-column relative text-center group-hover:bg-slate-100",
        header && "!bg-slate-100",
        action?.pin && "sticky left-0 z-[7] bg-white",
        action?.className
      )}
      style={{ width: action?.colWidth ?? variables.action.width }}
    >
      <div
        className={classNames(
          "flex items-center justify-center max-w-max max-h-max",
          action?.onRowHover && "invisible group-hover:visible"
        )}
      >
        {action?.component ? action?.component({ row }) : null}
      </div>

      {action?.pin && getPinnedWidth() === finalWidth && (
        <div
          className={classNames(
            Styles.table_data_column_side_show,
            tableScrollLeft > 0 && "!visible"
          )}
        />
      )}
    </Comp>
  );

  const finalWidth = action?.colWidth ?? variables.action.width;

  return (
    <>
      {header ? (
        <>
          {action?.inHeader ? (
            <ReturnComp />
          ) : (
            <th
              style={{ width: finalWidth }}
              className={classNames(
                "bg-slate-100",
                action?.pin && "sticky left-0 z-[7]"
              )}
            >
              {action?.pin && getPinnedWidth() === finalWidth && (
                <div
                  className={classNames(
                    Styles.table_data_column_side_show,
                    tableScrollLeft > 0 && "!visible"
                  )}
                />
              )}
            </th>
          )}
        </>
      ) : (
        <ReturnComp />
      )}
    </>
  );
};

export default RenderActions;
