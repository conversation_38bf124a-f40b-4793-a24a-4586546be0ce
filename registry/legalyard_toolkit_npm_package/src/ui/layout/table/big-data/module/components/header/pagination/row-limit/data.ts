export const rowLimitData = (
  active: number,
  setActive: (d?: any) => void,
  handleClose: () => void
) => {
  const ids: number[] = [10, 20, 50, 100];

  return [
    {
      menu: ids?.map((item: number) => {
        return {
          title: `${item} per page`,
          event: () => {
            setActive(item);
            handleClose();
          },
          active: active === item,
        };
      }),
    },
  ];
};
