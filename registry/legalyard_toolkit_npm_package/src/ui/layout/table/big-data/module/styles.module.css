.table_data_column_side_border {
  @apply absolute right-0 top-0 w-3 h-full bg-transparent;
}

.table_data_column_side_border::after {
  content: "";
  height: 67%;

  @apply absolute bg-slate-300 w-[1px] top-1/2 -translate-y-1/2 -right-[0.01rem] opacity-100 transition-all;
}

.table_data_column_resizer {
  @apply absolute right-0 top-0 w-3 h-full bg-transparent hover:cursor-ew-resize;
}

.table_data_column_resizer::after {
  content: "";
  height: 67%;

  @apply absolute bg-slate-300 w-[1px] top-1/2 -translate-y-1/2 -right-[0.01rem] opacity-100 transition-all;
}

@media (max-width: 640px) {
  .table_data_column_resizer::after {
    @apply absolute -right-[0.01rem] transition-all opacity-100 h-full top-0 w-1 translate-y-0 bg-main;
  }
}

.table_data_column_resizer::after:hover {
  @apply opacity-100 h-full top-0 w-1 translate-y-0 bg-main;
}

.table_data_column_side_show {
  @apply absolute top-0 left-full bottom-0 w-[10px] h-full bg-gradient-to-r from-[rgba(0,0,0,0.1)] to-[rgba(0,0,0,0)] pointer-events-none invisible;
}
