import Dropdown, { useDropdown } from "../../../../../../../../dropdown";
import DropdownMenu from "../../../../../../../../dropdown/menu";
import classNames from "classnames";
import { useState } from "react";
import { ChevronDown, ChevronUp } from "react-feather";
import useTable from "../../../../@context";
import { rowLimitData } from "./data";
import { HeaderType } from "../../../../types";

const RowLimit = () => {
  const [open, setOpen] = useState<boolean>(false);
  const {
    state: { itemsPerPage, setItemsPerPage, currentPage, setCurrentPage },
    rows,
    header,
  } = useTable();

  const { pagination: { onRowLimitChange, onPageChange } = {} } =
    header as HeaderType;

  const handleChange = (count: number) => {
    try {
      setItemsPerPage(count);

      if (onRowLimitChange) {
        onRowLimitChange(count);
      }

      if ((rows?.data?.length ?? 0) < count * currentPage) {
        setCurrentPage(1);
        if (onPageChange) {
          onPageChange(1);
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="relative normal-case z-20">
      <Dropdown
        inlineAlign="left"
        content={<DropContent active={itemsPerPage} setActive={handleChange} />}
        onToggle={(val: boolean) => setOpen(val)}
      >
        <button
          className={classNames(
            "group border border-slate-400 outline-none rounded-base p-1 bg-slate-50 hover:bg-white hover:border-main pl-2 pr-1",
            open && "!bg-white !border-main"
          )}
        >
          <div className="flex items-center gap-0.5">
            <span className="text-sm font-medium">{itemsPerPage} per page</span>
            <span
              className={classNames(
                "group-hover:text-main",
                open && "text-main"
              )}
            >
              {open ? <ChevronUp /> : <ChevronDown />}
            </span>
          </div>
        </button>
      </Dropdown>
    </div>
  );
};

const DropContent = ({
  active,
  setActive,
}: {
  active: number;
  setActive: (d?: any) => void;
}) => {
  const { handleClose } = useDropdown();

  return (
    <DropdownMenu
      data={rowLimitData(active, setActive, handleClose)}
      className={classNames("h-full !w-32 font-[400] z-20")}
    />
  );
};

export default RowLimit;
