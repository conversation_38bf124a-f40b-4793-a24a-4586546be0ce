import Placeholder from "../../../../../../../../../assets/placeholder/image.png";
import classNames from "classnames";
import { createElement, isValidElement, ReactNode } from "react";

const DataIcon = ({
  title,
  icon,
  className,
}: {
  title?: string;
  icon: string | ReactNode;
  className?: string;
}) => {
  const renderIcon = (icon: any) => {
    try {
      const IconComponent = icon;
      return createElement(IconComponent, null);
    } catch (error) {
      return "";
    }
  };

  if (!icon) return null;

  return (
    <>
      {typeof icon === "string" ? (
        <img
          src={icon ?? Placeholder}
          alt={title ?? "option-icon"}
          className={classNames(
            "w-full max-w-8 h-auto rounded-full aspect-square bg-slate-100",
            className
          )}
          onError={(e) => {
            try {
              const imgElement = e.target as HTMLImageElement;
              imgElement.src = Placeholder;
            } catch (error) {
              console.error(error);
            }
          }}
        />
      ) : isValidElement(icon) ? (
        <>{icon}</>
      ) : (
        typeof icon === "object" && <>{renderIcon(icon as ReactNode)}</>
      )}
    </>
  );
};

export default DataIcon;
