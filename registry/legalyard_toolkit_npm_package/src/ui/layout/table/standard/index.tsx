import { validArray } from "../../../../utils/helpers/data/array";
import React, { FC } from "react";

interface propsType {
  data: any[];
}

const StandardTable: FC<propsType> = ({ data }) => {
  const tableData = data ?? [];

  return (
    <div>
      <table className="w-full text-start mt-10">
        <colgroup>
          <col span={1} style={{ width: "20%" }} />
          <col span={1} style={{ width: "20%" }} />
          <col span={1} style={{ width: "20%" }} />
          <col span={1} style={{ width: "20%" }} />
          <col span={1} style={{ width: "10%" }} />
          <col span={1} style={{ width: "10%" }} />
        </colgroup>
        <thead className="border-b border-slate-300 bg-slate-500 text-white font-semibold text-start">
          <tr>
            <th>Title 1</th>
            <th>Title 1</th>
            <th>Title 1</th>
            <th>Title 1</th>
            <th>Title 1</th>
            <th>Title 1</th>
          </tr>
        </thead>
        <tbody>
          {validArray(tableData) ? (
            tableData?.map((item, index) => {
              return (
                <tr key={index}>
                  <td>Data</td>
                  <td>Data</td>
                  <td>Data</td>
                  <td>Data</td>
                  <td>Data</td>
                  <td>Data</td>
                </tr>
              );
            })
          ) : (
            <tr>
              <td colSpan={6}>
                <div className="flex items-center justify-center p-4">
                  <span>No Schedule Added</span>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default StandardTable;
