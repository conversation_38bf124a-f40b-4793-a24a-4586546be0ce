import { lazy } from "react";
import Image from "../../../../../image";
import Placeholder from "../../../../../../assets/placeholder/1.png";
import classNames from "classnames";

const IntroBoardCardList = lazy(() => import("./list"));

const IntroBoardCard = ({ imageFirst }: { imageFirst: boolean }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-regular">
      <Image
        className={classNames(
          "bg-gray h-full object-cover rounded-base shadow-md",
          imageFirst && "order-1"
        )}
        src={Placeholder}
      />
      <div className="flex flex-col gap-y-6 px-8 py-3">
        <div className="flex flex-col gap-y-2">
          <h1 className="teko_font text-5xl">
            Empower institution with Legalyard
          </h1>
          <p className="text-sm leading-4 text-gray">
            Your Workflow takes control of your task, Boost productivity, and
            Achieve success with Our compressive and Institute Document
            Management Solution
          </p>
        </div>
        <IntroBoardCardList />
      </div>
    </div>
  );
};

export default IntroBoardCard;
