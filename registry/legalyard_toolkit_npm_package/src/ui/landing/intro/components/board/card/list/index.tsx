import { lazy } from "react";

const IntroBoardCardListItem = lazy(() => import("./item"));

const IntroBoardCardList = () => {
  return (
    <div className="flex flex-col items-start gap-regular">
      {[...Array(2).keys()].map((item: any, index: number) => {
        return <IntroBoardCardListItem key={item?._id ?? index} item={item} />;
      })}
    </div>
  );
};

export default IntroBoardCardList;
