import Button from "../../../../button";
import Tag from "../../../../tags";
import { FC } from "react";
import { propsType } from "../../types";
import classNames from "classnames";

const IntroTitle: FC<propsType> = ({
  onGetStarted,
  getStartedContent,
  getStartedClassName,
}) => {
  const handleGetSTarted = () => {
    if (onGetStarted) {
      onGetStarted();
    }
  };

  return (
    <section
      aria-label="title"
      className="bg-sub py-5 md:py-8 px-5 text-center text-white rounded-base mb-5"
    >
      <Tag title="STORAGE" className="font-bold w-full mx-auto mb-4" />
      <h3 className="teko_font capitalize text-xl md:text-5xl font-bold !leading-[1.15] max-w-full md:max-w-[75%] lg:max-w-[60%] mx-auto mb-4">
        Your All-in-one <span className="text-orange">Document</span>
        <br />
        Management Solution
      </h3>
      <p className="leading-normal mb-5 max-w-full md:max-w-[80%] lg:max-w-[60%] mx-auto">
        Your Workflow takes control of your task, Boost productivity, and
        Achieve success with Our compressive and Institute Document Management
        Solution
      </p>
      <div className="relative">
        <Button
          className="!pr-6 fold fold-size-xs fold-bg-white hover:fold-none"
          onClick={handleGetSTarted}
        >
          Get Started now
        </Button>
        <div
          className={classNames(
            "absolute left-[50%] top-[50%] z-50 -translate-x-1/2 -translate-y-1/2 backdrop-blur-md",
            getStartedClassName
          )}
        >
          {getStartedContent}
        </div>
      </div>
    </section>
  );
};

export default IntroTitle;
