import { FC } from "react";
import { propsType } from "../../types";

const IntroVideo: FC<propsType> = () => {
  return (
    <section
      aria-label="how-it-works"
      className="grid grid-cols-1 md:grid-cols-[2fr,1fr] gap-regular mb-5"
    >
      <div aria-label="video" className="box fold hover:fold-none">
        <h3 className="teko_font text-3xl font-normal mb-2">How it works</h3>
        <iframe
          width="100%"
          height="315"
          src="https://www.youtube.com/embed/iJhuXzqvzGU?si=DsZaGqGzvC_0Ebaz"
          title="YouTube video player"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerPolicy="strict-origin-when-cross-origin"
          allowFullScreen
        ></iframe>
      </div>

      <div aria-label="video" className="box">
        <h3 className="teko_font text-3xl font-normal mb-2">Reference</h3>
        <iframe
          width="100%"
          height="315"
          src="https://www.youtube.com/embed/iJhuXzqvzGU?si=DsZaGqGzvC_0Ebaz"
          title="YouTube video player"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerPolicy="strict-origin-when-cross-origin"
          allowFullScreen
        ></iframe>
      </div>
    </section>
  );
};

export default IntroVideo;
