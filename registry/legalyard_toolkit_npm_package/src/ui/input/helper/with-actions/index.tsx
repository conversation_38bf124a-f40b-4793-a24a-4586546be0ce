import classNames from "classnames";
import { ElementType, FC, isValidElement, ReactNode } from "react";
import { ExternalLink, RefreshCw } from "react-feather";
import Button from "../../../button";

interface propsType {
  children: ReactNode;
  action?: {
    hide?: boolean;
    title?: string;
    icon?: ElementType | ReactNode;
    onClick?: (d?: any) => void;
  };
  refresh?: {
    hide?: boolean;
    loading?: boolean;
    title?: string;
    icon?: ElementType | ReactNode;
    onRefresh?: (d?: any) => void;
  };
  className?: string;
}

const WithActions: FC<propsType> = ({
  children,
  refresh,
  action,
  className,
}) => {
  const handleRefresh = () => {
    try {
      if (refresh?.onRefresh) {
        refresh?.onRefresh();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleExternal = () => {
    try {
      if (action?.onClick) {
        action?.onClick();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const renderIcon = (comp: ElementType | ReactNode, props?: any) => {
    try {
      if (typeof comp === "string") {
        return <span>{comp}</span>;
      } else if (isValidElement(comp)) {
        return comp;
      } else if (typeof comp === "function") {
        const FeatherIcon = comp;
        return <FeatherIcon />;
      }
    } catch (error) {
      return null;
    }
  };

  return (
    <div className={classNames("flex flex-col gap-y-3", className)}>
      {children}

      {(Boolean(refresh) || Boolean(action)) && (
        <div className="flex items-center justify-end max-w-max ml-auto gap-1">
          {Boolean(refresh) && !Boolean(refresh?.hide) && (
            <>
              <Button
                effect
                variant="action"
                className="flex items-center gap-x-1 text-sm "
                title="refresh"
                onClick={() => handleRefresh()}
              >
                {Boolean(refresh?.icon) ? (
                  renderIcon(refresh?.icon, { className: "w-3 h-3" })
                ) : (
                  <RefreshCw
                    className={classNames(
                      "w-3 h-3",
                      refresh?.loading && "animate-spin"
                    )}
                  />
                )}
                <span>{refresh?.title}</span>
              </Button>

              <span className="border-r border-slate-600 h-5" />
            </>
          )}

          {Boolean(action) && !Boolean(action?.hide) && (
            <Button
              effect
              variant="action"
              className="flex items-center gap-x-1 text-sm !bg-slate-50 hover:!bg-slate-200"
              onClick={() => handleExternal()}
            >
              {Boolean(action?.icon) ? (
                renderIcon(action?.icon, { className: "w-3 h-3" })
              ) : (
                <ExternalLink className="w-3 h-3" />
              )}

              <span>{action?.title}</span>
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default WithActions;
