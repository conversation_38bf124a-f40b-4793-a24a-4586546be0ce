import classNames from "classnames";
import { forwardRef, ForwardRefRenderFunction, Ref, useState } from "react";
import { ChevronDown, ChevronUp } from "react-feather";
import MiniCollapse from "../../../collapse/mini";
import Textarea from "../../textarea";
import { TextareaTypes } from "../../textarea/types";

const AddDescription: ForwardRefRenderFunction<
  HTMLTextAreaElement,
  TextareaTypes
> = ({ ...rest }, ref: Ref<HTMLTextAreaElement>) => {
  const [open, setOpen] = useState<boolean>(false);

  const handleToggle = () => {
    setOpen((prev) => !prev);
  };

  return (
    <div className={classNames("flex flex-col")}>
      <div
        className={classNames(
          "w-full text-sm flex items-center justify-between gap-x-4 p-1 cursor-pointer rounded-base hover:bg-slate-100 bg-opacity-10"
        )}
        onClick={() => handleToggle()}
      >
        <span>Add Description</span>
        {open ? <ChevronUp /> : <ChevronDown />}
      </div>

      <MiniCollapse open={open} className="pt-1">
        <Textarea ref={ref} {...rest} />
      </MiniCollapse>
    </div>
  );
};

export default forwardRef(AddDescription);
