import { FC, WheelEvent } from "react";
import * as validator from "../../../utils/validator";
import InputField from "../field";
import { InputFieldTypes } from "../field/types";

const MobileInput: FC<InputFieldTypes> = ({ name = "mobile", ...rest }) => {
  return (
    <InputField
      {...rest}
      type="text"
      name={name}
      maxLength={10}
      onKeyDown={(e: any) => validator.handleNumberException(e)}
      onWheel={(e: WheelEvent<HTMLDivElement>) =>
        (e.target as HTMLDivElement).blur()
      }
    />
  );
};

export default MobileInput;
