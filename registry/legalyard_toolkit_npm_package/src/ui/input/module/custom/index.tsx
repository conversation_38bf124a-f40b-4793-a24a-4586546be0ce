import { forwardRef, ForwardRefRenderFunction, Ref } from "react";
import InputField from "../../field";
import { CustomFieldTypes } from "./types";
import classNames from "classnames";

const CustomInput: ForwardRefRenderFunction<
  HTMLInputElement,
  CustomFieldTypes
> = ({ className, labelInput = {}, ...rest }, ref: Ref<HTMLInputElement>) => {
  const { hide = false, ...restLabel } = labelInput;

  return (
    <div
      className={classNames(
        "w-full flex flex-col sm:flex-row items-center gap-x-4 gap-y-2",
        className
      )}
    >
      <div className="w-full sm:flex-[2]">
        <InputField ref={ref} {...rest} type="text" />
      </div>
      {!Boolean(hide) && (
        <div className="w-full sm:flex-1">
          <InputField
            label={{ name: "label" }}
            {...(restLabel ?? {})}
            type="text"
          />
        </div>
      )}
    </div>
  );
};

export default forwardRef(CustomInput);
