import classNames from "classnames";
import {
  ChangeEvent,
  forwardRef,
  ForwardRefRenderFunction,
  InputHTMLAttributes,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Edit } from "react-feather";
import UserPlaceholder from "../../../../assets/placeholder/user.svg";
import Loader from "../../../loader";
import ImagePreview from "../preview";
import { DocumentUploadTypes } from "../types";

export interface UploaderTypes extends InputHTMLAttributes<HTMLInputElement> {
  click?: () => void;
  focus?: () => void;
}
interface PropsType
  extends Omit<InputHTMLAttributes<HTMLInputElement>, "value" | "onChange">,
    Omit<DocumentUploadTypes, "value"> {
  initials?: string;
  actionTitle?: string;
  preview?: boolean;
  initialsClassName?: string;
  value?: string | File;
  hideEdit?: boolean;
  previewOnly?: boolean;
  lightBox?: boolean;
  imageHight?: number;
  imageWidth?: number;
}

const ProfileUpload: ForwardRefRenderFunction<UploaderTypes, PropsType> = (
  {
    id,
    value,
    onChange,
    getEvent,
    action,
    hideAction,
    actionTitle,
    initials,
    initialsClassName,
    preview,
    hideEdit,
    previewOnly,
    className,
    lightBox,
    imageHight,
    imageWidth,
    ...rest
  },
  ref
) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentImgSrc, setCurrentImgSrc] = useState<string>("");

  useImperativeHandle(ref, () => ({
    click: () => inputRef?.current?.click(),
    focus: () => inputRef?.current?.focus(),
  }));

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      e.preventDefault();
      e.stopPropagation();
      const name = e?.target?.name;
      const files = e?.target?.files?.[0];
      if (files) {
        if (onChange) {
          onChange({
            target: { type: "file", name: name || "files", value: files },
          });
        }
        if (getEvent) {
          getEvent(e);
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    try {
      if (value) {
        if (typeof value === "string") {
          setCurrentImgSrc(value);
        } else if (typeof value === "object" && value?.name) {
          const imgUrl = window?.URL?.createObjectURL(value);
          setCurrentImgSrc(imgUrl);
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [value]);

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      {!previewOnly && (
        <input
          ref={inputRef}
          type="file"
          id={id || "profile-uploader"}
          onChange={handleChange}
          multiple={false}
          accept={rest?.accept ?? "image/png, image/jpeg, image/jpg"}
          className={classNames("hidden", className)}
          {...rest}
        />
      )}

      <label
        id={`label-${id || "profile-uploader"}`}
        htmlFor={id || "profile-uploader"}
        className={classNames(
          "input-label group block h-full bg-transparent transition-all p-1 hover:bg-slate-100 cursor-pointer rounded-base",
          {
            "!rounded-full": previewOnly || hideAction,
            "!cursor-default ": previewOnly || (hideEdit && hideAction),
          }
        )}
      >
        <div className="w-full h-full flex items-center gap-6">
          <div
            className={classNames(
              "input-preview relative w-max h-full rounded-full overflow-hidden aspect-square select-none",
              {
                "flex-1": hideAction,
                "drop-shadow-md": previewOnly || hideAction,
              }
            )}
          >
            {!value && initials ? (
              <div className="input-preview-initials relative flex items-center justify-center w-full h-full max-w-min aspect-square select-none">
                <div
                  className="w-full h-full flex items-center justify-center bg-slate-600 text-white p-1"
                  style={{ containerType: "inline-size" }}
                >
                  <span
                    className={classNames(
                      "initials-text flex items-center justify-center text-[clamp(0.65rem,50cqw,10rem)]  font-medium uppercase text-center",
                      initialsClassName
                    )}
                  >
                    {initials}
                  </span>
                </div>
              </div>
            ) : (
              preview && (
                <ImagePreview
                  data={currentImgSrc ?? UserPlaceholder}
                  lightBox={lightBox}
                  imageHight={imageHight}
                  imageWidth={imageWidth}
                />
              )
            )}

            {!previewOnly && !hideEdit && (
              <div className="input-edit hidden absolute left-0 top-0 w-full h-full group-hover:flex items-center justify-center bg-main bg-opacity-40 backdrop-blur-sm text-white">
                <Edit className="w-[25%] h-[25%]" />
              </div>
            )}
          </div>

          {!previewOnly && !hideAction && (
            <div className="flex-1">
              {action ?? (
                <button className="cursor-pointer bg-main text-white text-sb px-4 py-1.5 rounded-base group-hover:bg-white group-hover:text-font border border-transparent group-hover:border-main transition-all pointer-events-none whitespace-nowrap text-ellipsis overflow-hidden">
                  {actionTitle || "Change Picture"}
                </button>
              )}
            </div>
          )}
        </div>
      </label>
    </>
  );
};

export default forwardRef(ProfileUpload);
