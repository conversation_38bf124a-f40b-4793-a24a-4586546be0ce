import styled from "styled-components";

export const Container = styled.div`
  width: 100%;
  height: 100%;
`;

export const Wrapper = styled.div`
  position: relative;
  height: 100%;
`;

export const FormHolder = styled.div`
  max-width: 100%;
  height: 100%;
  text-align: center;
  position: relative;

  &:hover {
    .upload-button {
      svg {
        color: #4a5770;
        transition: all 0.2s ease-in-out;
      }
      svg.plus-icon {
        fill: #4a5770;
        color: #fff;
        transition: all 0.2s ease-in-out;
      }
    }
  }
`;

export const InputHolder = styled.input`
  display: none;
`;

export const LabelHolder = styled.label`
  height: 100%;
  padding: 30px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-width: 2px;
  border-radius: 1rem;
  border-style: dashed;
  border-color: ${({ theme: { color } }) => `${color.dark}10`};
  background-color: #fff;
  cursor: pointer;

  &:hover {
    background-color: ${({ theme: { color } }) => `${color.sub}10`};
  }

  &.drag-active {
    background-color: ${({ theme: { color } }) => `${color.sub}10`};
  }

  transition: all 0.2s ease-in-out;
`;

export const ContentWrapper = styled.div``;

export const TextHolder = styled.p``;

export const IconWrapper = styled.div`
  cursor: pointer;
  width: fit-content;
  margin: auto;
  position: relative;
  margin-top: 20px;

  svg {
    width: 40px;
    height: 100%;
    color: #a6aab0;
  }
`;

export const PlusIconHolder = styled.div`
  position: absolute;
  bottom: 0;
  right: 0;
  transform: translate(30%, 30%);

  svg {
    width: 25px;
    height: 100%;
    fill: #a6aab0;
    color: #fff;
  }
`;

export const DragBoxHolder = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1rem;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
`;

export const BoxLabelHolder = styled.label`
  color: ${({ theme: { color } }) => color.font};
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
  margin-left: 3px;
  text-transform: capitalize;
`;

export const PreviewWrapper = styled.div`
  margin: 20px 0;
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(2, 1fr);
`;
