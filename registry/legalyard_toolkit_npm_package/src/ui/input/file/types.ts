import { ChangeEvent, DragEvent, InputHTML<PERSON>ttributes, ReactNode } from "react";
import { InputLabelType } from "../components/label";

export interface InputAttributeIconTypes {
  show?: boolean;
  src?: string | ReactNode;
  className?: string;
  name?: string;
  tooltip?: {
    show?: boolean;
    name?: string;
    direction?: string;
  };
  onClick?: (d?: any) => void;
}

export interface InputAttributesTypes {
  prefix?: {
    icon: InputAttributeIconTypes;
    className?: string;
  };
  suffix?: {
    icon: InputAttributeIconTypes;
    className?: string;
  };
}

export interface DocumentUploadTypes
  extends InputHTMLAttributes<HTMLInputElement> {
  id?: string;
  action?: string | ReactNode;
  hideAction?: boolean;
  multiple?: boolean;
  attributes?: InputAttributesTypes;
  accept?: string;
  label?: InputLabelType;
  actionTitle?: string;
  heading?: string;
  className?: string;
  onChange?: (data: any) => void;
  getEvent?: (
    e: ChangeEvent<HTMLInputElement> | DragEvent<HTMLInputElement>
  ) => void;
}
