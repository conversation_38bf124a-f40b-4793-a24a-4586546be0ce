import { forwardRef, ForwardRefRenderFunction } from "react";
import { DocumentUploadTypes } from "../types";

const FileUploader: ForwardRefRenderFunction<
  HTMLInputElement,
  DocumentUploadTypes
> = ({ id, type, ...rest }, ref) => {
  return (
    <input
      ref={ref}
      type={type ?? "file"}
      id={id || "FileUploader"}
      {...rest}
    />
  );
};

export default forwardRef(FileUploader);
