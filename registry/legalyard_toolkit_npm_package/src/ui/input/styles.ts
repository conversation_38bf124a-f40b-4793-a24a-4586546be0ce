import styled, { css } from "styled-components";
import { StyledType } from "./types";

export const Container = styled.div`
  background: transparent;
  position: relative;
  width: 100%;
  margin: 0.2rem 0;
`;

const inputAUtoFill = css`
  box-shadow: 0 0 0 30px ${({ theme: { color } }) => color.page} inset;
  -webkit-box-shadow: 0 0 0 30px ${({ theme: { color } }) => color.page} inset;
  -webkit-text-fill-color: ${({ theme: { color } }) => color.font};
`;

export const Wrapper = styled.div<StyledType>`
  position: relative;
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  background-color: ${({ theme: { color } }) => color.white};
  border-radius: ${({ theme: { element } }) => element.radius};
  ${({ theme: { placeholders } }) => placeholders.inputHeight};

  border: 1px solid
    ${({ $focused, $noBorder, $error, $valid, theme: { color } }) =>
      $noBorder
        ? "transparent"
        : !$noBorder && $error
        ? color.red
        : !$noBorder && $valid
        ? color.green
        : !$noBorder && $focused
        ? color.skyBlue
        : color.gray};

  padding: 0;
  transition: border 150ms ease-in-out;

  input {
    &:-webkit-autofill {
      &:hover,
      &:focus,
      &:active {
        ${inputAUtoFill};
      }
    }
    &:-internal-autofill-selected {
      ${inputAUtoFill};
    }
  }
`;

export const CustomInput = styled.input<StyledType>`
  background-color: transparent;
  border: none;
  color: ${({ theme: { color } }) => color.font};
  font-size: ${({ theme: { font } }) => font.small};
  padding: 0.3rem;
  display: block;
  width: 100%;
  height: 100%;
  transition: all 0.2s ease-in-out;
  border-radius: ${({ theme: { element } }) => element.radius};
  box-sizing: border-box;
  appearance: textfield;

  &:focus {
    outline: none;
    border: none;

    /* &::-webkit-search-cancel-button {
      opacity: 0.65;
      pointer-events: all;
    } */
  }

  &:disabled {
    background-color: ${({ theme: { color } }) => `${color.grey}10`} !important;
    box-shadow: 0 0 0 30px ${({ theme: { color } }) => `${color.grey}10`} inset !important;
    -webkit-box-shadow: 0 0 0 30px
      ${({ theme: { color } }) => `${color.grey}30`} inset !important;
    -webkit-text-fill-color: ${({ theme: { color } }) => color.font} !important;
  }

  &::placeholder {
    font-size: ${({ theme: { font } }) => font.small};
  }

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type="number"] {
    -moz-appearance: textfield;
  }

  &::-webkit-search-cancel-button {
    -webkit-appearance: none;
    display: none;
  }
`;

export const LabelHolder = styled.label<StyledType>`
  color: ${({ theme: { color } }) => color.font};
  font-size: ${({ theme: { font } }) => font.small};
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
  margin-left: 3px;
  text-transform: capitalize;

  ${({
    $staticLabel,
    $focused,
    $icon,
    $countrySelector,
    $signText,
    theme: { font, color },
  }) =>
    !$staticLabel &&
    `position: absolute;
    top: ${$focused ? "-0.05rem" : "50%"};
    ${
      $focused
        ? "left: 10px"
        : $icon && !$signText && !$countrySelector
        ? "left : 1.5rem"
        : $icon && $signText && !$countrySelector
        ? "left : 2.5rem"
        : $icon && $countrySelector
        ? "left : 4.7rem"
        : !$icon && $countrySelector
        ? "left : 3.5rem"
        : "left : 8px"
    };
    transform: translateY(-50%);
    z-index: 9;
    user-select: none;
    pointer-events: none;
    font-weight: 400;
    font-size: ${$focused ? font.extraSmall : font.small};
    font-weight: ${$focused ? 600 : 400};
    background-color: ${$focused ? color.white : "transparent"};
    color: ${$focused ? color.main : color.gray};
    padding: 2px;
    border-radius: 2px 2px 0 0;
    line-height: 1;
    transition: all 150ms ease-in-out;
    `};
`;

export const IconHolder = styled.img`
  width: 13px;
  height: auto;
  margin: 0 0.35rem;
`;

export const InputButton = styled.button<StyledType>`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  background-color: ${({ theme: { color } }) => color.sub};
  border-left: ${({ $bg, theme: { color } }) =>
    $bg ? `2px solid ${$bg}` : `2px solid ${color.main}`};
  color: ${({ theme: { color } }) => color.white};
  justify-content: center;
  font-weight: bold;
  padding: 0.25rem 0.65rem;
  line-height: 1;
  margin-left: 0.05rem;
  border: none;
  height: 100%;
  width: auto;
  transition: all 150ms ease-in-out;
  text-transform: capitalize;
  font-size: ${({ theme: { font } }) => font.small};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  & > * {
    max-height: 100%;
    line-height: 1;
  }

  &:hover {
    background-color: ${({ theme: { color } }) => color.main};
  }
`;

export const TextHolder = styled.span<StyledType>`
  font-size: ${({ $signText, theme: { font } }) =>
    $signText ? font.sub : font.small};
  font-weight: ${({ $bold }) => ($bold ? 600 : 400)};
  color: ${({ theme: { color } }) => color.font};

  margin-left: ${({ $signText }) => ($signText ? "7px" : 0)};

  ${({ $signText, theme: { color, element } }) =>
    $signText
      ? `&::after{
            content: '';
            border-right: 1px solid ${color.font};
            margin: 0 4px 0 7px;
            border-radius: ${element.radius};
      }`
      : null}
`;

export const IconWrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  justify-content: center;
  cursor: pointer;
  margin: 0 0.35rem;

  svg {
    width: 1rem;
    height: 1rem;
    /* margin: 0 0.2rem 0 0.25rem; */
    color: ${({ theme: { color } }) => color.grey};
    transition: color 0.2s ease-in-out;
  }

  &.focused {
    svg {
      color: ${({ theme: { color } }) => color.main};
    }
  }

  &.passToggle {
    svg {
      width: 1.2rem;
      height: 1.2rem;
      margin-right: 0.5rem;
    }
  }
`;

export const CountrySelectorWrapper = styled.div`
  /* max-width: 1rem; */
  height: 100%;
`;

export const ErrorHolder = styled.span`
  color: ${({ theme: { color } }) => color.red};
  font-size: ${({ theme: { font } }) => font.small};
  margin: 7px 0;
  display: block;
`;
