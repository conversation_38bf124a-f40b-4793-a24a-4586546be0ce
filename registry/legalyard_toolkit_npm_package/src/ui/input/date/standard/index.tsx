import {
  useRef,
  useState,
  useEffect,
  forwardRef,
  Ref,
  FC,
  ChangeEvent,
  WheelEvent,
  MouseEvent,
  KeyboardEvent,
} from "react";
import { ChevronDown } from "react-feather";
import useOutsideAlert from "../../../../utils/hooks/useOutsideAlert";
import { monthData } from "../data";
import * as validator from "../../../../utils/validator";
import { DateSelectorTypes, dateValues } from "./types";
import * as Styles from "./styles";

const DateSelector: FC<DateSelectorTypes> = forwardRef(
  (
    { setTodayDate, label, onChange, error, name },
    ref: Ref<HTMLDivElement>
  ) => {
    const wrapperRef = useRef(null);
    const [drop, setDrop] = useState(false);
    const [valid, setValid] = useState({
      day: false,
      month: false,
      year: false,
    });
    const [errors, setErrors] = useState({
      day: "",
      month: "",
      year: "",
    });
    const [values, setValues] = useState<dateValues>({
      day: "",
      month: "",
      year: "",
    });

    const { day, month, year } = values;

    const errorExist = Object.values(errors).some((item) => item !== "");
    const dateExist = Object.values(values).every((item) => item !== "");
    const isDayValid = Boolean(day) && !errors.day;
    const isMonthValid = Boolean(month) && !errors.month;
    const isYearValid = Boolean(year) && !errors.year;

    const errMsg = "Please enter a valid date";
    // const futureDateMsg = "Date should not be from the past";

    // useImperativeHandle(ref, () => ({}));

    useEffect(() => {
      if (Boolean(setTodayDate) && !dateExist) {
        const setTodaysDate = () => {
          const currentDate = new Date();
          const cDay = currentDate.getDate();
          const cMonth = currentDate.getMonth();
          const cYear = currentDate.getFullYear();

          setValues((prevState) => ({
            ...prevState,
            day: cDay,
            month: monthData[cMonth]?.name,
            year: cYear,
          }));

          if (onChange) {
            onChange({
              target: {
                value: {
                  day: cDay,
                  month: monthData[cMonth]?.name,
                  year: cYear,
                },
                type: "date",
                name,
              },
            });
          }
        };

        setTodaysDate();
      }
    }, [dateExist, name, onChange, setTodayDate]);

    const handleDay = (e: ChangeEvent<HTMLInputElement>) => {
      try {
        const value = e?.target?.value;

        if (isNaN(Number(value)) || value?.toString() === "0") return;

        setValues({ ...values, day: value });

        handleError("day", value);
      } catch (err) {
        console.error({ err });
      }
    };

    const handleMonth = (val: string) => {
      try {
        setValues({ ...values, month: val });

        if (Boolean(val)) {
          handleValidState("month", val);
        }

        setDrop(false);
      } catch (err) {
        console.error({ err });
      }
    };

    const handleYear = (e: ChangeEvent<HTMLInputElement>) => {
      try {
        const value = e.target.value;

        if (isNaN(Number(value)) || value?.toString() === "0") return;

        setValues({ ...values, year: value });

        handleError("year", value);
      } catch (err) {
        console.error({ err });
      }
    };

    const handleError = (type: string, value: string | number) => {
      if (type) {
        handleValidator(type, value, handleErrorState, handleValidState);
      }
    };

    const handleValidator = (
      type: string,
      value: string | number,
      errorAction: (type: string) => void,
      validAction: (type: string, value: string | number) => void
    ) => {
      const validatorFunction = (validator as any)[`${type}Validator`];

      if (errorAction && !validatorFunction(value)?.validate) {
        errorAction(type);
      } else if (validAction) {
        validAction(type, value);
      }
    };

    const handleErrorState = (type: any) => {
      setErrors({ ...errors, [type]: errMsg });
      setValid({ ...valid, [type]: false });
      if (onChange) {
        onChange({
          target: { value: { ...values, [type]: "" }, type: "date", name },
        });
      }
    };

    const handleValidState = (type: string, value: string | number) => {
      const otherInput = type === "day" ? "year" : "day";
      const isMonth = type === "month";
      setErrors({ ...errors, [type]: "" });
      setValid({ ...valid, [type]: true });
      if (!isMonth) {
        if (onChange) {
          onChange({
            target: {
              value: {
                ...values,
                [type]: value,
                [otherInput]: valid[otherInput] ? values[otherInput] : "",
              },
              type: "date",
              name,
            },
          });
        }
      } else {
        const lastInput = otherInput === "year" ? "day" : "year";
        if (onChange) {
          onChange({
            target: {
              value: {
                ...values,
                [type]: value,
                [otherInput]: valid[otherInput] ? values[otherInput] : "",
                [lastInput]: valid[lastInput] ? values[lastInput] : "",
              },
              type: "date",
              name,
            },
          });
        }
      }
    };

    const validOnBlur = () => {
      if (!isDayValid && (isMonthValid || isYearValid)) {
        handleInputBlur("day");
      } else if (!isYearValid && isDayValid && isMonthValid) {
        handleInputBlur("year");
      } else if (!isMonthValid && (isDayValid || isYearValid)) {
        handleInputBlur("month");
      }
    };

    const handleInputBlur = (type: string) => {
      try {
        if (!validator.emptyFieldValidator((values as any)[type]).validate) {
          setErrors({
            ...errors,
            [type]: validator.emptyFieldValidator((values as any)[type]).msg,
          });
          setValid({ ...valid, [type]: false });
        }

        if (type === "month" && drop) {
          setDrop(false);
        }
      } catch (err) {
        console.error({ err });
      }
    };

    const handleWrapperBlur = () => {
      validOnBlur();
    };

    const handleDrop = (e: MouseEvent<HTMLDivElement>) => {
      try {
        e.stopPropagation();
        setDrop(!drop);
      } catch (err) {
        console.error({ err });
      }
    };

    useOutsideAlert(wrapperRef, () => setDrop(false));

    return (
      <Styles.Container>
        {label && (
          <Styles.LabelHolder $staticLabel={Boolean(label)}>
            {label}
          </Styles.LabelHolder>
        )}
        <Styles.Wrapper
          $valid={dateExist && !error}
          $error={Boolean(dateExist && error)}
          onBlur={() => handleWrapperBlur()}
        >
          <Styles.InputHolder
            type="text"
            name="day"
            onKeyDown={(e: KeyboardEvent<HTMLInputElement>) =>
              validator.handleNumberException(e)
            }
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleDay(e)}
            onKeyUp={() => handleWrapperBlur()}
            onBlur={() => handleInputBlur("day")}
            placeholder="Day"
            value={day}
            maxLength={2}
            autoComplete="off"
            onWheel={(e: WheelEvent<HTMLDivElement>) =>
              (e.target as HTMLDivElement).blur()
            }
            $error={Boolean(errors.day)}
            $valid={Boolean(day && !errors.day)}
            tabIndex={0}
          />
          <Styles.Divider />
          <Styles.SelectWRapper
            ref={wrapperRef}
            onFocus={() => setDrop(true)}
            onBlur={() => handleInputBlur("month")}
            tabIndex={0}
            $error={Boolean(errors.month)}
            $valid={Boolean(month && !errors.month)}
          >
            <Styles.SelectedHolder
              onClick={(e: MouseEvent<HTMLDivElement>) => handleDrop(e)}
              $selected={Boolean(month)}
            >
              <Styles.TextHolder>{month || "month"}</Styles.TextHolder>
              <ChevronDown />
            </Styles.SelectedHolder>
            {drop && (
              <Styles.ListHolder $show={drop}>
                {monthData?.map((item, index) => {
                  return (
                    <Styles.ItemHolder
                      key={index}
                      aria-label="month"
                      onClick={() => handleMonth(item?.name)}
                    >
                      {item?.name}
                    </Styles.ItemHolder>
                  );
                })}
              </Styles.ListHolder>
            )}
          </Styles.SelectWRapper>
          <Styles.Divider />
          <Styles.InputHolder
            type="text"
            name="year"
            onKeyDown={(e: KeyboardEvent<HTMLInputElement>) =>
              validator.handleNumberException(e)
            }
            onChange={(e: ChangeEvent<HTMLInputElement>) => handleYear(e)}
            onKeyUp={() => handleWrapperBlur()}
            onBlur={() => handleInputBlur("year")}
            placeholder="Year"
            value={year}
            maxLength={4}
            autoComplete="off"
            onWheel={(e: WheelEvent<HTMLDivElement>) =>
              (e.target as HTMLDivElement).blur()
            }
            $error={Boolean(errors.year)}
            $valid={Boolean(year && !errors.year)}
            tabIndex={0}
          />
        </Styles.Wrapper>
        {(errorExist || error) && (
          <Styles.ErrorHolder>
            {errors?.month ? errors?.month : errMsg}
          </Styles.ErrorHolder>
        )}
      </Styles.Container>
    );
  }
);

export default DateSelector;
