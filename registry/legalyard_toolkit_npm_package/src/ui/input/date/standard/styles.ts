import styled, { css } from "styled-components";
import * as Styles from "../../styles";
import { StyledType } from "../../types";

export const Container = styled.div`
  font-size: ${({ theme: { font } }) => font.sub};
`;

export const Wrapper = styled(Styles.Wrapper)<StyledType>`
  & > * {
    border-bottom: 1px solid
      ${({ $error, $valid }) =>
        $error || $valid ? "transparent !important" : "transparent"};
  }
  font-size: ${({ theme: { font } }) => font.sub};
`;

const errBackground = css<StyledType>`
  background-color: ${({ $error, theme: { color } }) =>
    $error ? `${color.red}20` : "transparent"};
`;

export const InputHolder = styled(Styles.CustomInput)<StyledType>`
  flex: 1;
  border-radius: ${({ $error, theme: { element } }) =>
    $error ? element.radius : 0};

  ${({ $error }) => $error && errBackground}
`;

export const Divider = styled.div<StyledType>`
  width: 1px;
  height: 20px;
  background-color: ${({ theme: { color } }) => `${color.font}70`};
`;

export const LabelHolder = styled(Styles.LabelHolder)<StyledType>``;

export const SelectWRapper = styled.div<StyledType>`
  flex: 2;
  max-width: 70%;
  width: 100%;
  padding: 0 5px;
  font-size: ${({ theme: { font } }) => font.sub};

  position: relative;

  border-radius: ${({ $error, theme: { element } }) =>
    $error ? element.radius : 0};
  border-bottom: 1px solid
    ${({ $valid, $error, theme: { color } }) =>
      $valid ? color.green : $error ? color.orange : "transparent"};

  ${({ $error }) => $error && errBackground}

  &:focus {
    outline: none;
  }
`;

export const SelectedHolder = styled.div<StyledType>`
  cursor: pointer;
  padding: 11px 15px;
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  justify-content: space-between;

  span,
  svg {
    color: ${({ $selected, theme: { color } }) =>
      $selected ? color.font : `${color.font}99`};
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

export const ListHolder = styled.ul<StyledType>`
  position: absolute;
  top: -20px;
  left: 0;
  right: 0;

  text-align: left;
  z-index: 9;
  background-color: ${({ theme: { color } }) => color.page};
  padding-left: 0;
  max-height: 200px;
  overflow: hidden;
  overflow-y: auto;
  padding: 10px 0;
  border: 1px solid
    ${({ $show, theme: { color } }) => ($show ? color.main : "transparent")};
  border-radius: ${({ theme: { element } }) => element.radius};
`;

export const ItemHolder = styled.li<StyledType>`
  cursor: pointer;
  padding: 10px;

  &:hover {
    background-color: ${({ theme: { color } }) => color.main};
    color: ${({ theme: { color } }) => color.white};
  }
`;

export const TextHolder = styled.span<StyledType>`
  text-transform: capitalize;
`;

export const ErrorHolder = styled(Styles.ErrorHolder)``;
