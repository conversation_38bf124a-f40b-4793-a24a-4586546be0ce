import {
  forwardRef,
  ForwardRefRenderFunction,
  Ref,
  useEffect,
  useRef,
  useState,
} from "react";

import Calendar from "../../calendar/mini";
import useOutsideAlert from "../../../utils/hooks/useOutsideAlert";
import usePosition from "../../../utils/hooks/usePosition";
import classNames from "classnames";
import InputBox from "../components/input";
import InputLabel from "../components/label";
import InputWrapper from "../components/wrapper";
import Styles from "../styles.module.css";
import { DateSelectorTypes } from "./types";

// TODO
// Add Loading to calendar dates

const DateSelector: ForwardRefRenderFunction<
  HTMLInputElement,
  DateSelectorTypes
> = (
  { value, label, onChange, name, onFocus, onBlur, className },
  ref: Ref<HTMLDivElement>
) => {
  const wrapperRef = useRef(null);

  const [date, setDate] = useState<Date | null>(null);
  const [openCalendar, setOpenCalendar] = useState<boolean>(false);

  const { toTop } = usePosition(wrapperRef);

  useEffect(() => {
    if (value) {
      const valueDate = new Date(value);
      setDate(valueDate);
    }
  }, [value]);

  const handleChange = (val: Date | null) => {
    try {
      setDate(val);

      if (onChange) {
        const stringDate = val?.toISOString();
        onChange({
          target: {
            value: stringDate,
            type: "date",
            name,
          },
        });
      }

      setOpenCalendar(false);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    try {
      if (onFocus) {
        onFocus(openCalendar);
      }
      if (onBlur) {
        onBlur(openCalendar);
      }
    } catch (error) {
      console.error(error);
    }
  }, [onBlur, onFocus, openCalendar]);

  const dateValue = `${date?.getDate() ?? "dd"} / ${
    date?.getMonth() ?? "mm"
  } / ${date?.getFullYear() ?? "yyyy"}`;

  useOutsideAlert(wrapperRef, () => setOpenCalendar(false));

  return (
    <div
      ref={wrapperRef}
      aria-label="Date Selector"
      className={classNames(Styles.input_container, "text-sb", className)}
    >
      {Boolean(label) && <InputLabel {...(label ?? {})} />}

      <InputWrapper valid={Boolean(date)} focused={Boolean(openCalendar)}>
        <InputBox
          type="text"
          name="trip-start"
          value={dateValue?.toString() ?? ""}
          onChange={(e) => e?.preventDefault}
          onClick={() => setOpenCalendar((prev) => !prev)}
        />

        {openCalendar && (
          <div
            className={classNames(
              "bg-white absolute shadow-2xl w-full sm:w-[300px] max-w-[300px] px-4 py-2 rounded-base z-50 border border-slate-300",
              "left-0",
              toTop ? "bottom-full" : "top-full"
            )}
          >
            <Calendar
              navigation
              currentDate={date}
              getSelected={(val) => handleChange(val)}
            />
          </div>
        )}
      </InputWrapper>
    </div>
  );
};

export default forwardRef(DateSelector);
