export interface styles extends React.InputHTMLAttributes<HTMLInputElement> {
  $bold?: boolean;
  $focused?: boolean;
  $noBorder?: boolean;
  $error?: boolean;
  $valid?: boolean;
  $staticLabel?: boolean;
  $icon?: boolean;
  $countrySelector?: boolean;
  $signText?: boolean;
  $bg?: string;
  $htmlFor?: string;
  $inline?: boolean;
  $selected?: boolean;
  $inGrid?: boolean;
  $gridColumn?: boolean;
  $rounded?: boolean;
  $show?: boolean;
  $isIcon?: boolean;
  $open?: boolean;
  $greyBorder?: boolean;
  $openAbove?: boolean;
  $small?: boolean;
  $extraSmall?: boolean;
  $noValue?: boolean;
  $actionable?: boolean;
  $height?: string | number;
  $margin?: string | number;
  $blink?: boolean;
  ref?: any;
}

export type StyledType = styles;
