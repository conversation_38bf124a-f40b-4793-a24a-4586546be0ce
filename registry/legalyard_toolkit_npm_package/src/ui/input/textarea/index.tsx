import classNames from "classnames";
import { forwardRef, ForwardRefRenderFunction, Ref, useState } from "react";
import InputLabel from "../components/label";
import InputWrapper from "../components/wrapper";
import { TextareaTypes } from "./types";

const Textarea: ForwardRefRenderFunction<HTMLTextAreaElement, TextareaTypes> = (
  {
    label,
    error,
    value,
    onFocus,
    onBlur,
    resize,
    row = 5,
    cols = 33,
    className,
    textarea_className,
    ...otherProps
  },
  ref: Ref<HTMLTextAreaElement>
) => {
  const [focused, setFocused] = useState(false);

  const getFocused = () => {
    if (onFocus) {
      onFocus(true);
    }
    setFocused(true);
  };

  const getBlurred = () => {
    if (onBlur) {
      onBlur(false);
    }
    if (onFocus) {
      onFocus(false);
    }
    setFocused(false);
  };

  return (
    <div className={classNames("textarea relative w-full my-1", className)}>
      {Boolean(label) && (
        <InputLabel
          {...(label ?? {})}
          focused={focused}
          className="top-3"
          focused_className="!-top-[0.03rem]"
        />
      )}
      <InputWrapper
        error={Boolean(error)}
        valid={value && !error}
        focused={focused}
        className="h-full min-h-full max-h-max w-full min-w-full max-w-max"
      >
        <textarea
          ref={ref}
          rows={row}
          cols={cols}
          spellCheck={true}
          autoCorrect="on"
          className={classNames(
            "resize-none flex-1",
            "bg-white border-0 text-font text-sb p-1.5 block w-full h-full transition-all rounded-base box-border appearance-auto focus:outline-0",
            "disabled:bg-grey disabled:bg-opacity-10 disabled:shadow-inner",
            "placeholder:capitalize placeholder:text-sm",
            resize && "resize",
            textarea_className
          )}
          onFocus={() => getFocused()}
          onBlur={() => getBlurred()}
          value={value}
          {...otherProps}
        />
      </InputWrapper>
    </div>
  );
};
export default forwardRef(Textarea);
