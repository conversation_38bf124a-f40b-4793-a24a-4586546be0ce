import classNames from "classnames";
import { ReactNode } from "react";

const FormContainer = ({
  children,
  className,
  hideStyles,
}: {
  children: ReactNode;
  className: string;
  hideStyles: boolean;
}) => {
  return (
    <div
      className={classNames(
        !hideStyles &&
          "w-[60vw] max-h-[85vh] overflow-x-hidden overflow-y-auto",
        className
      )}
    >
      {children}
    </div>
  );
};

export default FormContainer;
