import { ReactNode } from "react";

export interface buttonPropsType {
  content?: string | ReactNode;
  disabled?: boolean;
  hidden?: boolean;
}

export interface ModalFormLayoutType {
  hideStyle?: boolean;
  children?: ReactNode;
  title?: string;
  open?: boolean;
  button?: buttonPropsType;
  onSubmit?: (d?: any) => void;
  onClose?: (d?: any) => void;
  className?: string;
  buttonClassName?: string;
}

export interface styles extends React.InputHTMLAttributes<HTMLInputElement> {
  $bg?: string;
  $color?: string;
  $randomBg?: boolean;
}

export type StyledType = styles;
