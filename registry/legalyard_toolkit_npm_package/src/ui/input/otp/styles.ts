import styled from "styled-components";

export const InputHolder = styled.input<{
  $focused?: boolean;
  $noBorder?: boolean;
  $error?: boolean;
  $valid?: boolean;
}>`
  background-color: transparent;
  border-radius: ${({ theme: { element } }) => element.radius};
  width: 100%;
  padding: 0.3rem;
  min-width: 2.5rem;
  color: ${({ theme: { color } }) => color.font};
  text-align: center;
  display: block;
  box-sizing: border-box;
  display: inline-block;
  ${({ theme: { placeholders } }) => placeholders.inputHeight};

  border: 1px solid
    ${({ $error, $valid, theme: { color } }) =>
      $error ? color.red : $valid ? color.green : color.gray};

  &:disabled {
    background-color: ${({ theme: { color } }) => `${color.grey}10`} !important;
    box-shadow: 0 0 0 30px ${({ theme: { color } }) => `${color.grey}10`} inset !important;
    -webkit-box-shadow: 0 0 0 30px
      ${({ theme: { color } }) => `${color.grey}30`} inset !important;
    -webkit-text-fill-color: ${({ theme: { color } }) => color.font} !important;
  }

  &:focus {
    outline: none;
    border: 1px solid ${({ theme: { color } }) => color.skyBlue};
  }
`;

export const LabelHolder = styled.span`
  color: ${({ theme: { color } }) => color.font};
  font-size: ${({ theme: { font } }) => font.sub};
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
  margin-left: 3px;
  text-transform: capitalize;
`;
