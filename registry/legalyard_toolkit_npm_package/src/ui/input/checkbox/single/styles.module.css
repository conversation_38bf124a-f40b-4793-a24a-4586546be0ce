.checkbox_input[type="checkbox"] {
  @apply relative cursor-pointer bg-white border-2 border-main text-main w-5 h-5 outline-none appearance-none rounded-sm hover:shadow;
  @apply before:block before:box-border before:w-full before:h-full;
}
.checkbox_input[type="checkbox"]:hover {
  @apply bg-slate-50
}
.checkbox_input:before {
  @apply block box-border w-full h-full;
}
.checkbox_input:checked::before {
  @apply bg-white absolute top-1/2 left-1/2 w-[5px] h-[10px] border-solid border-main first-line:border-t-0 border-l-0 border-r-[2px] border-b-[2px] -translate-x-1/2 -translate-y-[55%] rotate-[45deg];
}
