import { ChangeEvent, FC } from "react";
import { SingleCheckType } from "./types";
import classNames from "classnames";
import Styles from "./styles.module.css";

const SingleCheck: FC<SingleCheckType> = ({
  label,
  name,
  id,
  onChange,
  getElement,
  boolean,
  error,
  className,
  inputClassName,
  ...rest
}) => {
  const uniqId: string = (name || id) ?? "select";

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      const isChecked = e?.target?.checked;
      const value = e?.target?.name;

      if (onChange) {
        if (getElement) {
          onChange(e);
        } else {
          if (boolean) {
            onChange({ target: { name, value: isChecked } });
          } else {
            onChange({ target: { name, value: isChecked ? value : "" } });
          }
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div
      title={label}
      className={classNames(
        "checkbox my-2 flex items-center select-none",
        className
      )}
    >
      <input
        type="checkbox"
        id={uniqId}
        name={uniqId}
        value={name}
        onChange={(e) => handleChange(e)}
        {...rest}
        className={classNames(
          Styles.checkbox_input,
          error && "!border-red",
          label && "!mr-2",
          inputClassName
        )}
      />
      {label && (
        <label
          htmlFor={String(uniqId)}
          className="cursor-pointer leading-normal w-full h-full"
        >
          {label}
        </label>
      )}
    </div>
  );
};

export default SingleCheck;
