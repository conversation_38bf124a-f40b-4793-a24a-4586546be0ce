import { ChangeEvent, FC, forwardRef, Ref, useEffect, useState } from "react";
import { handleChecked } from "../../../utils/helpers/data/input";
import classNames from "classnames";
import { checkboxInputType } from "./types";
import SingleCheck from "./single";

const CheckboxInput: FC<checkboxInputType> = forwardRef(
  (
    {
      checks,
      error,
      onChange,
      boolean,
      getElement,
      checked,
      inline,
      getChecked,
      ...rest
    },
    ref: Ref<HTMLDivElement>
  ) => {
    const [selected, setSelected] = useState(checked || []);

    useEffect(() => {
      if (getChecked) {
        getChecked(selected);
      }
    }, [getChecked, selected]);

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
      try {
        const name = e?.target?.name;
        const value = e?.target?.value;

        const val = handleChecked(selected, name);
        setSelected(val ?? []);

        if (onChange) {
          if (getElement) {
            onChange(e);
          } else {
            if (boolean) {
              onChange({ target: { name, value } });
            } else {
              onChange({ target: { name, value } });
            }
          }
        }
      } catch (error) {
        console.error(error);
      }
    };

    const propsGroup = {
      error,
      boolean,
      getElement,
    };

    return (
      <div
        ref={ref}
        aria-label="Checkbox Input"
        className={classNames(
          "checkbox flex justify-between flex-wrap gap-3 flex-col items-start",
          inline && "!flex-row !item-center"
        )}
      >
        {Array.isArray(checks) &&
          checks?.length > 0 &&
          checks.map((item, index) => {
            const uniqId = item?.id || item?._id || item?.name;

            return (
              <SingleCheck
                {...propsGroup}
                key={item + index}
                className={classNames(inline && "inline-flex")}
                id={uniqId}
                name={uniqId}
                label={item?.label}
                checked={selected?.includes(uniqId) || false}
                onChange={handleChange}
              />
            );
          })}
        {error ? (
          <div className="my-1 text-red">
            <span className="text-sb">{error}</span>
          </div>
        ) : null}
      </div>
    );
  }
);

export default CheckboxInput;
