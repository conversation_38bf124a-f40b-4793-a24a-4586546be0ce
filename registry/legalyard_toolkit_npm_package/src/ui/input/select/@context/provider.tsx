import useOutsideAlert from "../../../../utils/hooks/useOutsideAlert";
import {
  Dispatch,
  FC,
  ReactNode,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import { Context } from ".";
import { MultipleSelectedType, SelectedType, SelectInputType } from "../types";
import { useSelectHelper } from "./helpers";

interface ContextTypes extends SelectInputType {
  children: ReactNode;
}

export interface StateType {
  selected: SelectedType;
  setSelected: Dispatch<SetStateAction<SelectedType>>;
  multipleSelected: MultipleSelectedType;
  setMultipleSelected: Dispatch<SetStateAction<MultipleSelectedType>>;
  inputValue?: string;
  setInputValue: Dispatch<SetStateAction<string>>;
  optionSearchValue: string;
  setOptionSearchValue: Dispatch<SetStateAction<string>>;
  focused?: boolean;
  setFocused: Dispatch<SetStateAction<boolean>>;
  selectedFromOptions?: boolean;
  setSelectedFromOptions?: Dispatch<SetStateAction<boolean>>;
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  openAbove?: boolean;
  setOpenAbove?: Dispatch<SetStateAction<boolean>>;
  valueForBlink?: string;
  setValueForBlink?: Dispatch<SetStateAction<string>>;
}

const SelectInputProvider: FC<ContextTypes> = ({ children, ...rest }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const [selected, setSelected] = useState<SelectedType>(null);
  const [multipleSelected, setMultipleSelected] =
    useState<MultipleSelectedType>([]);
  const [inputValue, setInputValue] = useState<string>("");
  const [optionSearchValue, setOptionSearchValue] = useState<string>("");
  const [valueForBlink, setValueForBlink] = useState<string>("");
  const [focused, setFocused] = useState<boolean>(false);
  const [selectedFromOptions, setSelectedFromOptions] =
    useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);

  const {
    handleCallback,
    setInitialValue,
    dropdownToggle,
    handleReset,
    handleSelectInputChange,
    handleOptionSelect,
    finalData,
    getSearchValue,
    getFilteredCount,
    handleRemoveOption,
  } = useSelectHelper({
    selected,
    setSelected,
    multipleSelected,
    setMultipleSelected,
    setInputValue,
    inputValue,
    setOpen,
    setSelectedFromOptions,
    setValueForBlink,
    setOptionSearchValue,
    optionSearchValue,
    inputRef,
    ...rest,
  });

  // set existing value
  useEffect(() => {
    setInitialValue();
  }, [setInitialValue]);

  const handleFocused = () => {
    setOpen(true);
    setFocused(true);
  };

  const handleBlur = () => {
    setOpen(false);
    setFocused(false);
    setOptionSearchValue("");
    if (!selected) {
      setInputValue("");
    }
  };

  useOutsideAlert(wrapperRef, handleBlur);

  const statesData: StateType = {
    selected,
    setSelected,
    multipleSelected,
    setMultipleSelected,
    inputValue,
    setInputValue,
    optionSearchValue,
    setOptionSearchValue,
    focused,
    setFocused,
    selectedFromOptions,
    open,
    setOpen,
    valueForBlink,
    setValueForBlink,
  };

  const valueGroup = {
    helper: {
      setInitialValue,
      handleSelectInputChange,
      handleReset,
      handleCallback,
      dropdownToggle,
      handleFocused,
      handleBlur,
      handleOptionSelect,
      finalData,
      getSearchValue,
      getFilteredCount,
      handleRemoveOption,
    },
    state: statesData,
    refs: {
      containerRef,
      wrapperRef,
      dropdownRef,
      inputRef,
    },
    ...rest,
  };

  return (
    <Context.Provider value={{ ...valueGroup }}>{children}</Context.Provider>
  );
};

export default SelectInputProvider;
