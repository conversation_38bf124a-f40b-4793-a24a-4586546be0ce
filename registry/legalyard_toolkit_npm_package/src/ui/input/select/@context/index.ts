import { createContext, RefObject, useContext } from "react";
import { SelectInputType } from "../types";
import { HelperExportType } from "./helpers";
import { StateType } from "./provider";

interface Helpers extends HelperExportType {
  handleFocused: () => void;
  handleBlur: () => void;
}

export interface ContextValueType extends SelectInputType {
  helper: Helpers;
  state: StateType;
  refs: {
    inputRef: RefObject<HTMLInputElement>;
    [key: string]: RefObject<HTMLDivElement>;
  };
}

export const Context = createContext<ContextValueType | undefined>(undefined);

const useSelectInput = (): ContextValueType => {
  const context = useContext(Context);

  if (!context) {
    throw new Error("useSelectInput must be used within a SelectInputProvider");
  }

  return context;
};

export default useSelectInput;
