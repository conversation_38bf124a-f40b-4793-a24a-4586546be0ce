import { validArray } from "../../../../utils/helpers/data/array";
import { Dispatch, RefObject, SetStateAction, useCallback } from "react";
import {
  GroupOptionDataType,
  MultipleSelectedType,
  OptionDataType,
  SelectedType,
  SelectInputType,
} from "../types";

interface HelperType extends SelectInputType {
  selected: SelectedType;
  setSelected: Dispatch<SetStateAction<SelectedType>>;
  multipleSelected: MultipleSelectedType;
  setMultipleSelected: Dispatch<SetStateAction<MultipleSelectedType>>;
  setInputValue: Dispatch<SetStateAction<string>>;
  inputValue: string;
  setValueForBlink: Dispatch<SetStateAction<string>>;
  optionSearchValue: string;
  setOptionSearchValue: Dispatch<SetStateAction<string>>;
  setSelectedFromOptions: Dispatch<SetStateAction<boolean>>;
  setOpen: Dispatch<SetStateAction<boolean>>;
  inputRef: RefObject<HTMLInputElement>;
}

export const useSelectHelper = ({
  value,
  option,
  multiple,
  withInput,
  selected,
  setSelected,
  multipleSelected,
  setMultipleSelected,
  inputValue,
  setInputValue,
  setSelectedFromOptions,
  setOpen,
  setValueForBlink,
  optionSearchValue,
  setOptionSearchValue,
  inputRef,
  onChange,
  name,
}: HelperType): HelperExportType => {
  const handleCallback = useCallback(
    (selectedItem: SelectedType | MultipleSelectedType) => {
      try {
        if (onChange) {
          if (multiple) {
            onChange({
              target: {
                type: "select",
                name,
                value: validArray(selectedItem)
                  ? selectedItem?.map((item) => item?.value)
                  : null,
                selected: selectedItem,
              },
            });
          } else {
            onChange({
              target: {
                type: "select",
                name,
                value: (selectedItem as SelectedType)?.value ?? null,
                selected: selectedItem,
              },
            });
          }
        }
      } catch (error) {
        console.error(error);
      }
    },
    [multiple, name, onChange]
  );

  const handleSelectInputChange = useCallback(
    (value: string = "") => {
      setSelected(null);
      setInputValue(value);
      setValueForBlink(value);
      setSelectedFromOptions(false);

      setTimeout(() => {
        setValueForBlink("");
      }, 1000);
    },
    [setInputValue, setSelected, setSelectedFromOptions, setValueForBlink]
  );

  const setInitialValue = useCallback(() => {
    try {
      if (Boolean(value)) {
        if (multiple && Array.isArray(value)) {
          setMultipleSelected(value);
        } else if (
          typeof value === "object" &&
          Boolean(value?.title) &&
          Boolean(value?.value)
        ) {
          setSelected(value);
          if (withInput) {
            setInputValue(value?.title?.toString());
            setSelectedFromOptions(true);
            setOpen(false);
          }
        } else if (typeof value === "string" || typeof value === "number") {
          const selectedOption = validArray(option?.data)
            ? option?.data?.find((item: any) => item?.value === value)
            : null;

          const valuePayload: OptionDataType = selectedOption ?? {
            title: String(value ?? ""),
            value: String(value ?? ""),
          };

          setSelected(valuePayload);

          if (withInput) {
            setInputValue(valuePayload?.title?.toString());
            setSelectedFromOptions(true);
            setOpen(false);
          }
        }
      }
    } catch (error) {
      console.error(error);
    }
  }, [
    multiple,
    option?.data,
    setInputValue,
    setMultipleSelected,
    setOpen,
    setSelected,
    setSelectedFromOptions,
    value,
    withInput,
  ]);

  const dropdownToggle = useCallback(
    (val: boolean) => {
      try {
        setOpen(val);
      } catch (error) {
        console.error(error);
      }
    },
    [setOpen]
  );

  const handleReset = useCallback(() => {
    try {
      setMultipleSelected([]);
      setSelected(null);
      setInputValue("");
      handleCallback(null);
    } catch (error) {
      console.error(error);
    }
  }, [handleCallback, setInputValue, setMultipleSelected, setSelected]);

  const handleRemoveOption = useCallback(
    (removedItem: OptionDataType) => {
      try {
        if (multiple) {
          setMultipleSelected((prev) => {
            if (option?.data && prev?.length < option?.data?.length - 1) {
              inputRef?.current?.focus();
            }

            let selectedData = validArray(prev) ? Array.from(prev) : [];
            selectedData = selectedData?.filter(
              (item) => item?.value !== removedItem?.value
            );

            handleCallback(selectedData);
            return selectedData;
          });
          setInputValue("");
        }
      } catch (error) {
        console.error(error);
      }
    },
    [
      handleCallback,
      inputRef,
      multiple,
      option?.data,
      setInputValue,
      setMultipleSelected,
    ]
  );

  const handleOptionSelect = useCallback(
    (selectedItem: SelectedType) => {
      try {
        if (selectedItem) {
          if (multiple) {
            setMultipleSelected((prev) => {
              if (option?.data && prev?.length < option?.data?.length - 1) {
                inputRef?.current?.focus();
              } else {
                setOpen(false);
              }

              let selectedData = validArray(prev) ? Array.from(prev) : [];

              const isExist = selectedData?.find(
                (sl) => sl?.value === selectedItem?.value
              );

              if (isExist) {
                selectedData = selectedData?.filter(
                  (sl) => sl?.value !== selectedItem?.value
                );
              } else {
                selectedData.push(selectedItem);
              }
              handleCallback(selectedData);
              setInputValue("");

              return selectedData;
            });
          } else {
            setSelected(selectedItem);
            handleCallback(selectedItem);

            if (withInput) {
              setInputValue(selectedItem?.title?.toString());
              setSelectedFromOptions(true);
            }

            setOpen(false);

            setTimeout(() => {
              setOptionSearchValue("");
            }, 50);
          }
        }
      } catch (err) {
        console.error(err);
      }
    },
    [
      handleCallback,
      inputRef,
      multiple,
      option?.data,
      setInputValue,
      setMultipleSelected,
      setOpen,
      setOptionSearchValue,
      setSelected,
      setSelectedFromOptions,
      withInput,
    ]
  );

  const getSearchValue = useCallback((): string => {
    try {
      if (withInput) {
        return inputValue;
      } else if (option?.search && !withInput) {
        return optionSearchValue;
      } else {
        return "";
      }
    } catch (error) {
      return "";
    }
  }, [inputValue, option?.search, optionSearchValue, withInput]);

  const getFiltered = useCallback((): OptionDataType[] => {
    try {
      const searchValue: string = getSearchValue();
      if (searchValue && validArray(option?.data)) {
        return (
          option?.data?.filter((item: OptionDataType) => {
            return (
              (typeof item?.title === "string" ||
                typeof item?.title === "number") &&
              item?.title
                ?.toString()
                ?.toLowerCase()
                ?.includes(searchValue?.toString()?.toLowerCase())
            );
          }) ?? []
        );
      } else {
        return [];
      }
    } catch (error) {
      return [];
    }
  }, [getSearchValue, option?.data]);

  const getGroupFiltered = useCallback((): GroupOptionDataType[] => {
    try {
      const searchValue: string = getSearchValue()?.toLowerCase();
      if (searchValue && validArray(option?.data)) {
        return (
          option?.data?.reduce<GroupOptionDataType[]>(
            (filteredGroups, property) => {
              const filteredFields = property?.data.filter(
                (opt: OptionDataType) =>
                  (opt && opt?.name?.toLowerCase()?.includes(searchValue)) ||
                  (opt && opt?.value?.toLowerCase()?.includes(searchValue))
              );

              if (filteredFields.length > 0) {
                filteredGroups.push({ ...property, data: filteredFields });
              }

              return filteredGroups;
            },
            []
          ) ?? []
        );
      } else {
        return [];
      }
    } catch (error) {
      console.error(error);
      return [];
    }
  }, [getSearchValue, option?.data]);

  const getFilteredCount = useCallback((): number => {
    if (option?.group) {
      return (
        (validArray(getGroupFiltered()) ? getGroupFiltered()?.length : 0) ?? 0
      );
    } else {
      return (validArray(getFiltered()) ? getFiltered()?.length : 0) ?? 0;
    }
  }, [getFiltered, getGroupFiltered, option?.group]);

  const getUnselected = useCallback(
    (availableOptions: OptionDataType[] | GroupOptionDataType[]) => {
      try {
        const currentSelected = multiple ? multipleSelected : [selected];
        if (validArray(currentSelected)) {
          if (option?.group) {
            const valuesInSelected = new Set(
              currentSelected.map((item) => item?.value)
            );

            return option?.data?.reduce<GroupOptionDataType[]>(
              (filteredGroups, property) => {
                const filteredFields = property?.data.filter(
                  (opt: OptionDataType) => !valuesInSelected.has(opt?.value)
                );

                if (filteredFields.length > 0) {
                  filteredGroups.push({ ...property, data: filteredFields });
                }

                return filteredGroups;
              },
              []
            );
          } else {
            const valuesInSelected = new Set(
              currentSelected.map((item) => item?.value)
            );
            return (availableOptions as OptionDataType[])?.filter(
              (item) => !valuesInSelected.has(item?.value)
            );
          }
        } else {
          return availableOptions;
        }
      } catch (error) {
        return [];
      }
    },
    [multiple, multipleSelected, option?.data, option?.group, selected]
  );

  const finalData = useCallback(():
    | OptionDataType[]
    | GroupOptionDataType[] => {
    try {
      const filterResult =
        (option?.group ? getGroupFiltered() : getFiltered()) ?? [];
      const existingDat = option?.data ?? [];

      const outputList =
        withInput && inputValue && !selected
          ? filterResult
          : !withInput && optionSearchValue
          ? filterResult
          : existingDat;

      const finalList = option?.removeSelected
        ? getUnselected(outputList)
        : outputList;

      return finalList ?? [];
    } catch (error) {
      console.error(error);
      return [];
    }
  }, [
    getFiltered,
    getGroupFiltered,
    getUnselected,
    inputValue,
    option,
    optionSearchValue,
    selected,
    withInput,
  ]);

  return {
    handleCallback,
    handleSelectInputChange,
    setInitialValue,
    dropdownToggle,
    handleReset,
    handleRemoveOption,
    handleOptionSelect,
    finalData,
    getFilteredCount,
    getSearchValue,
  };
};

export interface HelperExportType {
  handleCallback: (val: SelectedType) => void;
  handleSelectInputChange: (value: string) => void;
  setInitialValue: () => void;
  dropdownToggle: (val: boolean) => void;
  handleReset: () => void;
  handleRemoveOption: (removedItem: OptionDataType) => void;
  handleOptionSelect: (selectedItem: SelectedType) => void;
  finalData: () => OptionDataType[] | GroupOptionDataType[];
  getFilteredCount: () => number;
  getSearchValue: () => string;
}
