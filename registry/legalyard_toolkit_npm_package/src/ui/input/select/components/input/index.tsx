import classNames from "classnames";
import InputBox from "../../../components/input";
import useSelectInput from "../../@context";

const SelectInputBox = () => {
  const {
    state: { inputValue },
    helper: { handleSelectInputChange, handleFocused },
    placeholder,
    disabled,
    inputProps,
  } = useSelectInput();

  return (
    <div
      className={classNames(
        "cursor-auto w-full flex-1",
        disabled && "pointer-events-none bg-grey bg-opacity-10 shadow-inner"
      )}
      aria-label="input wrapper"
    >
      <InputBox
        type="text"
        name="selectInputBox"
        placeholder={placeholder ?? "Select"}
        onChange={(e) => handleSelectInputChange(e?.target?.value)}
        value={inputValue || ""}
        onFocus={() => handleFocused()}
        autoComplete="off"
        className="text-sm capitalize"
        {...(inputProps ?? {})}
      />
    </div>
  );
};

export default SelectInputBox;
