import { validArray } from "../../../../../utils/helpers/data/array";
import classNames from "classnames";
import InputBox from "../../../components/input";
import useTagSelect from "../../@context";
import AddedTag from "./tag";

const MultipleSelectBox = () => {
  const {
    state: { inputValue, multipleSelected, setOpen },
    helper: { handleSelectInputChange, handleFocused },
    option: { hideIcon } = {},
    refs: { inputRef },
    placeholder,
    disabled,
    inputProps,
    withInput,
  } = useTagSelect();

  return (
    <div
      className={classNames(
        "cursor-auto w-full flex-1",
        disabled && "pointer-events-none bg-grey bg-opacity-10 shadow-inner"
      )}
      aria-label="input wrapper"
    >
      <div
        className={classNames(
          "w-full inline-flex items-center flex-wrap gap-1.5"
        )}
      >
        {validArray(multipleSelected) &&
          multipleSelected?.map((tag, index) => {
            return (
              <AddedTag
                key={index + String(tag?.title)}
                tag={tag}
                hideIcon={hideIcon}
              />
            );
          })}

        {withInput ? (
          <InputBox
            ref={inputRef}
            type="text"
            name="selectInputBox"
            placeholder={placeholder ?? "Select"}
            onChange={(e) => handleSelectInputChange(e?.target?.value)}
            value={inputValue || ""}
            onFocus={() => handleFocused()}
            autoComplete="off"
            className={classNames(
              "inline-flex text-sm grow text-ellipsis w-0 min-w-14 max-w-full hover:placeholder-shown:bg-slate-100 focus:!bg-transparent"
            )}
            {...(inputProps ?? {})}
          />
        ) : (
          <div
            role="button"
            className="text-grey text-opacity-70 cursor-pointer bg-transparent p-1.5 rounded-base inline-flex text-sm grow text-ellipsis w-0 min-w-14 max-w-full hover:bg-slate-100"
            onClick={() => setOpen((prev) => !prev)}
          >
            {placeholder ?? "Select"}
          </div>
        )}
      </div>
    </div>
  );
};

export default MultipleSelectBox;
