import styled from "styled-components";
import * as Styles from "../styles";
import { StyledType } from "../types";

export const Container = styled.div`
  font-size: ${({ theme: { font } }) => font.sub};
`;

export const Wrapper = styled(Styles.Wrapper)`
  position: relative;
`;

export const LabelHolder = styled(Styles.LabelHolder)<StyledType>``;

export const Input = styled(Styles.CustomInput)<StyledType>`
  &[type="date"]::-webkit-inner-spin-button,
  &[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
  }
`;

export const ActionIconWrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  row-gap: 1rem;
  padding: 0 0.5rem 0 0.1rem;

  .remove {
    &:hover {
      color: ${({ theme: { color } }) => color.main};
    }
  }

  svg {
    cursor: pointer;
  }
`;
