import classNames from "classnames";
import { ChangeEvent, FC, useEffect, useRef, useState } from "react";
import { ChevronDown, ChevronUp, X } from "react-feather";
import { TimeSelectorTypes } from "./types";

import useOutsideAlert from "../../../utils/hooks/useOutsideAlert";
import usePosition from "../../../utils/hooks/usePosition";
import TimeDropdown from "./dropdown";
import * as Styles from "./styles";

const TimeSelector: FC<TimeSelectorTypes> = ({
  label,
  name,
  onChange,
  value,
  className,
  clear,
  ...rest
}) => {
  const wrapperRef = useRef(null);
  const [timeValue, setTimeValue] = useState<string>(value ?? "");
  const [openTimes, setOpenTimes] = useState<boolean>(false);

  useEffect(() => {
    if (value) {
      setTimeValue(value);
    }
  }, [value]);

  const { toTop } = usePosition(wrapperRef);

  const timeValidation = (itsTime: string) => {
    try {
      const matches = itsTime?.match(/\d+/g);
      if (matches) {
        const [hours, minutes] = matches;
        const amPMMatch = itsTime.match(/AM|PM/);
        const amPM = amPMMatch?.[0];

        const finalHours = Boolean(hours) && hours?.length === 2 ? hours : "10";
        const finalMins =
          Boolean(minutes) && minutes?.length === 2 ? minutes : "00";
        const finalAmPm = Boolean(amPM) ? amPM : "AM";

        return `${finalHours}:${finalMins} ${finalAmPm}`;
      } else {
        return "10:00 AM";
      }
    } catch (error) {
      console.error(error);
      return "10:00 AM";
    }
  };

  const handleCHange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      e?.preventDefault();
      setOpenTimes(false);

      const value = e?.target?.value;

      setTimeValue(value);

      if (onChange) {
        onChange({
          target: { value: value, type: "time", name },
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleBlur = () => {
    try {
      const finalTime = timeValidation(timeValue);
      setTimeValue(finalTime);

      if (onChange) {
        onChange({
          target: { value: finalTime, type: "time", name },
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleSelect = (time: string) => {
    try {
      setTimeValue(time);

      if (onChange) {
        onChange({
          target: { value: time, type: "time", name },
        });
      }
      setOpenTimes(false);
    } catch (error) {
      console.error(error);
    }
  };

  const handleValueRemove = () => {
    try {
      setTimeValue("");
      if (onChange) {
        onChange({
          target: { value: "", type: "time", name },
        });
      }

      setOpenTimes(false);
    } catch (error) {
      console.error(error);
    }
  };

  const timeValueFormate = () => {
    try {
      if (Boolean(timeValue)) {
        const matches = timeValue?.match(/\d+/g);
        if (matches) {
          const [hours, minutes] = matches;
          const amPMMatch = timeValue.match(/AM|PM/);
          const amPM = amPMMatch?.[0];

          return `${hours ?? "10"}:${minutes ?? "00"} ${amPM ?? "AM"}`;
        }
      }
    } catch (error) {
      console.error(error);
      return "";
    }
  };

  useOutsideAlert(wrapperRef, () => setOpenTimes(false));

  return (
    <Styles.Container
      aria-label="Time Selector"
      className={classNames(className)}
    >
      {label && (
        <Styles.LabelHolder $staticLabel={Boolean(label)}>
          {label}
        </Styles.LabelHolder>
      )}

      <Styles.Wrapper
        ref={wrapperRef}
        $valid={Boolean(timeValue)}
        $focused={Boolean(openTimes)}
      >
        <Styles.Input
          type="text"
          name="trip-start"
          value={timeValueFormate()?.toString() ?? ""}
          onChange={(e) => handleCHange(e)}
          onBlur={() => handleBlur()}
          onClick={() => setOpenTimes((prev) => !prev)}
          {...rest}
        />

        <Styles.ActionIconWrapper>
          {Boolean(clear) && Boolean(timeValue) && (
            <X className="remove" onClick={() => handleValueRemove()} />
          )}
          <span
            className="flex items-center justify-center"
            onClick={() => setOpenTimes((prev) => !prev)}
          >
            {openTimes ? <ChevronUp /> : <ChevronDown />}
          </span>
        </Styles.ActionIconWrapper>

        {openTimes && (
          <div
            className={classNames(
              "bg-white absolute shadow-2xl w-full sm:w-[150px] max-w-[150px] px-1 py-2  rounded-base z-50 border border-slate-300",
              "left-0",
              toTop ? "bottom-full" : "top-full"
            )}
          >
            <TimeDropdown handleSelect={handleSelect} />
          </div>
        )}
      </Styles.Wrapper>
    </Styles.Container>
  );
};

export default TimeSelector;
