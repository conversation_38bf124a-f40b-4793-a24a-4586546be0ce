import { validArray } from "../../../../utils/helpers/data/array";
import React from "react";
import { timeData } from "../data";

const TimeDropdown = ({
  handleSelect,
  ...rest
}: {
  handleSelect: (d: string) => void;
}) => {
  const times = timeData();

  return (
    <div
      className="h-40 overflow-y-auto bg-white rounded-base w-full"
      {...rest}
    >
      {validArray(times) &&
        times?.map((item, index) => {
          return (
            <div
              key={item + index}
              className="text-sm px-2 py-1.5 cursor-pointer rounded-base hover:text-white hover:bg-main whitespace-nowrap overflow-hidden text-ellipsis"
              title={item}
              onClick={() => handleSelect(item)}
            >
              {item}
            </div>
          );
        })}
    </div>
  );
};

export default TimeDropdown;
