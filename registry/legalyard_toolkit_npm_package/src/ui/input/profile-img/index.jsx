import { useEffect, useState } from "react";
import { Camera } from "react-feather";
import userPlaceholder from "../../../assets/placeholder/user.svg";
import Tooltip from "../../tooltip";
import * as Styles from "./styles";

const ProfileImageUpload = ({
  name,
  value,
  onChange,
  center,
  note,
  uploadButton,
  ...otherProps
}) => {
  const [img, setImg] = useState(null);
  const [previewImg, setPreviewImg] = useState("");

  useEffect(() => {
    if (typeof value === "object") {
      setImg(value);
    } else if (typeof value === "string") {
      setPreviewImg(value);
    }
  }, [value]);

  const handleImageError = (e) => {
    e.target.src = userPlaceholder;
  };

  const handleChange = (e) => {
    try {
      e.preventDefault();
      e.stopPropagation();
      const file = e?.target?.files[0];
      if (file) {
        if (onChange) {
          onChange({ target: { name: name || "avatar", value: file } });
        }
        setImg(file);
        const imgUrl = window?.URL?.createObjectURL(file);
        setPreviewImg(imgUrl);
      }
    } catch (error) {
      console.error({ error });
    }
  };

  return (
    <Styles.Container
      aria-label="Profile Image Upload"
      center={center}
      {...otherProps}
    >
      <Styles.Wrapper button={Boolean(uploadButton)}>
        <Styles.PreviewWrapper>
          <Styles.PreviewHolder
            src={Boolean(previewImg) ? previewImg : userPlaceholder}
            alt="User Avatar"
            onError={handleImageError}
          />
        </Styles.PreviewWrapper>
        <Styles.ActionWrapper>
          <Styles.UploadWrapper button={Boolean(uploadButton)}>
            <Styles.UploadHolder
              type="file"
              id="profile-image-upload"
              onChange={handleChange}
              accept="image/png, image/jpeg, image/jpg"
            />
            <Tooltip data="Upload" direction="right">
              <Styles.LabelHolder
                id="label-profile-image-upload"
                htmlFor="profile-image-upload"
              >
                <Styles.ActionHolder>
                  {Boolean(uploadButton) ? (
                    Boolean(previewImg) ? (
                      `Change ${uploadButton}`
                    ) : (
                      `upload ${uploadButton}`
                    )
                  ) : (
                    <Camera />
                  )}
                </Styles.ActionHolder>
              </Styles.LabelHolder>
            </Tooltip>
          </Styles.UploadWrapper>
          {note && (
            <Styles.NoteHolder>
              Must be JPEG or PNG and cannot exceed 5MB.
            </Styles.NoteHolder>
          )}
        </Styles.ActionWrapper>
      </Styles.Wrapper>
    </Styles.Container>
  );
};

export default ProfileImageUpload;
