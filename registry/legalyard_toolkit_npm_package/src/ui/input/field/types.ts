import { InputHTMLAttributes, ReactNode } from "react";
import { CountrySelectorType } from "../../country-selector/types";
import { InputLabelType } from "../components/label";

export interface InputAttributeIconTypes {
  show?: boolean;
  src?: string | ReactNode;
  className?: string;
  name?: string;
  tooltip?: {
    show?: boolean;
    name?: string | ReactNode;
    direction?: string;
  };
  onClick?: (d?: any) => void;
}

export interface InputAttributesTypes {
  prefix?: {
    icon: InputAttributeIconTypes;
    className?: string;
  };
  suffix?: {
    icon: InputAttributeIconTypes;
    className?: string;
  };
}

export interface InputFieldTypes extends InputHTMLAttributes<HTMLInputElement> {
  label?: InputLabelType;
  attributes?: InputAttributesTypes;
  passwordViewToggle?: boolean;
  countrySelector?: CountrySelectorType;
  error?: string;
  onFocus?: (d: any) => void;
  onBlur?: (d: any) => void;
  type?: string;
  value?: string;
  className?: string;
}
