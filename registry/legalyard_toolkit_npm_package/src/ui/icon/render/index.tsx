import classNames from "classnames";
import { createElement, isValidElement, ReactNode } from "react";
import Placeholder from "../../../assets/placeholder/image.png";
import ImageRender from "../../image";

const IconRender = ({
  title,
  icon,
  className,
}: {
  title?: string;
  icon: string | ReactNode;
  className?: string;
}) => {
  const renderIcon = (icon: any, props?: any) => {
    try {
      const IconComponent = icon;
      return createElement(IconComponent, props);
    } catch (error) {
      return "";
    }
  };

  if (!icon) return null;

  return (
    <>
      {typeof icon === "string" ? (
        <ImageRender
          src={icon ?? Placeholder}
          alt={title ?? "icon"}
          className={classNames(
            "w-full max-w-max h-auto aspect-square bg-slate-100",
            className
          )}
        />
      ) : isValidElement(icon) ? (
        <>{icon}</>
      ) : (
        <>{renderIcon(icon as ReactNode, { className, title })}</>
      )}
    </>
  );
};

export default IconRender;
