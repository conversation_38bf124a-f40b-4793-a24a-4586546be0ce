import classNames from "classnames";
import { FC } from "react";
import { getRandomColor } from "../../utils/helpers/data/color";
import * as Styles from "./styles";
import { propsType } from "./types";

const Tag: FC<propsType> = ({
  className,
  title,
  icon,
  bg,
  randomBg,
  color,
  ...rest
}) => {
  const localBg = getRandomColor();

  return (
    <Styles.TagsContainer
      className={classNames("rounded-full", className)}
      $randomBg={randomBg}
      $bg={randomBg ? localBg : bg}
      {...rest}
    >
      {icon ? <Styles.IconHolder src={icon} /> : null}
      <Styles.TextHolder
        $color={color}
        $randomBg={randomBg}
        $bg={randomBg ? localBg : bg}
      >
        {title}
      </Styles.TextHolder>
    </Styles.TagsContainer>
  );
};

export default Tag;
