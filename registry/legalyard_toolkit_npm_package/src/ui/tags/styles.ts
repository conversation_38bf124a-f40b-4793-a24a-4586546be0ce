import styled from "styled-components";
import { StyledType } from "./types";

export const TagsContainer = styled.div<StyledType>`
  display: flex;
  align-items: center;
  background-color: ${({ $randomBg, $bg, theme: { color } }) =>
    $randomBg ? `${$bg}20` : $bg ? $bg : color.grey};
  width: fit-content;
  padding: 0.2rem 0.4rem;
  cursor: pointer;

  &:hover {
    background-color: ${({ $bg, theme: { color } }) => ($bg ? $bg : color.sub)};

    span {
      color: ${({ theme: { color } }) => color.white};
    }
  }
`;

export const IconHolder = styled.img`
  width: 10px;
  height: 10px;
  object-fit: contain;
  margin-right: 6px;
`;

export const TextHolder = styled.span<StyledType>`
  font-weight: bold;
  font-size: ${({ theme: { font } }) => font.small};
  color: ${({ $randomBg, $bg, $color, theme: { color } }) =>
    $randomBg ? $bg : $color ? $color : color.white};
  text-transform: capitalize;
`;
