import Button from "../../../button";
import { FC } from "react";
import { actionType } from "../../types";

const Actions: FC<actionType> = ({ handleSetToday, handleClearData }) => {
  return (
    <div className="flex items-center justify-between mt-0">
      <Button
        effect
        variant="action"
        className="flex-1 flex items-center justify-center text-sm hover:text-main py-2 hover:!bg-slate-100"
        onClick={() => handleClearData()}
      >
        Clear
      </Button>
      <span className="border-r border-slate-200 h-5 mx-1" />
      <Button
        effect
        variant="action"
        className="flex-1 flex items-center justify-center text-sm hover:text-main py-2 hover:!bg-slate-100"
        onClick={() => handleSetToday()}
      >
        Today
      </Button>
    </div>
  );
};

export default Actions;
