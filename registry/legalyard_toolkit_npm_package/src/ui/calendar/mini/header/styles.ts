import styled from "styled-components";
import { StyledType } from "../../types";
import Button from "../../../button";

export const Container = styled.div<StyledType>`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  justify-content: space-between;
  column-gap: 1rem;

  height: 32px;
`;

export const TextHolder = styled.span`
  font-weight: 600;
`;

export const ButtonWrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  column-gap: 0.5rem;
`;

export const ButtonHolder = styled(Button)`
  cursor: pointer;
  height: 100%;
  padding: 0.25rem;

  &:hover {
    background-color: ${({ theme: { color } }) => `${color.main}10`};
  }
`;
