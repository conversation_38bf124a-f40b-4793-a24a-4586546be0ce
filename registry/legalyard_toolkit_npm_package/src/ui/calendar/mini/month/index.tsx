import React, { <PERSON> } from "react";
import { monthBoxType } from "../../types";
import { validArray } from "../../../../utils/helpers/data/array";
import { monthData } from "../../../input/date/data";
import { X } from "react-feather";
import Button from "../../../button";
import classNames from "classnames";

const MonthBox: FC<monthBoxType> = ({
  startMonth,
  endMonth,
  date,
  handleSelectMonth,
  setShowMonths,
  setShowYears,
}) => {
  const itsYear = date?.getFullYear();

  const startFrom = startMonth ?? 0;
  const endTo = endMonth ?? monthData?.length;

  const handleYearShow = () => {
    setShowYears(true);
    setShowMonths(false);
  };

  return (
    <div className="bg-white absolute top-0 left-0 w-full h-full border border-slate-200">
      <div className="flex flex-col w-full h-full overflow-y-auto ">
        <div className="sticky top-0 bg-white flex items-center justify-between text-sm border-b border-slate-200 px-1">
          <Button
            effect
            variant="action"
            className="h-8 flex-1 flex items-center justify-center font-semibold"
            onClick={() => handleYearShow()}
          >
            {itsYear}
          </Button>
          <span className="border-r border-slate-200 h-5 mx-1" />
          <Button
            effect
            variant="action"
            className="h-8 flex-1 flex items-center justify-center gap-x-0"
            onClick={() => setShowMonths(false)}
          >
            <X className="w-3 h-3" />
            Close
          </Button>
        </div>
        <div className="flex-1 grid grid-cols-3 w-full h-full">
          {validArray(monthData) &&
            monthData?.slice(startFrom, endTo)?.map((month, index) => {
              const today = new Date();
              const currentMonth = today?.getMonth() as number;

              return (
                <div
                  key={month?.name + 1}
                  className={classNames(
                    "bg-white text-sm hover:bg-slate-100 flex items-center justify-center p-3 shadow cursor-pointer",
                    currentMonth === index && "!bg-slate-100 font-semibold"
                  )}
                  onClick={() => handleSelectMonth(index)}
                >
                  {month?.short}
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default MonthBox;
