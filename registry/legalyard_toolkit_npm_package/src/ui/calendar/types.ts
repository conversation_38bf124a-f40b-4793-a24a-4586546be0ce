import { Dispatch, SetStateAction } from "react";

export interface propsType {
  currentDate?: Date | null;
  getSelected?: (date: Date | null) => void;
  hideYear?: boolean;
  navigation?: boolean;
  startMonth?: number;
  endMonth?: number;
  startYear?: number;
  endYear?: number;
  onClear?: () => void;
  className?: string;
}

export interface headerType {
  hideYear?: boolean;
  navigation?: boolean;
  date: Date;
  setDate: Dispatch<SetStateAction<Date>>;
  setShowMonths: Dispatch<SetStateAction<boolean>>;
  setShowYears: Dispatch<SetStateAction<boolean>>;
}

export interface monthBoxType {
  startMonth?: number;
  endMonth?: number;
  date?: Date;
  setShowMonths: Dispatch<SetStateAction<boolean>>;
  setShowYears: Dispatch<SetStateAction<boolean>>;
  handleSelectMonth: (month: number) => void;
}

export interface yearBoxType {
  startYear?: number;
  endYear?: number;
  date?: Date;
  setShowYears: Dispatch<SetStateAction<boolean>>;
  setShowMonths: Dispatch<SetStateAction<boolean>>;
  handleSelectYear: (year: number) => void;
}

export interface actionType {
  handleSetToday: () => void;
  handleClearData: () => void;
}

export interface styles {
  $active?: boolean;
  $selected?: boolean;
}

export type StyledType = styles;
