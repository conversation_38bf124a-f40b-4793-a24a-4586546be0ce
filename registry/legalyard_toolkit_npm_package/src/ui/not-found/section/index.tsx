import { FC } from "react";
import Image from "../../image";
import { propsType } from "../types";
import classNames from "classnames";

const Section404: FC<propsType> = ({
  icon,
  title,
  text,
  children,
  className,
}) => {
  return (
    <div
      className={classNames(
        "w-full h-full flex items-center justify-center",
        className
      )}
    >
      <div className="flex flex-col items-center justify-center gap-y-4">
        {icon && (
          <Image src={icon} className="max-h-64 w-auto object-contain" />
        )}

        {(title || text) && (
          <div className="flex flex-col items-center justify-center gap-y-2">
            {title && (
              <h4 className="text-xl text-font font-bold tracking-wide">
                {title}
              </h4>
            )}
            {text && <h4>{text}</h4>}
          </div>
        )}

        {children}
      </div>
    </div>
  );
};

export default Section404;
