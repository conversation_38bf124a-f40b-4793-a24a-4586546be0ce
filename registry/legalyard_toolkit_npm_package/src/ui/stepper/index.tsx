import { Fragment, FC } from "react";
import { stepperData } from "./data";
import * as Styles from "./styles";
import { Check } from "react-feather";
import { propsType, dataType } from "./types";
import classNames from "classnames";

const Stepper: FC<propsType> = ({
  data,
  currentActive,
  lastCompleted,
  justify,
  vertical,
  className,
  wrapperClass,
  hideCheck,
  ...otherProps
}) => {
  const listData = data || stepperData;

  return (
    <Styles.Container
      aria-label="Stepper"
      className={classNames("stepper", className)}
      {...otherProps}
    >
      <Styles.StepsWrapper
        $justify={Boolean(justify)}
        $vertical={vertical}
        className={classNames(wrapperClass)}
      >
        {Array.isArray(listData) &&
          listData?.length > 0 &&
          listData?.map((step: dataType, index) => {
            const isActive = currentActive === index;
            const complete =
              (index === listData?.length - 1 && lastCompleted) ||
              index < currentActive;

            const showCheck = !Boolean(hideCheck) && Boolean(complete);

            return (
              <Fragment key={index}>
                <Styles.StepHolder>
                  <Styles.NumberWrapper
                    $active={isActive}
                    $complete={Boolean(showCheck)}
                  >
                    {showCheck ? <Check /> : index + 1}
                  </Styles.NumberWrapper>
                  <Styles.TextWrapper>
                    <Styles.TextHolder>{step?.title}</Styles.TextHolder>
                    {Boolean(step?.sub) && (
                      <Styles.TextHolder $sub className="hidden xs:block">
                        {step?.sub}
                      </Styles.TextHolder>
                    )}
                  </Styles.TextWrapper>
                </Styles.StepHolder>
                {index < listData?.length - 1 && (
                  <Styles.StepDivider $vertical={vertical} />
                )}
              </Fragment>
            );
          })}
      </Styles.StepsWrapper>
    </Styles.Container>
  );
};

export default Stepper;
