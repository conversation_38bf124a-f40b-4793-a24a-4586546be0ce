import { FC } from "react";
import { propsType } from "../types";
import classNames from "classnames";
import * as Styles from "./styles";

const PageHeading: FC<propsType> = ({
  className,
  title,
  sub,
  button,
  handleButton,
}) => {
  const buttonCLick = () => {
    if (button && handleButton) {
      handleButton();
    }
  };
  return (
    <Styles.Container className={classNames(className, "page_heading")}>
      <Styles.TextWrapper>
        <Styles.TextHolder $head>{title}</Styles.TextHolder>
        {sub && <Styles.TextHolder>{sub}</Styles.TextHolder>}
      </Styles.TextWrapper>
      {button && (
        <Styles.Button onClick={() => buttonCLick()}>{button}</Styles.Button>
      )}
    </Styles.Container>
  );
};

export default PageHeading;
