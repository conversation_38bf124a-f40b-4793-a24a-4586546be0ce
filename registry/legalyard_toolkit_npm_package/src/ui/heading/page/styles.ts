import styled from "styled-components";
import { StyledType } from "../types";

export const Container = styled.div<StyledType>`
  ${({ theme: { placeholders } }) => placeholders.flex};
  align-items: flex-start;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 20px;
`;

export const TextWrapper = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
`;

export const TextHolder = styled.p<StyledType>`
  flex: 1;
  font-size: ${({ $head, theme: { font } }) => ($head ? font.big : font.sub)};
  color: ${({ $head, theme: { color } }) => ($head ? color.font : color.grey)};
  font-weight: ${({ $head }) => ($head ? 700 : 400)};
  text-transform: ${({ $head }) => ($head ? "capitalize" : "none")};
`;

export const Button = styled.button`
  ${({ theme: { placeholders } }) => placeholders.button};
`;
