import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const Carousel = ({
  children,
  dots,
  infinite,
  speed,
  slidesToShow,
  slidesToScroll,
  prevRef,
  nextRef,
  responsive,
}) => {
  const NextArrow = (props) => {
    const { onClick } = props;
    return (
      <div ref={nextRef} className="custom-arrow next" onClick={onClick} />
    );
  };

  const PrevArrow = (props) => {
    const { onClick } = props;
    return (
      <div ref={prevRef} className="custom-arrow prev" onClick={onClick} />
    );
  };

  const settings = {
    dots: dots || false,
    infinite: infinite || false,
    speed: speed || 500,
    slidesToShow: slidesToShow || 1,
    slidesToScroll: slidesToScroll || 1,
    prevArrow: <PrevArrow />,
    nextArrow: <NextArrow />,
    responsive: responsive || null,
  };

  return <>{children && <Slider {...settings}>{children}</Slider>}</>;
};

export default Carousel;
