import { useState, useEffect, useRef } from "react";

import { ChevronLeft, ChevronRight } from "react-feather";
import PageDimensions from "../../styles/usePageDimensions";

import * as Styles from "./styles";

const Carousel = ({
  children,
  id,
  gap,
  forceGap,
  row,
  column,
  fullWidth,
  draggable,
  padding,
  dots,
  innerDots,
  arrowButton,
  scrollByItemWidth,
  childWidth,
  autoPlay,
}) => {
  let mouseDown = useRef(false);
  let startX = useRef();

  const [containerWidth, setContainerWidth] = useState(null);
  const [currentItem, setCurrentItem] = useState(1);
  const [currentSlider, setCurrentSlide] = useState(1);
  // const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const [buttonDesable, setButtonDesable] = useState(false);
  const [currentChildWidth, setCurrentChildWidth] = useState("");

  const { width } = PageDimensions();

  const boxWidth = padding ? containerWidth - padding * 2 : containerWidth;
  const gridGap = fullWidth && !row ? 0 : fullWidth && row && row < 2 ? 0 : gap;
  const containerPadding = padding || 0;
  const containerColumns = column > 1 ? column : 2;
  const childrenLength = children.length;

  //use When only 2 slides
  const columnFor2Slides = containerColumns === 2 ? 1 : containerColumns;

  const box = document.getElementById(id || "carousel-container");

  useEffect(() => {
    const childrenElement = document.getElementById(
      id || "carousel-container"
    )?.children;

    const handleChildrenWidth = async () => {
      try {
        await Array.prototype.forEach.call(childrenElement, (child) => {
          setTimeout(() => {
            setCurrentChildWidth(child.offsetWidth);
          }, 100);
        });
      } catch (err) {
        console.error({ err });
      }
    };

    handleChildrenWidth();
  }, [children, id, width]);

  useEffect(() => {
    const sliderWidth = box?.clientWidth;
    setContainerWidth(sliderWidth);

    if (draggable) {
      const startDragging = (e) => {
        mouseDown.current = true;
        startX.current = e.pageX - box?.offsetLeft;
      };

      const moveDragging = (e) => {
        e.preventDefault();
        if (!mouseDown.current) return;
        const x = e.pageX - box?.offsetLeft;
        const scroll = x - startX.current;
        box.scrollLeft = box?.scrollLeft - scroll;
      };

      const stopDragging = (e) => {
        mouseDown.current = false;
        box.style.cursor = "grab";
      };

      const handleDraggable = (e) => {
        box.style.cursor = "grabbing";

        //   let prevX = -1;
        //   e = e || window.event;
        //   var dragX = e.pageX;
        //   if (prevX === -1) {
        //     prevX = e.pageX;
        //     return false;
        //   }
        //   if (prevX < e.pageX) {
        //     box.scrollLeft = box.scrollLeft - dragX;
        //   }
        //   if (prevX > e.pageX) {
        //     box.scrollLeft = box.scrollLeft + dragX;
        //   }
      };
      box?.addEventListener("mousedown", startDragging, false);
      box?.addEventListener("mousemove", moveDragging, false);
      box?.addEventListener("mouseup", stopDragging, false);
      box?.addEventListener("mouseleave", stopDragging, false);
      box?.addEventListener("dragover", handleDraggable, false);
    }
  }, [box, draggable, width]);

  const finalScrollWidth = childWidth
    ? childWidth
    : scrollByItemWidth && !childWidth
    ? currentChildWidth
    : containerWidth;

  const handlePrev = (e) => {
    e = e || window.event;
    e.stopPropagation();

    if (scrollByItemWidth) {
      box.scrollLeft = box?.scrollLeft - finalScrollWidth - gridGap;
    } else {
      box.scrollLeft =
        box?.scrollLeft - finalScrollWidth - gridGap + containerPadding * 2;
    }
    setButtonDesable(true);

    if (scrollByItemWidth) {
      if (currentItem !== 1) {
        setCurrentItem(currentItem - 1);
      }
    } else {
      if (currentSlider !== 1) {
        setCurrentSlide(currentSlider - 1);
      }
    }

    setTimeout(() => {
      setButtonDesable(false);
    }, 500);
  };
  const handleNext = (e) => {
    e = e || window.event;
    e.stopPropagation();

    if (scrollByItemWidth) {
      box.scrollLeft = box?.scrollLeft + finalScrollWidth + gridGap;
    } else {
      box.scrollLeft =
        box?.scrollLeft + finalScrollWidth + gridGap - containerPadding * 2;
    }

    setButtonDesable(true);

    if (scrollByItemWidth) {
      if (currentItem !== childrenLength - columnFor2Slides) {
        setCurrentItem(currentItem + 1);
      }
    } else if (fullWidth) {
      if (currentSlider !== childrenLength) {
        setCurrentSlide(currentSlider + 1);
      }
    } else {
      if (currentSlider !== childrenLength / containerColumns) {
        setCurrentSlide(currentSlider + 1);
      }
    }

    setTimeout(() => {
      setButtonDesable(false);
    }, 500);
  };

  const dotLength = scrollByItemWidth
    ? childrenLength - columnFor2Slides
    : fullWidth
    ? childrenLength
    : childrenLength / containerColumns;

  const dotActive = scrollByItemWidth ? currentItem : currentSlider;

  const leftButtonActive = scrollByItemWidth
    ? currentItem > 1
    : currentSlider > 1;

  const rightButtonActive = scrollByItemWidth
    ? currentItem < childrenLength - columnFor2Slides
    : fullWidth
    ? currentSlider < childrenLength
    : currentSlider < childrenLength / containerColumns;

  return (
    <Styles.Container
      aria-label="Carousel-Start"
      containerWidth={containerWidth}
    >
      <Styles.Wrapper>
        {arrowButton ? (
          <Styles.ButtonContainer aria-label="Button Container">
            <Styles.ButtonWrapper>
              {leftButtonActive ? (
                <Styles.ButtonHolder
                  disabled={buttonDesable}
                  onClick={(e) => handlePrev(e)}
                  left
                >
                  <ChevronLeft />
                </Styles.ButtonHolder>
              ) : null}
              {rightButtonActive ? (
                <Styles.ButtonHolder
                  disabled={buttonDesable}
                  onClick={(e) => handleNext(e)}
                  right
                >
                  <ChevronRight />
                </Styles.ButtonHolder>
              ) : null}
            </Styles.ButtonWrapper>
          </Styles.ButtonContainer>
        ) : null}
        <Styles.CarouselContainer
          id={id || "carousel-container"}
          gap={gridGap || forceGap}
          padding={containerPadding}
          row={row}
          column={containerColumns}
          draggable={true}
          fullWidth={fullWidth}
          containerWidth={boxWidth}
          childWidth={childWidth}
        >
          {children}
        </Styles.CarouselContainer>
      </Styles.Wrapper>
      {innerDots ? (
        <Styles.InnerDotsContainer>
          <Styles.InnerDotsWrapper aria-label="Inner Dots Wrapper">
            {[...Array(+dotLength).keys()].map((item, index) => {
              const isACtive = dotActive === index + 1;
              return <Styles.InnerDotHolder key={index} active={isACtive} />;
            })}
          </Styles.InnerDotsWrapper>
        </Styles.InnerDotsContainer>
      ) : null}
      {dots ? (
        <Styles.DotsWrapper aria-label="Dots Wrapper">
          {[...Array(+dotLength).keys()].map((item, index) => {
            const isACtive = dotActive === index + 1;
            return <Styles.DotHolder key={index} active={isACtive} />;
          })}
        </Styles.DotsWrapper>
      ) : null}
    </Styles.Container>
  );
};

export default Carousel;
