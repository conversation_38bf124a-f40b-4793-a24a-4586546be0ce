import styled from "styled-components";

export const Container = styled.div`
  max-width: 100%;
  position: relative;
`;

export const Wrapper = styled.div`
  position: relative;
`;

export const CarouselContainer = styled.div`
  display: grid;
  grid-auto-flow: column;
  grid-template-rows: ${({ row }) => (row ? `repeat(${row}, 1fr)` : "1fr")};

  /* display: flex;
  align-items: center;
  grid-row: 2; */

  grid-gap: ${({ gap }) => (gap ? `${gap}px` : 0)};

  overflow-x: hidden;
  scroll-behavior: smooth;

  padding: ${({ padding }) => (padding ? `${padding}px` : 0)};

  &:active {
    cursor: grab;
  }

  user-select: none;

  & > * {
    width: ${({ gap, childWidth, column, fullWidth, containerWidth }) =>
      childWidth
        ? `${childWidth}px`
        : fullWidth && !childWidth
        ? `${containerWidth}px`
        : `calc((${containerWidth}px / ${column}) - (${gap}px / ${column}))`};
  }
`;

export const ButtonContainer = styled.div`
  width: 100%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 999;

  /* display: flex;
  align-items: center;
  justify-content: space-between; */
`;

export const ButtonWrapper = styled.div`
  position: relative;
`;

export const ButtonHolder = styled.button`
  position: absolute;
  ${({ right }) => (right ? "right: 0" : "left: 0")};
  top: 0;
  border: 0px solid ${({ theme: { allColors } }) => allColors.grey};
  box-shadow: 0 0 10px ${({ theme: { allColors } }) => `${allColors.grey}50`};
  border-radius: 100px;
  padding: 10px;

  background-color: #fff;
  color: ${({ theme: { allColors } }) => allColors.dark};

  display: flex;
  align-items: center;
  justify-content: center;

  transform: ${({ right }) =>
    right ? "translate(50%, -50%)" : "translate(-50%, -50%)"};
  transition: all 0.2s ease-in-out;

  &:hover {
    color: #fff;
    background-color: ${({ theme: { allColors } }) => allColors.grey};
    transition: all 0.2s ease-in-out;
  }
`;

export const DotsWrapper = styled.div`
  width: 100%;
  height: 20px;

  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;

  margin: 50px 0 0;
`;

export const DotHolder = styled.div`
  display: inline-block;
  background-color: ${({ active, theme: { allColors } }) =>
    active ? allColors.sub : `${allColors.dark}40`};

  height: ${({ active }) => (active ? "12px" : "5px")};
  width: ${({ active }) => (active ? "12px" : "5px")};
  border-radius: 20px;
  margin: 5px;

  transition: all 0.2s ease-in-out;
`;

export const InnerDotsContainer = styled.div`
  width: 100%;
  height: 20px;

  position: absolute;
  bottom: 30px;
`;

export const InnerDotsWrapper = styled.div`
  background-color: ${({ theme: { allColors } }) => `${allColors.white}80`};
  width: fit-content;
  padding: 0px 5px;
  border-radius: 100px;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;

  margin: auto;
`;

export const InnerDotHolder = styled.div`
  display: inline-block;
  background-color: ${({ active, theme: { allColors } }) =>
    active ? allColors.white : `${allColors.dark}40`};

  height: ${({ active }) => (active ? "10px" : "5px")};
  width: ${({ active }) => (active ? "10px" : "5px")};
  border-radius: 20px;
  margin: 5px;

  transition: all 0.2s ease-in-out;
`;