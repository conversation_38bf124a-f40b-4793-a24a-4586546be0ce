export const filterOperators = [
  {
    value: "is",
    title: "is",
  },
  {
    value: "is_not",
    title: "is not",
  },
  {
    value: "contains",
    title: "contains",
  },
  {
    value: "does_not_contain",
    title: "does not contain",
  },
  {
    value: "starts_with",
    title: "starts with",
  },
  {
    value: "ends_with",
    title: "ends with",
  },
  {
    value: "is_empty",
    title: "is empty",
  },
  {
    value: "is_not_empty",
    title: "is not empty",
  },
  {
    value: "is_known",
    title: "is known",
  },
  {
    value: "is_unknown",
    title: "is unknown",
  },
];

export const numberOperators = [
  {
    value: "is",
    title: "is",
  },
  {
    value: "is_not",
    title: "is not",
  },
  {
    value: "is_greater_than",
    title: "is greater than",
  },
  {
    value: "is_greater_than_or_equal_to",
    title: "is greater than or equal to",
  },
  {
    value: "is_less_than",
    title: "is less than",
  },
  {
    value: "is_less_than_or_equal_to",
    title: "is less than or equal to",
  },
  {
    value: "is_between",
    title: "is between",
  },
  {
    value: "is_known",
    title: "is known",
  },
  {
    value: "is_unknown",
    title: "is unknown",
  },
];

export const dateOperators = [
  {
    value: "is",
    title: "is",
  },
  {
    value: "is_not",
    title: "is not",
  },
  {
    value: "is_before",
    title: "is before",
  },
  {
    value: "is_after",
    title: "is after",
  },
  {
    value: "is_between",
    title: "is between",
  },
  {
    value: "is_more_than",
    title: "is more than",
  },
  {
    value: "is_less_than",
    title: "is less than",
  },
  {
    value: "is_known",
    title: "is known",
  },
  {
    value: "is_unknown",
    title: "is unknown",
  },
];
