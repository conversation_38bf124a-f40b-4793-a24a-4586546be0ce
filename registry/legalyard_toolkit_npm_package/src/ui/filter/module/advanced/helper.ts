import { Di<PERSON>atch, RefObject, SetStateAction, useCallback } from "react";
import { FieldType, FilterCondition, FilterGroup } from "./types"; // Adjust the import path
import { v4 } from "uuid";

export const applyFilters = <T>(
  data: T[],
  filterGroups: FilterGroup[]
): T[] => {
  try {
    return data?.filter((item) => {
      return filterGroups?.some((group) =>
        group?.filters.every((condition) => {
          const value = item[condition?.field?.key as keyof T];
          switch (condition?.operator) {
            case "is":
              return value === condition?.value;
            case "is_not":
              return value !== condition?.value;
            case "contains":
              return (
                typeof value === "string" &&
                value.includes(condition?.value as string)
              );
            case "does_not_contain":
              return (
                typeof value === "string" &&
                !value.includes(condition?.value as string)
              );
            case "starts_with":
              return (
                typeof value === "string" &&
                value.startsWith(condition?.value as string)
              );
            case "ends_with":
              return (
                typeof value === "string" &&
                value.endsWith(condition?.value as string)
              );
            case "is_empty":
              return value === "";
            case "is_not_empty":
              return value !== "";
            case "is_known":
              return value !== undefined && value !== null;
            case "is_unknown":
              return value === undefined || value === null;
            default:
              return false;
          }
        })
      );
    });
  } catch (error) {
    console.error(error);
    return data;
  }
};

interface HelperType {
  setLocalFilterGroups: Dispatch<SetStateAction<FilterGroup[]>>;
  setShowProperties: Dispatch<SetStateAction<boolean>>;
  onChange: (filterGroups: FilterGroup[]) => void;
  data: any[];
  localFilterGroups: FilterGroup[];
  groupsWrapperRef: RefObject<HTMLDivElement>;
  currentGroup: number | null | undefined;
  filtersRef: RefObject<Map<string, HTMLDivElement | null>>;
}

interface HelperExportType {
  handleFilterChange: (updatedFilterGroups: FilterGroup[]) => void;
  scrollToBottom: (id: string) => void;
  addFilterToGroup: (groupIndex: number | null, field: FieldType) => void;
  addFilterGroup: (selectedField: FieldType) => void;
  removeFilterCondition: (groupIndex: number, conditionIndex: number) => void;
  removeFilterGroup: (groupIndex: number) => void;
  handleConditionChange: (
    groupIndex: number,
    conditionIndex: number,
    newCondition: FilterCondition
  ) => void;
}

export const useFilterHelper = (args: HelperType): HelperExportType => {
  const handleFilterChange = useCallback(
    (updatedFilterGroups: FilterGroup[]) => {
      try {
        args?.setLocalFilterGroups(updatedFilterGroups);
        if (args?.onChange) {
          args?.onChange(updatedFilterGroups);
        }
      } catch (error) {
        console.error(error);
      }
    },
    [args]
  );

  const scrollToBottom = useCallback(
    (id?: string) => {
      try {
        if (id && args?.filtersRef?.current) {
          const item = args?.filtersRef?.current?.get(id);
          if (item) {
            item.scrollIntoView({ behavior: "auto", block: "center" });
            item.classList.add("shadow-md");
            item.classList.add("shadow-main");

            setTimeout(() => {
              item.classList.remove("shadow-md");
              item.classList.remove("shadow-main");
            }, 1500);
          }
        } else if (args?.groupsWrapperRef?.current) {
          args.groupsWrapperRef.current.scrollTop =
            args?.groupsWrapperRef?.current.scrollHeight * 2;
        }
      } catch (error) {
        console.error(error);
      }
    },
    [args?.filtersRef, args.groupsWrapperRef]
  );

  const addFilterToGroup = useCallback(
    (groupIndex: number | null = 0, field: FieldType) => {
      try {
        if (groupIndex !== null) {
          const unique_id = v4();
          const newCondition: FilterCondition = {
            id: unique_id,
            field,
            operator: "is",
            value: "",
          };
          const newGroups = [...args?.localFilterGroups];
          newGroups[groupIndex].filters.push(newCondition);
          handleFilterChange(newGroups);

          setTimeout(() => {
            scrollToBottom(unique_id);
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    [args?.localFilterGroups, handleFilterChange, scrollToBottom]
  );

  const addFilterGroup = useCallback(
    (selectedField: FieldType) => {
      try {
        if (typeof args?.currentGroup === "number") {
          addFilterToGroup(args?.currentGroup, selectedField);
        } else {
          const unique_id = v4();
          const newGroup: FilterGroup = { filters: [] };
          const newCondition: FilterCondition = {
            id: unique_id,
            field: selectedField,
            operator: "is",
            value: "",
          };
          newGroup.filters.push(newCondition);
          const newGroups = [...args?.localFilterGroups, newGroup];
          handleFilterChange(newGroups);

          setTimeout(() => {
            scrollToBottom(unique_id);
          });
        }
      } catch (error) {
        console.error(error);
      } finally {
        args?.setShowProperties(false);
      }
    },
    [addFilterToGroup, args, handleFilterChange, scrollToBottom]
  );

  const removeFilterCondition = useCallback(
    (groupIndex: number, conditionIndex: number) => {
      try {
        const newGroups = [...args?.localFilterGroups];
        newGroups[groupIndex].filters.splice(conditionIndex, 1);
        handleFilterChange(newGroups);
      } catch (error) {
        console.error(error);
      }
    },
    [args?.localFilterGroups, handleFilterChange]
  );

  const removeFilterGroup = useCallback(
    (groupIndex: number) => {
      try {
        const newGroups = [...args?.localFilterGroups];
        newGroups.splice(groupIndex, 1);
        handleFilterChange(newGroups);
      } catch (error) {
        console.error(error);
      }
    },
    [args?.localFilterGroups, handleFilterChange]
  );

  const handleConditionChange = useCallback(
    (
      groupIndex: number,
      conditionIndex: number,
      newCondition: FilterCondition
    ) => {
      try {
        const newGroups = [...args?.localFilterGroups];
        newGroups[groupIndex].filters[conditionIndex] = newCondition;
        handleFilterChange(newGroups);
      } catch (error) {
        console.error(error);
      }
    },
    [args?.localFilterGroups, handleFilterChange]
  );

  return {
    handleFilterChange,
    scrollToBottom,
    addFilterToGroup,
    addFilterGroup,
    removeFilterCondition,
    removeFilterGroup,
    handleConditionChange,
  };
};
