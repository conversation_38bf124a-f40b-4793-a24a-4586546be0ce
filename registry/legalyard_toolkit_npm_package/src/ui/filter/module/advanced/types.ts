import { FilterOperator } from "../../types";

export interface FieldType {
  _id?: string;
  id?: string;
  key: string;
  name: string;
  icon?: string;
  operators?: FilterOperator[];
  inputType?: "date" | "select" | "multiselect" | "boolean" | "number";
  options?: {
    value: string;
    title: string;
    [key: string]: any;
  }[];
}

export interface FilterCondition {
  id: string;
  field: FieldType;
  operator: FilterOperator;
  value?: string | number | boolean | null;
}

export interface FilterGroup {
  filters: FilterCondition[];
}

export interface PropertiesType {
  title?: string;
  fields: FieldType[];
}

export interface AdvanceFilterType {
  data: any[];
  Properties?: PropertiesType[];
  onChange: (filterGroups: FilterGroup[]) => void;
  onFilter?: (filterGroups: FilterGroup[], data: any[]) => void;
  className?: string;
  onClose?: () => void;
}
