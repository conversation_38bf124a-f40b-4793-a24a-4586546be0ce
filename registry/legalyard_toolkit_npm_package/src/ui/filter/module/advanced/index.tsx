import Loader from "../../../loader";
import { validArray } from "../../../../utils/helpers/data/array";
import classNames from "classnames";
import {
  forwardRef,
  Fragment,
  lazy,
  Ref,
  Suspense,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Plus, X } from "react-feather";
import Actions from "./components/actions";
import { applyFilters, useFilterHelper } from "./helper";
import { FilterGroup, AdvanceFilterType } from "./types";
import { DataFilterReferenceType } from "../../types";

const Filter = lazy(() => import("./components/filter"));
const Group = lazy(() => import("./components/group"));
const Properties = lazy(() => import("./components/properties"));

// TODO
// Add field specific input type, Date, Select, multiselect, boolean, number

const AdvancedFilter = forwardRef(
  (
    { data, onChange, onFilter, className, onClose }: AdvanceFilterType,
    ref: Ref<DataFilterReferenceType>
  ) => {
    const groupsWrapperRef = useRef<HTMLDivElement>(null);
    const filtersRef = useRef<Map<string, HTMLDivElement | null>>(new Map());

    const [localFilterGroups, setLocalFilterGroups] = useState<FilterGroup[]>(
      []
    );
    const [showProperties, setShowProperties] = useState<boolean>(false);
    const [currentGroup, setCurrentGroup] = useState<number | null | undefined>(
      null
    );

    const {
      addFilterGroup,
      removeFilterCondition,
      removeFilterGroup,
      handleConditionChange,
    } = useFilterHelper({
      setLocalFilterGroups,
      setShowProperties,
      onChange,
      data,
      localFilterGroups,
      groupsWrapperRef,
      currentGroup,
      filtersRef,
    });

    const InitiateAddFilter = (
      val?: boolean,
      groupIndex?: number | null | undefined
    ) => {
      setShowProperties((prev) => val ?? !prev);
      setCurrentGroup(groupIndex);
    };

    const handleCallback = (newData: any[]) => {
      if (onFilter) {
        onFilter(localFilterGroups, newData);
      }
    };

    const applyFiltersNow = () => {
      const newFilteredData = applyFilters(data, localFilterGroups);
      handleCallback(newFilteredData);
    };

    const handleClose = () => {
      if (onClose) {
        onClose();
      }
    };

    useImperativeHandle(ref, () => ({
      applyFilter: applyFiltersNow,
    }));

    return (
      <div
        className={classNames(
          "filter w-full h-full flex flex-col items-start min-w-[35vw] overflow-hidden",
          className
        )}
      >
        <div className="relative w-full z-10 shadow-md bg-gradient-to-r from-blue to-sky-600 text-white padding px-6 py-4 flex items-center justify-between xs:justify-start flex-row flex-wrap gap-4">
          <div className="flex items-center justify-start gap-2">
            <button
              className="block xs:hidden text-sb font-medium border border-red bg-red hover:bg-red hover:bg-opacity-95 text-white p-1.5 rounded-base transition-all outline-0"
              onClick={() => handleClose()}
            >
              <X />
            </button>
            <h2 className="text-base xs:text-xl font-semibold">
              Advanced FIlters
            </h2>
          </div>

          <button className="justify-self-end text-slate-500 text-sb font-medium border border-slate-400 bg-slate-100 hover:border-red hover:bg-red hover:bg-opacity-95 hover:text-white px-3 py-1.5 rounded-base transition-all outline-0">
            Discard
          </button>

          <button
            className="hidden md:block text-sb font-medium border border-red bg-red hover:bg-red hover:bg-opacity-95 text-white p-1 rounded-base transition-all outline-0 absolute right-4"
            onClick={() => handleClose()}
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        <div className="w-full flex-1 relative overflow-hidden">
          <div className="w-full h-full flex items-start flex-row gap-0">
            <div
              ref={groupsWrapperRef}
              className="w-full h-full bg-white border-r border-sky-300 overflow-auto"
            >
              <div className="flex flex-col gap-1 p-3">
                {validArray(localFilterGroups) &&
                  localFilterGroups.map((group, groupIndex) => {
                    return (
                      <Fragment key={groupIndex}>
                        <Suspense fallback={<Loader big center />}>
                          <Group
                            index={groupIndex}
                            removeGroup={() => removeFilterGroup(groupIndex)}
                          >
                            <div className="flex flex-col gap-3 mb-4">
                              {group.filters.map(
                                (condition, conditionIndex) => (
                                  <Filter
                                    ref={(el) =>
                                      el &&
                                      filtersRef.current.set(condition?.id, el)
                                    }
                                    id={condition?.id}
                                    key={conditionIndex}
                                    index={conditionIndex}
                                    condition={condition}
                                    groupIndex={groupIndex}
                                    removeFilter={removeFilterCondition}
                                    handleChange={handleConditionChange}
                                  />
                                )
                              )}
                            </div>

                            <button
                              onClick={() =>
                                InitiateAddFilter(true, groupIndex)
                              }
                              className="border border-blue px-2 py-1 mb-2 outline-main rounded-base text-sm flex items-center gap-1 hover:bg-blue hover:bg-opacity-10"
                            >
                              <Plus /> Add Filter
                            </button>
                          </Group>
                        </Suspense>
                      </Fragment>
                    );
                  })}
              </div>
              <Actions
                localFilterGroups={localFilterGroups}
                applyFiltersNow={applyFiltersNow}
                InitiateAddFilter={() => InitiateAddFilter()}
              />
            </div>
            <div
              className={classNames(
                "w-full h-full absolute top-0 left-0 bottom-0 bg-white transition-[transform] z-[5]",
                showProperties ? "translate-x-0" : "translate-x-full"
              )}
            >
              <Suspense fallback={<Loader big center />}>
                <Properties
                  onClose={(d) => InitiateAddFilter(d)}
                  onSelect={(d) => addFilterGroup(d)}
                />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export default AdvancedFilter;
