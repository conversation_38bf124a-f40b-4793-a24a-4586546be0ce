export interface DataFilterReferenceType {
  applyFilter: () => void;
}

export type FilterOperator =
  | "is"
  | "is_not"
  | "contains"
  | "does_not_contain"
  | "starts_with"
  | "ends_with"
  | "is_empty"
  | "is_not_empty"
  | "is_known"
  | "is_unknown";

export type NumberFilterOperator =
  | "is"
  | "is_not"
  | "is_greater_than"
  | "is_greater_than_or_equal_to"
  | "is_less_than"
  | "is_less_than_or_equal_to"
  | "is_between"
  | "is_known"
  | "is_unknown";

export * from "./module/advanced/types";
export * from "./module/fields/types";
