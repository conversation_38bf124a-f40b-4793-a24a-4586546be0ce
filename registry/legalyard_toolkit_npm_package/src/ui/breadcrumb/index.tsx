import classNames from "classnames";
import { FC, Fragment } from "react";
import { ChevronRight, Home, Info } from "react-feather";
import { validArray } from "../../utils/helpers/data/array";
import { toValidPath } from "../../utils/helpers/data/url";
import * as Styles from "./styles";
import { propsType } from "./types";

const Breadcrumb: FC<propsType> = ({
  data,
  main,
  intro,
  dashboard,
  last,
  basePath,
  urlParameter,
  className,
  onNavigate,
}) => {
  // available props
  // dashboard, basePath, main, last, urlParameter

  const handleNavigate = (url: string) => {
    try {
      if (<PERSON><PERSON><PERSON>(basePath) && Boolean(url)) {
        const finalPath = toValidPath(`${basePath}/${url}`);

        if (onNavigate) {
          onNavigate(finalPath ?? "");
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getNavigate = (url: string, options?: any) => {
    try {
      if (onNavigate) {
        onNavigate(url ?? "", options);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const introHas = typeof intro === "object";
  const mainPath = main?.path ?? basePath ?? "";
  const introPath = introHas && intro?.path ? intro?.path : `${mainPath}/intro`;

  return (
    <div
      className={classNames(
        "flex items-center flex-wrap gap-2 text-sm",
        className
      )}
    >
      {dashboard && (
        <Styles.ItemWrapper
          onClick={() => getNavigate("/")}
          $nav={Boolean(basePath)}
        >
          <Home />
        </Styles.ItemWrapper>
      )}

      {Boolean(intro) && (
        <>
          {dashboard && <ChevronRight />}
          <Styles.ItemWrapper
            onClick={() =>
              getNavigate(introPath, { state: { fromMain: true } })
            }
            $nav={Boolean(basePath)}
          >
            {introHas && intro?.icon ? intro?.icon : <Info />}
            <Styles.TextHolder className="mt-[0.04rem]">
              {introHas && Boolean(intro?.name) ? intro?.name : "Intro"}
            </Styles.TextHolder>
          </Styles.ItemWrapper>
        </>
      )}

      {main && (
        <>
          {(intro || dashboard) && <ChevronRight />}
          <Styles.ItemWrapper
            onClick={() => getNavigate(mainPath)}
            $nav={Boolean(basePath)}
          >
            {main?.icon && main?.icon}
            {main?.name && (
              <Styles.TextHolder className="mt-[0.04rem]">
                {main?.name}
              </Styles.TextHolder>
            )}
          </Styles.ItemWrapper>
        </>
      )}

      {validArray(data) && (
        <>
          {data?.map((item: any, index: number) => {
            return (
              <Fragment
                key={index + (item?.[urlParameter!] || item?.name || "")}
              >
                <ChevronRight />
                <Styles.ItemWrapper
                  onClick={() => handleNavigate(item?.[urlParameter!])}
                  $nav={Boolean(basePath)}
                >
                  <Styles.TextHolder>{item?.name}</Styles.TextHolder>
                </Styles.ItemWrapper>
              </Fragment>
            );
          })}
        </>
      )}

      {last && (
        <>
          <ChevronRight />
          <Styles.ItemWrapper $nav={Boolean(basePath)}>
            <Styles.TextHolder>{last?.name}</Styles.TextHolder>
          </Styles.ItemWrapper>
        </>
      )}
    </div>
  );
};

export default Breadcrumb;
