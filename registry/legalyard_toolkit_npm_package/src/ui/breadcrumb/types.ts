import { ReactNode } from "react";

export interface propsType {
  data?: any;
  main?: {
    name?: string;
    icon?: ReactNode;
    path?: string;
  };
  intro?:
    | boolean
    | {
        name?: string;
        icon?: ReactNode;
        path?: string;
      };
  last?: {
    name?: string;
    icon?: ReactNode;
    path?: string;
  };
  basePath?: string;
  urlParameter?: string;
  dashboard?: boolean;
  className?: string;
  onNavigate?: (path?: string, options?: any) => void;
}

export interface styles {
  $nav?: any;
}

export type StyledType = styles;
