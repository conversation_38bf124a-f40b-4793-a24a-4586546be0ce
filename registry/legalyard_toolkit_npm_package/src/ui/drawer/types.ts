import { ReactNode } from "react";

export type AllowPosition = "top" | "right" | "bottom";

export interface DrawerType {
  id?: string;
  children?: ReactNode;
  position?: AllowPosition;
  open?: boolean;
  close?: boolean;
  outsideClose?: boolean;
  onClose?: (d?: any) => void;
  className?: string;
  backdropClassName?: string;
  hideBackdrop?: boolean;
  contentClassName?: string;
}

export interface styles {
  open?: boolean;
}

export type StyledType = DrawerType & styles;
