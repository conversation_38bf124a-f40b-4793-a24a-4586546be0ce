import { AllowPosition } from "./types";

export const openerPosition = (pos?: AllowPosition): string => {
  const defaultPosition = "left-0 top-0 bottom-0";
  try {
    const topPosition = "left-0 top-0 right-0";
    const rightPosition = "top-0 right-0 bottom-0";
    const bottomPosition = "left-0 right-0 bottom-0";

    let finalPosition = "";

    if (!Boolean(pos)) {
      finalPosition = defaultPosition;
    }

    if (pos === "top") {
      finalPosition = finalPosition.concat(" ", topPosition);
    }

    if (pos === "right") {
      finalPosition = finalPosition.concat(" ", rightPosition);
    }

    if (pos === "bottom") {
      finalPosition = finalPosition.concat(" ", bottomPosition);
    }
    return finalPosition;
  } catch (error) {
    console.error(error);
    return defaultPosition;
  }
};

export const openClass = (pos?: AllowPosition, display?: boolean): string => {
  const defaultPosition = display ? "translate-x-0" : "-translate-x-full";
  try {
    const topPosition = display ? "translate-y-0" : "-translate-y-full";
    const rightPosition = display ? "translate-x-0" : "translate-x-full";
    const bottomPosition = display ? "translate-y-0" : "translate-y-full";

    let finalPosition = "";

    if (!Boolean(pos)) {
      finalPosition = defaultPosition;
    }

    if (pos === "top") {
      finalPosition = finalPosition.concat(" ", topPosition);
    }

    if (pos === "right") {
      finalPosition = finalPosition.concat(" ", rightPosition);
    }

    if (pos === "bottom") {
      finalPosition = finalPosition.concat(" ", bottomPosition);
    }
    return finalPosition;
  } catch (error) {
    console.error(error);
    return defaultPosition;
  }
};

export const contentClass = (pos?: AllowPosition): string => {
  const defaultPosition =
    "h-screen w-screen xs:w-max xs:min-w-56 xs:max-w-max left-0 me-auto";
  try {
    const topPosition = "w-screen min-h-56 max-h-max top-0 mb-auto";
    const rightPosition =
      "h-screen w-screen xs:w-max xs:min-w-56 xs:max-w-max right-0 ms-auto";
    const bottomPosition = "w-screen min-h-56 max-h-max bottom-0 mt-auto";

    let finalPosition = "";

    if (!Boolean(pos)) {
      finalPosition = defaultPosition;
    }

    if (pos === "top") {
      finalPosition = finalPosition.concat(" ", topPosition);
    }

    if (pos === "right") {
      finalPosition = finalPosition.concat(" ", rightPosition);
    }

    if (pos === "bottom") {
      finalPosition = finalPosition.concat(" ", bottomPosition);
    }
    return finalPosition;
  } catch (error) {
    console.error(error);
    return defaultPosition;
  }
};

export const closeClass = (pos?: AllowPosition): string => {
  const defaultPosition = "top-2.5 right-2.5";
  try {
    const topPosition = "bottom-2.5 right-2.5";
    const rightPosition = "top-2.5 left-2.5";

    let finalPosition = "";

    if (!Boolean(pos)) {
      finalPosition = defaultPosition;
    }

    if (pos === "top") {
      finalPosition = finalPosition.concat(" ", topPosition);
    }

    if (pos === "right") {
      finalPosition = finalPosition.concat(" ", rightPosition);
    }

    if (pos === "bottom") {
      finalPosition = finalPosition.concat(" ", defaultPosition);
    }
    return finalPosition;
  } catch (error) {
    console.error(error);
    return defaultPosition;
  }
};
