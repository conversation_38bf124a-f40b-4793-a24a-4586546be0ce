import classNames from "classnames";
import {
  Dispatch,
  FC,
  SetStateAction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { TabsDataType, TabsType } from "../../types";

interface PropsType extends TabsType {
  activeTabIndex: number | null;
  setActiveTabIndex: Dispatch<SetStateAction<number | null>>;
  data: TabsDataType[];
}
const TabsMenu: FC<PropsType> = ({
  activeTabIndex,
  data,
  setActiveTabIndex,
  ghostTheme,
  variant,
  onChange,
  tabClassName,
}) => {
  const tabsRef = useRef<(HTMLElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [tabUnderlineWidth, setTabUnderlineWidth] = useState(0);
  const [tabUnderlineLeft, setTabUnderlineLeft] = useState(0);

  const updateTabPosition = useCallback(() => {
    if (activeTabIndex === null) return;

    const currentTab = tabsRef.current[activeTabIndex] as HTMLElement;
    if (currentTab) {
      setTabUnderlineLeft(currentTab.offsetLeft);
      setTabUnderlineWidth(currentTab.clientWidth);
    }
  }, [activeTabIndex]);

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      updateTabPosition();
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [updateTabPosition]);

  useEffect(() => {
    const handleResize = () => {
      updateTabPosition();
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [updateTabPosition]);

  useEffect(() => {
    updateTabPosition();
  }, [updateTabPosition]);

  const handleChange = (index: number) => {
    if (onChange) {
      onChange(index);
    }
    setActiveTabIndex(index);
  };

  return (
    <div
      ref={containerRef}
      className={classNames(
        "tabs-menu relative flex flex-row mx-auto h-input rounded-base shadow bg-zinc-200 px-0.5 backdrop-blur-sm"
      )}
    >
      <span
        className={classNames(
          "tabs-slider absolute -z-10 flex overflow-hidden rounded-base py-0.5 transition-all",
          {
            "bottom-0 top-0 h-full": !variant || variant === "normal",
            "bottom-0 h-max": variant === "underline",
          }
        )}
        style={{ left: tabUnderlineLeft, width: tabUnderlineWidth }}
      >
        <span
          className={classNames("tabs-slider-track w-full rounded-base", {
            "bg-main": !ghostTheme,
            "bg-white": (!variant || variant === "normal") && ghostTheme,
            "h-full": !variant || variant === "normal",
            "h-0.5": variant === "underline",
            "bg-font": variant === "underline" && ghostTheme,
          })}
        />
      </span>
      {Array.isArray(data) &&
        data?.length > 0 &&
        data.map((tab, index) => {
          const isActive = activeTabIndex === index;

          return (
            <button
              key={index}
              ref={(el) => (tabsRef.current[index] = el)}
              className={classNames(
                "tab-button my-auto cursor-pointer select-none rounded-base px-4 text-sm text-center font-bold outline-0 transition-all",
                {
                  active: isActive,
                  "text-zinc-500": !isActive,
                  "text-main":
                    variant === "underline" && isActive && !ghostTheme,
                  "text-white":
                    (!variant || variant === "normal") &&
                    isActive &&
                    !ghostTheme,
                  "hover:text-main": !isActive && !ghostTheme,
                  "text-font": isActive && ghostTheme,
                  "hover:text-zinc-600": !isActive && ghostTheme,
                },
                tabClassName
              )}
              onClick={() => handleChange(index)}
            >
              {tab.name}
            </button>
          );
        })}
    </div>
  );
};

export default TabsMenu;
