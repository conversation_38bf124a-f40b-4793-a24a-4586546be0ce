import { ReactNode } from "react";

export interface propsType {
  id?: string;
  children?: ReactNode;
  data?: string | ReactNode;
  direction?: string;
  delay?: any;
  onHold?: any;
  hide?: any;
  padding?: any;
  bg?: any;
  bold?: any;
  capitalize?: any;
  className?: string;
}

export interface styles extends React.InputHTMLAttributes<HTMLInputElement> {
  $bg?: string;
  $padding?: string;
  $sidebar?: boolean;
  $bold?: boolean;
  $capitalize?: boolean;
}

export type StyledType = styles;
