import { InputHTMLAttributes } from "react";

export interface CountrySelectorOnchange {
  target: {
    value: string;
    type: string;
    name: string;
  };
}

export interface CountrySelectorType
  extends Omit<InputHTMLAttributes<HTMLSelectElement>, "value"> {
  onChange?: (d: any) => void;
  value?: string;
  defaultValue?: string;
}

export interface styles {
  $open?: boolean;
  $selected?: boolean;
  $bold?: boolean;
  $openAbove?: boolean;
}

export type StyledType = styles;
