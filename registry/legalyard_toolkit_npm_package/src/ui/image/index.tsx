import classNames from "classnames";
import {
  forwardRef,
  ForwardRefRenderFunction,
  Ref,
  SyntheticEvent,
  useCallback,
  useState,
} from "react";
import Placeholder from "../../assets/placeholder/image.png";
import Loader from "../loader";
import { propsType } from "./types";

const ImageRender: ForwardRefRenderFunction<HTMLImageElement, propsType> = (
  { className, src, alt, onError, onLoad },
  ref: Ref<HTMLImageElement>
) => {
  const [loading, setLoading] = useState<boolean>(true);

  const handleError = useCallback(
    (e: SyntheticEvent<HTMLImageElement, Event>) => {
      try {
        if (onError) {
          onError(e);
        } else {
          const imgElement = e.target as HTMLImageElement;
          imgElement.src = Placeholder;
        }
        setLoading(false);
      } catch (err) {
        console.error(err);
        setLoading(false);
      }
    },
    [onError]
  );

  const handleLoading = (val: boolean) => {
    setLoading(val);
    onLoad(val);
  };

  if (loading) {
    return (
      <div className="h-full w-full flex ite justify-center">
        <Loader variant="skeleton" count={1} height={"100%"} center />
      </div>
    );
  }

  return (
    <img
      ref={ref}
      className={classNames("image", className)}
      src={src ?? ""}
      alt={alt ?? "figure"}
      onError={handleError}
      onLoad={() => handleLoading(false)}
    />
  );
};

export default forwardRef(ImageRender);
