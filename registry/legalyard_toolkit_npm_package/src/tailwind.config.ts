import type { Config } from "tailwindcss";
import plugin from "tailwindcss/plugin";
import { CSSRuleObject } from "tailwindcss/types/config";
import { styleConstants, styleMediaQuery, styleVariables } from "./styles";

const config: Config = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx,mdx}",
    "./dist/**/*.{js,jsx,ts,tsx}",
    "./**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      screens: {
        xs: styleMediaQuery?.screenConstant?.mobileWidth,
      },
      colors: {
        ...styleVariables?.color,
      },
      height: {
        header: `${styleConstants?.headerHeight}`,
        footer: `${styleConstants?.headerHeight}`,
        page: `calc(100vh - ${styleConstants?.headerFooterHeight})`,
        input: styleConstants?.inputHeight,
      },
      borderRadius: {
        base: styleVariables?.element.radius,
      },
      fontSize: {
        xs: styleVariables?.font.extraSmall,
        sm: styleVariables?.font.small,
        sb: styleVariables?.font.sub,
      },
      spacing: {
        regular: "1rem",
      },
      padding: {
        page: "1.25rem",
      },
      keyframes: {
        shimmer: {
          "100%": { transform: "translateX(100%)" },
        },
      },
      animation: {
        "bounce-short": "bounce 0.75s ease-in-out 12",
        shimmer: "shimmer 2s linear infinite",
      },
    },
  },
  plugins: [
    plugin(function ({ addUtilities, theme }) {
      const newUtilities: { [key: string]: CSSRuleObject } = {
        ".fold": {
          position: "relative",
          clipPath: `polygon(0 0, calc(100% - 2.1rem) 0, 100% 2.1rem, 100% 100%, 0 100%)`,
          transition: "all 150ms ease-in-out",
        },
        ".fold::before": {
          content: '""',
          position: "absolute",
          top: "0",
          right: "0",
          borderWidth: "0 2rem 2rem 0",
          borderStyle: "solid",
          borderColor: "transparent transparent #E27735 #E27735",
          borderRadius: "0.05rem",
          background: "transparent",
          transition: "all 150ms ease-in-out",
          boxShadow:
            "0 1px 1px rgba(0, 0, 0, 0.3), -1px 1px 1px rgba(0, 0, 0, 0.2)",
          display: "block",
          width: "0",
        },
        ".fold-none": {
          clipPath:
            "polygon(0 0, calc(100% - 0rem) 0, 100% 0rem, 100% 100%, 0 100%)",
        },
        ".fold-none::before": {
          content: '""',
          borderWidth: "0 0 0 0",
        },
        ".fold-size-xs": {
          clipPath: `polygon(0 0, calc(100% - 0.76rem) 0, 100% 0.76rem, 100% 100%, 0 100%)`,
        },
        ".fold-size-xs::before": {
          borderWidth: "0 0.75rem 0.75rem 0",
        },
        ".fold-size-sm": {
          clipPath: `polygon(0 0, calc(100% - 1.1rem) 0, 100% 1.1rem, 100% 100%, 0 100%)`,
        },
        ".fold-size-sm::before": {
          borderWidth: "0 1rem 1rem 0",
        },
        ".fold-size-lg": {
          clipPath: `polygon(0 0, calc(100% - 3.6rem) 0, 100% 3.6rem, 100% 100%, 0 100%)`,
        },
        ".fold-size-lg::before": {
          borderWidth: "0 3.5rem 3.5rem 0",
        },
        ".box": {
          padding: "1rem",
          "@apply rounded-lg w-full h-full bg-white shadow": {},
        },
      };

      const colors = theme("colors");
      if (colors) {
        Object.keys(colors).forEach((color) => {
          const colorClass = `.fold-bg-${color}::before`;
          newUtilities[colorClass] = {
            borderColor: `transparent transparent ${
              colors[color][500] ?? colors[color]
            } ${colors[color][500] ?? colors[color]}`,
          };
        });
      }

      addUtilities(newUtilities);
    }),
  ],
  variants: {
    extend: {
      fold: ["responsive", "hover"],
    },
  },
};
export default config;
