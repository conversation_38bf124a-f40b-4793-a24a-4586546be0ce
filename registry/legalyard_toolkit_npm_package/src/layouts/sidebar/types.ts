import { Dispatch, ReactNode, SetStateAction } from "react";

export interface MenuItemType {
  name?: string;
  path?: string;
  icon?: string | ReactNode;
  externalUrl?: boolean;
  openInNewTab?: boolean;
}

export interface MenuDataEntryType {
  title?: string;
  menu?: MenuItemType[];
}

export interface MenuDataType {
  allow?: string[];
  data?: MenuDataEntryType[];
}

export interface DashboardSidebarType {
  data: MenuDataType[];
  pathname?: string;
  onNavigate?: (path?: string, options?: any) => void;
  setSidebarOpen: Dispatch<SetStateAction<boolean>>;
  sidebarOpen: boolean;
}
