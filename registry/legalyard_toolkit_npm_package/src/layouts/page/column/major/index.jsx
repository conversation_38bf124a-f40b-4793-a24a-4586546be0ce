import React from "react";

import PageDimensions from "../../../../styles/usePageDimensions";

import * as Styles from "./styles";

const MajorSection = ({ children, margin, bigMargin }) => {
  const { mobile } = PageDimensions();

  return (
    <Styles.Container
      aria-label="Major Section"
      margin={margin}
      bigMargin={bigMargin}
      isMobile={mobile}
    >
      {children}
    </Styles.Container>
  );
};

export default MajorSection;
