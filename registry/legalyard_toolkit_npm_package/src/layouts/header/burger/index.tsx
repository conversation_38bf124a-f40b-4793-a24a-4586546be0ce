import { Dispatch, SetStateAction } from "react";

import { Menu, X } from "react-feather";

const BurgerIcon = ({
  setSidebarOpen,
  sidebarOpen,
}: {
  setSidebarOpen: Dispatch<SetStateAction<boolean>>;
  sidebarOpen: boolean;
}) => {
  const handleToggle = () => {
    setSidebarOpen((prev) => !prev);
  };

  return (
    <button className="block md:hidden" onClick={() => handleToggle()}>
      {sidebarOpen ? <X /> : <Menu />}
    </button>
  );
};

export default BurgerIcon;
