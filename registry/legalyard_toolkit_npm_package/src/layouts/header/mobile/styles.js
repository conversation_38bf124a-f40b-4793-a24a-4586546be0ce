import styled from "styled-components";

export const Container = styled.div`
  ${({ theme: { placeholders } }) => placeholders.flex};
  align-items: flex-start;
  flex-direction: column;
  gap: 30px;
  background-color: ${({ theme: { color } }) => color.white};
  padding: 20px;
  width: 100%;
  min-width: 50vw;
  height: 100%;
  overflow: auto;

  ${({ theme: { media } }) => `
    ${media.mobile_land}{
      width: 100vw;
    }
  `}
`;
