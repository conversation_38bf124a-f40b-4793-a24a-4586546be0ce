import { Dispatch, SetStateAction } from "react";
import Logo from "../../ui/logo";
import { LinkType } from "../../utils/hooks/Link";
import PageDimensions from "../../utils/hooks/usePageDimensions";
import AppContainer from "../app-container";
import ActionArea from "./actions";
import BurgerIcon from "./burger";
import * as Data from "./data";
// import MobileMenu from "./mobile";
import classNames from "classnames";
import AllSearch from "./search";

const AppHeader = ({
  LinkComponent,
  pathname,
  sidebarOpen,
  setSidebarOpen,
}: {
  LinkComponent: LinkType;
  pathname?: string;
  sidebarOpen: boolean;
  setSidebarOpen: Dispatch<SetStateAction<boolean>>;
}) => {
  const hideHeader =
    (pathname && Data?.hideOnPaths?.includes(pathname)) ||
    Data?.hideOnPaths?.some((item) => pathname?.indexOf(item) !== -1);

  const { pageWidth } = PageDimensions();

  if (hideHeader) {
    return null;
  }

  return (
    <div
      id="global-header"
      aria-label="App Header"
      className={classNames(
        "global-header fixed top-0 z-[112] w-full h-header max-w-[100vw] bg-sub text-white select-none",
        hideHeader ? "none" : "flex"
      )}
    >
      <AppContainer fluid>
        <div
          aria-label="App Header wrapper"
          className="relative flex items-center w-full max-h-full"
        >
          <div className="flex items-center justify-center h-full max-w-56">
            <div
              aria-label="branding"
              className={classNames(
                "branding relative w-full h-full max-w-10 max-h-8 pl-0 pe-4 flex items-center justify-center",
                "after:absolute after:top-1/2 after:-translate-y-1/2 after:-right-[1px] after:w-[1px] after:h-2/3 after:bottom-2 after:block after:bg-gray after:bg-opacity-50"
              )}
            >
              <Logo iconOnly LinkComponent={LinkComponent} />
            </div>
          </div>
          <AllSearch />
          <div
            aria-label="Actions"
            className="flex items-center justify-end gap-5 h-full"
          >
            <ActionArea />
            <BurgerIcon
              setSidebarOpen={setSidebarOpen}
              sidebarOpen={sidebarOpen}
            />
          </div>
        </div>
      </AppContainer>
      {pageWidth?.mobile_down && <span>Menu</span>}
    </div>
  );
};

export default AppHeader;
