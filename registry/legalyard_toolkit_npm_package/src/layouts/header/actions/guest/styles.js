import styled, { css } from "styled-components";

export const Container = styled.div`
  padding-left: 20px;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20px;
`;

const Menu = css`
  a {
    padding: 10px 0;
  }
  color: ${({ theme: { color } }) => color.grey};
  font-weight: 600;
  text-transform: capitalize;

  ${({ theme: { placeholders } }) => placeholders.bottomBorder};
`;

export const ItemHolder = styled.div`
  cursor: pointer;
  ${({ theme: { placeholders } }) => placeholders.flexCenter};
  justify-content: center;
  gap: 5px;
  ${({ button, theme: { placeholders } }) =>
    button ? placeholders.button : Menu};

  ${({ button }) => button && `min-width: 146px`};

  svg {
    width: 18px;
    height: 18px;
  }
`;

export const TextHolder = styled.span`
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
  white-space: nowrap;
`;
