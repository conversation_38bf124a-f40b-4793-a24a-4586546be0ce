import { User } from "react-feather";
import PageDimensions from "../../../../styles/usePageDimensions";

import * as Styles from "./styles";

const GuestAction = ({ pathname }) => {
  const isPartner = pathname?.includes("/partner");

  const { pageWidth } = PageDimensions();

  return (
    <Styles.Container>
      {pageWidth?.sm_mobile_up && (
        <Styles.ItemHolder>
          <User />
          <Styles.TextHolder>
            <a href="/auth">Log in</a>
          </Styles.TextHolder>
        </Styles.ItemHolder>
      )}
      {pageWidth?.desktop_up && (
        <>
          {isPartner ? (
            <Styles.ItemHolder button onClick={() => {}} as="button">
              <Styles.TextHolder>Sign Up Now</Styles.TextHolder>
            </Styles.ItemHolder>
          ) : (
            <Styles.ItemHolder button onClick={() => {}} as="button">
              <Styles.TextHolder>Partner with us</Styles.TextHolder>
            </Styles.ItemHolder>
          )}
        </>
      )}
    </Styles.Container>
  );
};

export default GuestAction;
