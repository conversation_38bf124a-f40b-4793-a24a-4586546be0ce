import classNames from "classnames";
import { useState } from "react";
import SearchDropdown from "./dropdown";
import SearchInput from "./input";

const AllSearch = () => {
  const [focused, setFocused] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState("");

  return (
    <div className="global-header-search ms-4 flex-1 relative h-header">
      <div
        className={classNames(
          "hidden md:flex md:max-w-[40vw] p-0.5 h-header [&_.input-wrapper]:font-medium [&_.input-wrapper]:!border-none"
        )}
      >
        <SearchInput
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          setFocused={setFocused}
        />
      </div>

      {focused && <SearchDropdown />}
    </div>
  );
};

export default AllSearch;
