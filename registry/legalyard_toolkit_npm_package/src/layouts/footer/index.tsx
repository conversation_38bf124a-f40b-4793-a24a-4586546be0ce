import { Suspense, lazy } from "react";
import Loader from "../../ui/loader";
import AppContainer from "../app-container";
import { validArray } from "../../utils/helpers/data/array";
import * as Data from "./data";
import * as Styles from "./styles";
import classNames from "classnames";
import { footerHeight } from "../../styles/constants";

const CopyrightFooter = lazy(() => import("./copyright"));

const Footer = ({ pathname }: { pathname?: string }) => {
  const hideFooter = () => {
    try {
      if (validArray(Data?.hideOnPaths) && pathname) {
        return Data?.hideOnPaths?.includes(pathname);
      } else return false;
    } catch (err) {
      return false;
    }
  };

  if (hideFooter()) {
    return null;
  }

  return (
    <footer
      id="global-footer"
      aria-label="Footer"
      className={classNames(
        "fixed bottom-0 z-50 max-h-[31px] w-full bg-dark",
        `h-[${footerHeight}]`
      )}
    >
      <AppContainer fluid>
        <Suspense
          fallback={
            <Loader
              count={5}
              containerStyle={{ padding: "50px" }}
              variant="skeleton"
            />
          }
        >
          <CopyrightFooter />
        </Suspense>
      </AppContainer>
    </footer>
  );
};

export default Footer;
