import { createGlobalStyle } from "styled-components";
import { colorHSL } from "./variables";

export const GlobalStyle = createGlobalStyle`

@layer base {
  :root {
    --main: ${colorHSL.main};
    --sub: ${colorHSL.sub};
    --orange: ${colorHSL.orange};
    --blue: ${colorHSL.blue};
    --skyBlue: ${colorHSL.skyBlue};
    --dark: ${colorHSL.dark};
    --red: ${colorHSL.red};
    --green: ${colorHSL.green};
    --yellow: ${colorHSL.yellow};
    --black: ${colorHSL.black};
    --white: ${colorHSL.white};
    --gray: ${colorHSL.gray};
    --grey: ${colorHSL.grey};
    --page: ${colorHSL.page};
    --font: ${colorHSL.font};
  }
}

  html,
  body,
  div,
  span,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  a,
  em,
  img,
  strong,
  form,
  table,
  section {
    margin: 0;
    padding: 0;
    font-size: 100%;
    vertical-align: baseline;
    box-sizing: border-box;
    line-height: 1;
  }

  html, body {
    width: 100%;
  }

  section,
  footer,
  header,
  menu,
  nav {
    display: block;
  }

  input{
    box-sizing: border-box;
  }

  body {
    font-size: ${({ theme: { fontSize } }) => fontSize?.main};
    line-height: 1.15;
    -webkit-text-size-adjust: none;
    vertical-align: baseline;
    font-family: 'Roboto', sans-serif !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-y: scroll;
    box-sizing: border-box;
    background-color: ${({ theme: { color } }) => color?.page};
    color: ${({ theme: { color } }) => color?.font};

    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }

  code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
  }

  a, button {
    cursor: pointer;
  }

  a:link {
    text-decoration: none !important;
  }

  a{
    font-size: inherit;
    text-decoration: none;
    color: inherit;
  }
  a:hover{
    color: inherit
  }

  svg{
    width: 16px;
    height: 16px;
  }
`;
