{"compilerOptions": {"declaration": true, "declarationMap": false, "sourceMap": false, "inlineSourceMap": false, "outDir": "./dist", "emitDeclarationOnly": false, "rootDir": "./src", "module": "ESNext", "moduleResolution": "node", "target": "ESNext", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowJs": true, "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*", "**/*.css", "src/declarations.d.ts", "src/tailwind.config.ts"], "exclude": ["node_modules", "dist"]}