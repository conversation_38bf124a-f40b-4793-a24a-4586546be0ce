const fs = require("fs");
const path = require("path");

// Utility function to create a directory if it doesn't exist
const ensureDirectoryExists = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
};

// Utility function to create export statements for a given directory
const createExportStatements = (dirPath) => {
  const files = fs.readdirSync(dirPath);
  let exportStatements = [];

  files.forEach((file) => {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);

    if (stats.isFile() && (file.endsWith(".ts") || file.endsWith(".tsx"))) {
      const relativePath = `./${path
        .relative(dirPath, filePath)
        .replace(/\.tsx?$/, "")}`;
      const exportName = path.basename(file, path.extname(file));
      if (exportName !== "index") {
        // Avoid exporting index file itself
        exportStatements.push(
          `export { default as ${exportName} } from '${relativePath}';`
        );
      }
    }
  });

  return exportStatements;
};

// Function to generate index.ts for UI folder
const generateUIIndex = (uiPath) => {
  console.log(`Processing UI folder: ${uiPath}`);

  if (fs.existsSync(uiPath)) {
    const files = fs.readdirSync(uiPath);
    const directories = files.filter((file) =>
      fs.statSync(path.join(uiPath, file)).isDirectory()
    );
    let exportStatements = [];

    directories.forEach((dir) => {
      const subDirPath = path.join(uiPath, dir);
      generateUIIndex(subDirPath); // Recursively handle subdirectories

      const subDirExportStatements = createExportStatements(subDirPath);
      if (subDirExportStatements.length > 0) {
        const indexFilePath = path.join(subDirPath, "index.tsx");
        if (!fs.existsSync(indexFilePath)) {
          fs.writeFileSync(
            indexFilePath,
            subDirExportStatements.join("\n"),
            "utf-8"
          );
          console.log(`Generated ${indexFilePath}`);
        }
      }
    });

    // Create index.ts in the UI folder
    const uiExportStatements = directories.map(
      (dir) => `export * from './${dir}';`
    );
    const uiIndexPath = path.join(uiPath, "index.ts");
    if (!fs.existsSync(uiIndexPath)) {
      fs.writeFileSync(uiIndexPath, uiExportStatements.join("\n"), "utf-8");
      console.log(`Generated ${uiIndexPath}`);
    }
  } else {
    console.error(`UI folder does not exist: ${uiPath}`);
  }
};

// Function to recursively process folders and generate index.ts for utils
const processUtilsFolder = (dirPath) => {
  console.log(`Processing Utils folder: ${dirPath}`);

  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      const subDirPath = filePath;
      processUtilsFolder(subDirPath); // Recursively handle subdirectories

      const subDirExportStatements = createExportStatements(subDirPath);

      // Create index.ts in each sub-directory if not already present
      const indexFilePath = path.join(subDirPath, "index.ts");
      if (!fs.existsSync(indexFilePath)) {
        if (subDirExportStatements.length > 0) {
          fs.writeFileSync(
            indexFilePath,
            subDirExportStatements.join("\n"),
            "utf-8"
          );
          console.log(`Generated ${indexFilePath}`);
        }
      }
    } else if (dirPath.includes("utils")) {
      // Handle files in root of utils directory
      const parentDir = path.dirname(dirPath);
      const parentExportStatements = createExportStatements(
        path.dirname(dirPath)
      );
      const parentIndexFilePath = path.join(parentDir, "index.ts");
      if (!fs.existsSync(parentIndexFilePath)) {
        if (parentExportStatements.length > 0) {
          fs.writeFileSync(
            parentIndexFilePath,
            parentExportStatements.join("\n"),
            "utf-8"
          );
          console.log(`Generated ${parentIndexFilePath}`);
        }
      }
    }
  });
};

// Main function to generate indexes for both UI and utils folders
const main = () => {
  const srcDir = path.resolve(__dirname, "src");
  const uiPath = path.join(srcDir, "ui");
  const utilsPath = path.join(srcDir, "utils");

  generateUIIndex(uiPath);
  processUtilsFolder(utilsPath);
};

main();
