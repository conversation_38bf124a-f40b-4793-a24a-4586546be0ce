image: node:20.11.1

stages:
  - build
  - deploy

before_script:
  - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${NPM_PACKAGE_TOKEN}" > .npmrc
  - npm install

build:
  stage: build
  script:
    - npm run build
  artifacts:
    paths:
      - dist/
      - types/
    expire_in: 1 hour
  only:
    - main

deploy:
  stage: deploy
  script:
    - npm publish
  environment: production
  only:
    - main
